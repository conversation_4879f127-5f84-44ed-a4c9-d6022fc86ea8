# 3D Digital Human System - PowerShell Startup Script
# Enterprise Edition v2.1
# Auto-compile backend and start frontend services

Write-Host "===========================================" -ForegroundColor Cyan
Write-Host "    3D Digital Human System - Startup     " -ForegroundColor Cyan  
Write-Host "===========================================" -ForegroundColor Cyan
Write-Host ""

# Function to check if command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

# Check if backend server exists
if (-not (Test-Path "digital_human_server.exe")) {
    Write-Host "[WARNING] Backend server not compiled, compiling now..." -ForegroundColor Yellow
    
    # Check for build script
    if (Test-Path "build-server.bat") {
        Write-Host "[INFO] Running build script..." -ForegroundColor Green
        & cmd.exe /c "build-server.bat"
        if ($LASTEXITCODE -ne 0) {
            Write-Host "[ERROR] Server compilation failed, please check environment" -ForegroundColor Red
            Write-Host ""
            Write-Host "Required tools:" -ForegroundColor Yellow
            Write-Host "  - GCC compiler (MinGW or MSYS2)" -ForegroundColor White
            Write-Host "  - libwebsockets library" -ForegroundColor White
            Write-Host "  - json-c library" -ForegroundColor White
            Write-Host ""
            Write-Host "Install options:" -ForegroundColor Yellow
            Write-Host "  1. MSYS2: https://www.msys2.org/" -ForegroundColor White
            Write-Host "  2. MinGW-w64: https://www.mingw-w64.org/" -ForegroundColor White
            Write-Host "  3. vcpkg: vcpkg install libwebsockets json-c" -ForegroundColor White
            pause
            exit 1
        }
    } else {
        Write-Host "[INFO] Creating build script..." -ForegroundColor Green
        
        # Create build script
        $buildScript = @"
@echo off
REM 3D Digital Human WebSocket Server Build Script
echo ===========================================
echo 3D Digital Human WebSocket Server Builder
echo ===========================================
echo.

REM Check for GCC compiler
gcc --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] GCC compiler not found
    echo.
    echo Please install one of the following:
    echo 1. MSYS2: https://www.msys2.org/
    echo 2. MinGW-w64: https://www.mingw-w64.org/
    echo 3. TDM-GCC: https://jmeubank.github.io/tdm-gcc/
    echo.
    echo After installation, add to PATH and restart terminal
    pause
    exit /b 1
)

echo [INFO] GCC compiler found
echo [INFO] Compiling WebSocket server...

REM Compile server with required libraries
gcc -o digital_human_server.exe server.c -lws2_32 -lwebsockets -ljson-c -pthread

if %errorlevel% equ 0 (
    echo [SUCCESS] Server compiled successfully
    echo [INFO] Executable: digital_human_server.exe
) else (
    echo [ERROR] Compilation failed
    echo.
    echo Possible solutions:
    echo 1. Install libwebsockets: pacman -S mingw-w64-x86_64-libwebsockets
    echo 2. Install json-c: pacman -S mingw-w64-x86_64-json-c
    echo 3. Use vcpkg: vcpkg install libwebsockets json-c
    echo.
    pause
    exit /b 1
)

echo.
echo Build completed successfully!
pause
"@
        
        $buildScript | Out-File -FilePath "build-server.bat" -Encoding ASCII
        
        # Run the build script
        & cmd.exe /c "build-server.bat"
        if ($LASTEXITCODE -ne 0) {
            Write-Host "[ERROR] Compilation failed, check build requirements" -ForegroundColor Red
            pause
            exit 1
        }
    }
}

# Check for 3D models
if (-not (Test-Path "models\RiggedSimple.glb")) {
    Write-Host "[INFO] 3D model missing, checking download script..." -ForegroundColor Yellow
    
    if (Test-Path "download-models.py") {
        if (Test-Command python) {
            Write-Host "[INFO] Downloading 3D models..." -ForegroundColor Green
            python download-models.py --all
            if ($LASTEXITCODE -ne 0) {
                Write-Host "[WARNING] Model download failed, will use online models" -ForegroundColor Yellow
            }
        } else {
            Write-Host "[WARNING] Python not found, cannot download models" -ForegroundColor Yellow
            Write-Host "[INFO] System will use online model fallback" -ForegroundColor White
        }
    } else {
        Write-Host "[WARNING] No model download script found" -ForegroundColor Yellow
        Write-Host "[INFO] Creating models directory..." -ForegroundColor White
        New-Item -ItemType Directory -Force -Path "models" | Out-Null
    }
}

Write-Host "[INFO] Starting system components..." -ForegroundColor Green
Write-Host ""

# Start backend WebSocket server
Write-Host "[1/2] Starting WebSocket server (port 8080)..." -ForegroundColor Cyan
if (Test-Path "digital_human_server.exe") {
    Start-Process -FilePath "cmd.exe" -ArgumentList "/k", "digital_human_server.exe 8080" -WindowStyle Normal
    Write-Host "[SUCCESS] Backend server started" -ForegroundColor Green
} else {
    Write-Host "[ERROR] Backend server executable not found" -ForegroundColor Red
    Write-Host "[INFO] Starting in frontend-only mode" -ForegroundColor Yellow
}

# Wait for server startup
Write-Host "[WAIT] Server initializing..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

# Start frontend HTTP server
Write-Host "[2/2] Starting frontend server (port 8000)..." -ForegroundColor Cyan

# Try Python first
if (Test-Command python) {
    Write-Host "[INFO] Using Python HTTP server..." -ForegroundColor Green
    Start-Process -FilePath "cmd.exe" -ArgumentList "/k", "python -m http.server 8000" -WindowStyle Normal
    $frontendStarted = $true
} elseif (Test-Command node) {
    Write-Host "[INFO] Using Node.js HTTP server..." -ForegroundColor Green
    Start-Process -FilePath "cmd.exe" -ArgumentList "/k", "npx http-server -p 8000" -WindowStyle Normal
    $frontendStarted = $true
} elseif (Test-Command php) {
    Write-Host "[INFO] Using PHP built-in server..." -ForegroundColor Green
    Start-Process -FilePath "cmd.exe" -ArgumentList "/k", "php -S localhost:8000" -WindowStyle Normal
    $frontendStarted = $true
} else {
    Write-Host "[WARNING] No HTTP server found (Python, Node.js, or PHP)" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Please install one of the following:" -ForegroundColor White
    Write-Host "  1. Python: python -m http.server 8000" -ForegroundColor Gray
    Write-Host "  2. Node.js: npx http-server -p 8000" -ForegroundColor Gray
    Write-Host "  3. PHP: php -S localhost:8000" -ForegroundColor Gray
    Write-Host "  4. Open index-enterprise.html directly in browser" -ForegroundColor Gray
    $frontendStarted = $false
}

# Wait for frontend startup
if ($frontendStarted) {
    Write-Host "[WAIT] Frontend server initializing..." -ForegroundColor Yellow
    Start-Sleep -Seconds 5
}

Write-Host ""
Write-Host "===========================================" -ForegroundColor Cyan
Write-Host "           SYSTEM STARTUP COMPLETE        " -ForegroundColor Cyan
Write-Host "===========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "📌 Access URLs:" -ForegroundColor White
if ($frontendStarted) {
    Write-Host "   Enterprise Interface: http://localhost:8000/index-enterprise.html" -ForegroundColor Green
    Write-Host "   (Note: Standard interface removed, focus on enterprise features)" -ForegroundColor Gray
} else {
    Write-Host "   Direct File: Open index-enterprise.html in browser" -ForegroundColor Yellow
}
Write-Host ""

Write-Host "🔧 System Components:" -ForegroundColor White
Write-Host "   WebSocket Server: ws://localhost:8080" -ForegroundColor Green
if ($frontendStarted) {
    Write-Host "   Frontend Server: http://localhost:8000" -ForegroundColor Green
} else {
    Write-Host "   Frontend Server: Manual setup required" -ForegroundColor Yellow
}
Write-Host ""

Write-Host "📖 Usage Instructions:" -ForegroundColor White
Write-Host "   1. Open browser and visit the URL above" -ForegroundColor Gray
Write-Host "   2. Wait for 3D model loading completion" -ForegroundColor Gray
Write-Host "   3. Select digital human model in sidebar" -ForegroundColor Gray
Write-Host "   4. Start voice or text interaction" -ForegroundColor Gray
Write-Host ""

Write-Host "⚠️  Important Notes:" -ForegroundColor Yellow
Write-Host "   - First use requires 3D model download" -ForegroundColor Gray
Write-Host "   - Recommended browsers: Chrome or Edge" -ForegroundColor Gray
Write-Host "   - Voice features require microphone permission" -ForegroundColor Gray
Write-Host ""

Write-Host "🎭 NEW v2.1 Features:" -ForegroundColor Magenta
Write-Host "   ✅ Advanced Facial Expression System (FACS)" -ForegroundColor Green
Write-Host "   ✅ Company Voice System Adapter" -ForegroundColor Green
Write-Host "   ✅ Enterprise Professional Mode" -ForegroundColor Green
Write-Host "   ✅ Real-time Expression Response (<150ms)" -ForegroundColor Green
Write-Host "   ✅ AI-driven Emotion Analysis" -ForegroundColor Green
Write-Host ""

# Ask if user wants to open browser immediately
$choice = Read-Host "Open enterprise interface in browser now? (Y/n)"
if ($choice -ne "n" -and $choice -ne "N") {
    Write-Host "[INFO] Opening browser..." -ForegroundColor Green
    Write-Host "[NEW] Advanced facial expression system integrated" -ForegroundColor Magenta
    Write-Host "[NEW] Company voice system adapter configured" -ForegroundColor Magenta
    Start-Sleep -Seconds 2
    
    if ($frontendStarted) {
        Start-Process "http://localhost:8000/index-enterprise.html"
    } else {
        $htmlPath = Get-Location | Join-Path -ChildPath "index-enterprise.html"
        if (Test-Path $htmlPath) {
            Start-Process $htmlPath
        } else {
            Write-Host "[ERROR] index-enterprise.html not found in current directory" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "✅ System fully operational! Press any key to close this window." -ForegroundColor Green
Write-Host "   To stop system, close the server windows." -ForegroundColor Gray
Read-Host "Press Enter to continue"