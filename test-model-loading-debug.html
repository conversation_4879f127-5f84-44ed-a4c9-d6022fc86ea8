<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模型加载测试 - 调试版</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .warning { background-color: #fff3cd; color: #856404; }
        #debug-container {
            width: 600px;
            height: 400px;
            border: 2px solid #333;
            margin: 20px 0;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:hover { background: #0056b3; }
        .model-info {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 3D模型加载调试工具</h1>
        
        <div class="test-section">
            <h3>1. 环境检测</h3>
            <div id="environment-check"></div>
        </div>
        
        <div class="test-section">
            <h3>2. 文件路径检测</h3>
            <div id="path-check"></div>
            <button onclick="testAllPaths()">测试所有模型路径</button>
        </div>
        
        <div class="test-section">
            <h3>3. Three.js加载测试</h3>
            <div id="threejs-check"></div>
            <div id="debug-container"></div>
            <button onclick="testThreeJS()">测试Three.js基础功能</button>
            <button onclick="loadBusinessFemale()">加载商务女性模型</button>
            <button onclick="loadSimpleModel()">加载简化模型</button>
        </div>
        
        <div class="test-section">
            <h3>4. 详细日志</h3>
            <div id="debug-log" class="model-info"></div>
        </div>
    </div>

    <!-- Three.js和GLTFLoader -->
    <script src="./libs/three.min.js"></script>
    <script src="./libs/GLTFLoader.js"></script>
    
    <script>
        let scene, camera, renderer, container;
        let debugLog = [];
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            debugLog.push(`[${timestamp}] ${type.toUpperCase()}: ${message}`);
            updateDebugLog();
            console.log(`[模型调试] ${message}`);
        }
        
        function updateDebugLog() {
            document.getElementById('debug-log').innerHTML = debugLog.slice(-20).join('\n');
        }
        
        function showStatus(elementId, message, type) {
            document.getElementById(elementId).innerHTML += 
                `<div class="status ${type}">${message}</div>`;
        }
        
        // 1. 环境检测
        function checkEnvironment() {
            log('开始环境检测...');
            
            // WebGL检测
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (gl) {
                showStatus('environment-check', '✅ WebGL支持正常', 'success');
                log('WebGL支持检测通过');
            } else {
                showStatus('environment-check', '❌ WebGL不支持', 'error');
                log('WebGL支持检测失败', 'error');
            }
            
            // Three.js检测
            if (typeof THREE !== 'undefined') {
                showStatus('environment-check', `✅ Three.js已加载 (版本: ${THREE.REVISION})`, 'success');
                log(`Three.js版本: ${THREE.REVISION}`);
            } else {
                showStatus('environment-check', '❌ Three.js未加载', 'error');
                log('Three.js未加载', 'error');
            }
            
            // GLTFLoader检测
            if (typeof THREE.GLTFLoader !== 'undefined') {
                showStatus('environment-check', '✅ GLTFLoader已加载', 'success');
                log('GLTFLoader已加载');
            } else {
                showStatus('environment-check', '❌ GLTFLoader未加载', 'error');
                log('GLTFLoader未加载', 'error');
            }
        }
        
        // 2. 路径检测
        async function testAllPaths() {
            log('开始路径检测...');
            
            const paths = [
                './models/business_female.glb',
                './models/business_male.glb',
                './models/RiggedSimple.glb',
                './models/RiggedFigure.glb',
                './models/RiggedSimple_updated.glb'
            ];
            
            for (const path of paths) {
                try {
                    const response = await fetch(path, { method: 'HEAD' });
                    if (response.ok) {
                        showStatus('path-check', `✅ ${path} - 可访问 (${response.status})`, 'success');
                        log(`路径可访问: ${path}`);
                    } else {
                        showStatus('path-check', `❌ ${path} - HTTP错误 (${response.status})`, 'error');
                        log(`路径HTTP错误: ${path} - ${response.status}`, 'error');
                    }
                } catch (error) {
                    showStatus('path-check', `❌ ${path} - 网络错误: ${error.message}`, 'error');
                    log(`路径网络错误: ${path} - ${error.message}`, 'error');
                }
            }
        }
        
        // 3. Three.js基础测试
        function testThreeJS() {
            log('开始Three.js基础测试...');
            
            try {
                container = document.getElementById('debug-container');
                
                // 创建场景
                scene = new THREE.Scene();
                scene.background = new THREE.Color(0x87CEEB);
                log('Scene创建成功');
                
                // 创建相机
                camera = new THREE.PerspectiveCamera(75, container.clientWidth / container.clientHeight, 0.1, 1000);
                camera.position.set(0, 1, 3);
                log('Camera创建成功');
                
                // 创建渲染器
                renderer = new THREE.WebGLRenderer({ antialias: true });
                renderer.setSize(container.clientWidth, container.clientHeight);
                renderer.setPixelRatio(window.devicePixelRatio);
                renderer.shadowMap.enabled = true;
                renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                container.appendChild(renderer.domElement);
                log('Renderer创建成功');
                
                // 添加光照
                const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
                scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
                directionalLight.position.set(10, 10, 5);
                directionalLight.castShadow = true;
                scene.add(directionalLight);
                log('光照设置完成');
                
                // 添加测试立方体
                const geometry = new THREE.BoxGeometry();
                const material = new THREE.MeshStandardMaterial({ color: 0x00ff00 });
                const cube = new THREE.Mesh(geometry, material);
                cube.position.set(0, 0, 0);
                scene.add(cube);
                log('测试立方体已添加');
                
                // 渲染循环
                function animate() {
                    requestAnimationFrame(animate);
                    cube.rotation.x += 0.01;
                    cube.rotation.y += 0.01;
                    renderer.render(scene, camera);
                }
                animate();
                
                showStatus('threejs-check', '✅ Three.js基础功能正常', 'success');
                log('Three.js基础测试完成');
                
            } catch (error) {
                showStatus('threejs-check', `❌ Three.js测试失败: ${error.message}`, 'error');
                log(`Three.js测试失败: ${error.message}`, 'error');
            }
        }
        
        // 4. 加载商务女性模型
        function loadBusinessFemale() {
            log('开始加载商务女性模型...');
            loadModel('./models/business_female.glb', '商务女性模型');
        }
        
        // 5. 加载简化模型
        function loadSimpleModel() {
            log('开始加载简化模型...');
            loadModel('./models/RiggedSimple.glb', '简化模型');
        }
        
        // 通用模型加载函数
        function loadModel(path, name) {
            if (!renderer) {
                showStatus('threejs-check', '⚠️ 请先测试Three.js基础功能', 'warning');
                return;
            }
            
            const loader = new THREE.GLTFLoader();
            
            log(`开始加载${name}: ${path}`);
            
            loader.load(
                path,
                function(gltf) {
                    log(`${name}加载成功`);
                    
                    // 清除之前的模型
                    const existingModel = scene.getObjectByName('digital-human-model');
                    if (existingModel) {
                        scene.remove(existingModel);
                    }
                    
                    const model = gltf.scene;
                    model.name = 'digital-human-model';
                    
                    // 调整模型位置和大小
                    const box = new THREE.Box3().setFromObject(model);
                    const center = box.getCenter(new THREE.Vector3());
                    const size = box.getSize(new THREE.Vector3());
                    
                    model.position.sub(center);
                    model.position.y = -size.y / 2;
                    
                    const maxSize = Math.max(size.x, size.y, size.z);
                    const scale = 2 / maxSize;
                    model.scale.setScalar(scale);
                    
                    scene.add(model);
                    
                    log(`${name}已添加到场景中`);
                    log(`模型信息: ${gltf.scene.children.length}个子对象`);
                    
                    // 检查动画
                    if (gltf.animations && gltf.animations.length > 0) {
                        log(`发现${gltf.animations.length}个动画`);
                        gltf.animations.forEach((clip, index) => {
                            log(`动画${index}: ${clip.name} (时长: ${clip.duration.toFixed(2)}s)`);
                        });
                    } else {
                        log('未发现动画数据');
                    }
                    
                    showStatus('threejs-check', `✅ ${name}加载成功`, 'success');
                },
                function(progress) {
                    const percent = Math.round((progress.loaded / progress.total) * 100);
                    log(`${name}加载进度: ${percent}%`);
                    
                    if (percent % 25 === 0) { // 每25%显示一次
                        showStatus('threejs-check', `⏳ ${name}加载中: ${percent}%`, 'info');
                    }
                },
                function(error) {
                    log(`${name}加载失败: ${error.message}`, 'error');
                    showStatus('threejs-check', `❌ ${name}加载失败: ${error.message}`, 'error');
                    console.error('模型加载错误详情:', error);
                }
            );
        }
        
        // 页面加载完成后自动检测环境
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(checkEnvironment, 100);
        });
        
        // 窗口大小调整
        window.addEventListener('resize', function() {
            if (camera && renderer && container) {
                camera.aspect = container.clientWidth / container.clientHeight;
                camera.updateProjectionMatrix();
                renderer.setSize(container.clientWidth, container.clientHeight);
            }
        });
    </script>
</body>
</html>