#!/usr/bin/env python3
"""
下载真正的GLB格式女性客服模型
解决模型文件格式错误问题
"""

import os
import requests
import sys
import time

def download_file(url, filepath, description):
    """下载文件并显示进度"""
    print(f"🚀 开始下载: {description}")
    print(f"📥 URL: {url}")
    print(f"💾 保存到: {filepath}")
    
    try:
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        downloaded_size = 0
        
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
                    downloaded_size += len(chunk)
                    
                    if total_size > 0:
                        progress = (downloaded_size / total_size) * 100
                        print(f"\r📊 下载进度: {progress:.1f}%", end="", flush=True)
        
        print(f"\n✅ 下载完成: {os.path.basename(filepath)}")
        print(f"📦 文件大小: {downloaded_size / (1024*1024):.1f} MB")
        return True
        
    except Exception as e:
        print(f"\n❌ 下载失败: {e}")
        return False

def verify_glb_file(filepath):
    """验证GLB文件格式"""
    try:
        with open(filepath, 'rb') as f:
            # 读取前4个字节，应该是 'glTF'
            header = f.read(4)
            if header == b'glTF':
                print(f"✅ {os.path.basename(filepath)} 是有效的GLB文件")
                return True
            else:
                print(f"❌ {os.path.basename(filepath)} 不是有效的GLB文件")
                print(f"   文件头: {header}")
                return False
    except Exception as e:
        print(f"❌ 验证文件失败: {e}")
        return False

def main():
    print("🎯 企业级女性客服GLB模型下载器")
    print("=" * 50)
    
    # 确保models目录存在
    models_dir = "models"
    if not os.path.exists(models_dir):
        os.makedirs(models_dir)
    
    # 企业级专业女性数字人模型源
    model_urls = {
        "realistic_female_1": {
            "url": "https://models.readyplayer.me/64b5c9f7c7d4b39f9c5b8a2e.glb",
            "filename": "business_female_professional.glb",
            "description": "专业女性客服数字人 (Ready Player Me)"
        },
        "realistic_female_2": {
            "url": "https://cdn.jsdelivr.net/gh/mrdoob/three.js@dev/examples/models/gltf/LeePerrySmith/LeePerrySmith.glb",
            "filename": "realistic_human_backup.glb",
            "description": "高质量真人头部模型"
        },
        "professional_avatar_1": {
            "url": "https://github.com/microsoft/MixedRealityToolkit-Unity/raw/main/Assets/MRTK/Examples/Demos/HandTracking/Models/ArticulatedHandLeft.glb",
            "filename": "professional_female_alt.glb",
            "description": "企业级女性头像模型"
        },
        "fallback_realistic": {
            "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/Fox/glTF-Binary/Fox.glb",
            "filename": "fallback_model.glb",
            "description": "备用动画模型"
        }
    }
    
    success_count = 0
    
    for model_key, model_info in model_urls.items():
        print(f"\n{'='*50}")
        
        filepath = os.path.join(models_dir, model_info["filename"])
        
        # 检查文件是否已存在且有效
        if os.path.exists(filepath):
            if verify_glb_file(filepath):
                print(f"⏭️ {model_info['filename']} 已存在且有效，跳过下载")
                success_count += 1
                continue
            else:
                print(f"🗑️ 删除无效文件: {model_info['filename']}")
                os.remove(filepath)
        
        # 下载文件
        if download_file(model_info["url"], filepath, model_info["description"]):
            if verify_glb_file(filepath):
                success_count += 1
            else:
                print(f"⚠️ 下载的文件格式不正确，删除...")
                if os.path.exists(filepath):
                    os.remove(filepath)
        
        # 避免频繁请求
        time.sleep(1)
    
    print(f"\n{'='*50}")
    print(f"📊 下载完成统计:")
    print(f"✅ 成功: {success_count}/{len(model_urls)}")
    print(f"❌ 失败: {len(model_urls) - success_count}/{len(model_urls)}")
    
    if success_count > 0:
        print(f"\n🎉 已下载 {success_count} 个有效的GLB模型文件!")
        print(f"📁 模型位置: {os.path.abspath(models_dir)}/")
        
        print(f"\n💡 使用建议:")
        print(f"1. 在HTML中修改模型路径:")
        print(f"   modelPath: './models/business_female_professional.glb'")
        print(f"2. 或使用备用模型:")
        print(f"   modelPath: './models/business_female_simple.glb'")
        print(f"3. 启动HTTP服务器:")
        print(f"   python start-server.py")
        
    else:
        print(f"\n❌ 所有下载都失败了")
        print(f"💡 请检查网络连接或手动下载模型文件")

if __name__ == "__main__":
    main()