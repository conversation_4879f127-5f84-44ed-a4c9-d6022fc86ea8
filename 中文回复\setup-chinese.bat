@echo off
chcp 65001 >nul
echo 正在设置Claude Code中文环境...

REM 设置环境变量
setx CLAUDE_LANGUAGE "zh-CN" /M
setx LANG "zh_CN.UTF-8" /M
setx LC_ALL "zh_CN.UTF-8" /M
setx CLAUDE_CODE_LANGUAGE "chinese" /M
setx CLAUDE_DEFAULT_LANGUAGE "zh-CN" /M
setx CLAUDE_RESPONSE_LANGUAGE "chinese" /M

REM 创建多个可能的配置目录
if not exist "%USERPROFILE%\.config" mkdir "%USERPROFILE%\.config"
if not exist "%USERPROFILE%\.config\claude-code" mkdir "%USERPROFILE%\.config\claude-code"
if not exist "%APPDATA%\Claude Code" mkdir "%APPDATA%\Claude Code"
if not exist "%LOCALAPPDATA%\Claude Code" mkdir "%LOCALAPPDATA%\Claude Code"
if not exist "%USERPROFILE%\.claude-code" mkdir "%USERPROFILE%\.claude-code"

REM 复制配置文件到多个位置
echo 正在复制配置文件...
copy "claude-code-settings.json" "%USERPROFILE%\.config\claude-code\settings.json" >nul 2>&1
copy "claude-code-settings.json" "%APPDATA%\Claude Code\settings.json" >nul 2>&1
copy "claude-code-settings.json" "%LOCALAPPDATA%\Claude Code\settings.json" >nul 2>&1
copy "claude-code-settings.json" "%USERPROFILE%\.claude-code\settings.json" >nul 2>&1
copy "claude-code-settings.json" "%USERPROFILE%\.config\claude-code\config.json" >nul 2>&1

echo 设置完成！请重新启动Claude Code以使设置生效。
echo 配置文件已创建在以下位置：
echo - %USERPROFILE%\.config\claude-code\settings.json
echo - %APPDATA%\Claude Code\settings.json
echo.
echo 环境变量已设置：
echo - CLAUDE_LANGUAGE=zh-CN
echo - LANG=zh_CN.UTF-8
echo - LC_ALL=zh_CN.UTF-8
echo - CLAUDE_CODE_LANGUAGE=chinese
echo.
echo 请重新启动命令行或计算机以使环境变量生效。
pause