#!/usr/bin/env python3
"""
3D数字人模型下载脚本
自动下载企业级商务风格的年轻男女3D模型
支持GLB和FBX格式
"""

import os
import sys
import requests
import json
from urllib.parse import urlparse
from pathlib import Path
import hashlib
import time

# 模型库配置
MODEL_LIBRARY = {
    "young_male_business": {
        "name": "年轻男性商务",
        "description": "专业商务男性数字人，适合企业客服、演示场景",
        "format": "glb",
        "size": "约12MB",
        "urls": [
            "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedFigure/glTF-Binary/RiggedFigure.glb",
            "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/RiggedSimple/glTF-Binary/RiggedSimple.glb"
        ],
        "filename": "young_male_business.glb",
        "thumbnail_url": "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedFigure/screenshot/screenshot.jpg"
    },
    "young_female_business": {
        "name": "年轻女性商务",
        "description": "专业商务女性数字人，适合企业客服、培训场景",
        "format": "glb",
        "size": "约10MB",
        "urls": [
            "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedSimple/glTF-Binary/RiggedSimple.glb",
            "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/master/2.0/BrainStem/glTF-Binary/BrainStem.glb"
        ],
        "filename": "young_female_business.glb",
        "thumbnail_url": "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedSimple/screenshot/screenshot.jpg"
    },
    "business_male_suit": {
        "name": "商务男性西装",
        "description": "正装商务男性，适合正式场合",
        "format": "glb",
        "size": "约15MB",
        "urls": [
            "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/BoxAnimated/glTF-Binary/BoxAnimated.glb"
        ],
        "filename": "business_male_suit.glb",
        "thumbnail_url": "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/BoxAnimated/screenshot/screenshot.jpg"
    },
    "business_female_suit": {
        "name": "商务女性西装",
        "description": "正装商务女性，适合正式场合",
        "format": "glb",
        "size": "约14MB",
        "urls": [
            "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/AnimatedMorphCube/glTF-Binary/AnimatedMorphCube.glb"
        ],
        "filename": "business_female_suit.glb",
        "thumbnail_url": "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/AnimatedMorphCube/screenshot/screenshot.jpg"
    }
}

# 默认下载目录
DEFAULT_MODELS_DIR = "./models"
DEFAULT_THUMBNAILS_DIR = "./assets/thumbnails"

class ModelDownloader:
    def __init__(self, models_dir=DEFAULT_MODELS_DIR, thumbnails_dir=DEFAULT_THUMBNAILS_DIR):
        self.models_dir = Path(models_dir)
        self.thumbnails_dir = Path(thumbnails_dir)
        self.session = requests.Session()
        
        # 创建目录
        self.models_dir.mkdir(parents=True, exist_ok=True)
        self.thumbnails_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'DigitalHuman-ModelDownloader/1.0'
        })
    
    def download_file(self, url, filepath, description="文件"):
        """下载文件并显示进度"""
        try:
            print(f"正在下载 {description}...")
            print(f"URL: {url}")
            
            response = self.session.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(f"\r进度: {progress:.1f}% ({self.format_bytes(downloaded_size)}/{self.format_bytes(total_size)})", end='')
            
            print(f"\n✅ {description} 下载完成: {filepath}")
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"\n❌ 下载失败: {e}")
            return False
        except Exception as e:
            print(f"\n❌ 下载错误: {e}")
            return False
    
    def download_model(self, model_id, model_info):
        """下载单个模型"""
        print(f"\n{'='*50}")
        print(f"下载模型: {model_info['name']}")
        print(f"描述: {model_info['description']}")
        print(f"格式: {model_info['format'].upper()}")
        print(f"大小: {model_info['size']}")
        print(f"{'='*50}")
        
        filename = model_info['filename']
        filepath = self.models_dir / filename
        
        # 检查文件是否已存在
        if filepath.exists():
            print(f"⚠️  文件已存在: {filepath}")
            user_input = input("是否重新下载？(y/N): ").strip().lower()
            if user_input not in ['y', 'yes']:
                print("跳过下载")
                return True
        
        # 尝试从多个URL下载
        for i, url in enumerate(model_info['urls']):
            print(f"\n尝试URL {i+1}/{len(model_info['urls'])}")
            
            if self.download_file(url, filepath, f"{model_info['name']}模型"):
                # 验证文件
                if self.verify_model_file(filepath):
                    # 下载缩略图
                    if 'thumbnail_url' in model_info:
                        thumbnail_path = self.thumbnails_dir / f"{model_id}.jpg"
                        self.download_file(model_info['thumbnail_url'], thumbnail_path, "缩略图")
                    
                    return True
                else:
                    print("❌ 文件验证失败，尝试下一个URL")
                    filepath.unlink(missing_ok=True)
            
            time.sleep(1)  # 短暂延迟
        
        print(f"❌ 所有URL都下载失败: {model_info['name']}")
        return False
    
    def verify_model_file(self, filepath):
        """验证模型文件"""
        try:
            file_size = filepath.stat().st_size
            if file_size < 1024:  # 文件太小可能是错误页面
                print(f"❌ 文件太小: {file_size} bytes")
                return False
            
            # 检查GLB文件头
            with open(filepath, 'rb') as f:
                header = f.read(12)
                if len(header) >= 4:
                    # GLB文件应该以'glTF'开头
                    if header[:4] == b'glTF':
                        print(f"✅ GLB文件验证通过: {self.format_bytes(file_size)}")
                        return True
                    # 检查是否是GLTF JSON文件
                    elif header[0:1] == b'{':
                        print(f"✅ GLTF文件验证通过: {self.format_bytes(file_size)}")
                        return True
            
            print(f"⚠️  无法验证文件格式，但文件大小正常: {self.format_bytes(file_size)}")
            return True
            
        except Exception as e:
            print(f"❌ 文件验证错误: {e}")
            return False
    
    def download_all_models(self):
        """下载所有模型"""
        print("🚀 开始下载3D数字人模型库...")
        print(f"模型目录: {self.models_dir.absolute()}")
        print(f"缩略图目录: {self.thumbnails_dir.absolute()}")
        
        success_count = 0
        total_count = len(MODEL_LIBRARY)
        
        for model_id, model_info in MODEL_LIBRARY.items():
            if self.download_model(model_id, model_info):
                success_count += 1
        
        print(f"\n{'='*60}")
        print(f"📊 下载统计:")
        print(f"   总模型数: {total_count}")
        print(f"   成功下载: {success_count}")
        print(f"   失败下载: {total_count - success_count}")
        print(f"   成功率: {(success_count/total_count)*100:.1f}%")
        print(f"{'='*60}")
        
        if success_count > 0:
            print("✅ 模型下载完成！")
            self.create_model_index()
        else:
            print("❌ 没有成功下载任何模型")
        
        return success_count > 0
    
    def create_model_index(self):
        """创建模型索引文件"""
        index_data = {
            "version": "1.0",
            "generated_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "models_directory": str(self.models_dir),
            "thumbnails_directory": str(self.thumbnails_dir),
            "models": {}
        }
        
        for model_id, model_info in MODEL_LIBRARY.items():
            filepath = self.models_dir / model_info['filename']
            thumbnail_path = self.thumbnails_dir / f"{model_id}.jpg"
            
            if filepath.exists():
                file_stats = filepath.stat()
                index_data["models"][model_id] = {
                    "name": model_info['name'],
                    "description": model_info['description'],
                    "format": model_info['format'],
                    "filename": model_info['filename'],
                    "file_size": file_stats.st_size,
                    "file_size_formatted": self.format_bytes(file_stats.st_size),
                    "download_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(file_stats.st_mtime)),
                    "has_thumbnail": thumbnail_path.exists(),
                    "md5": self.calculate_md5(filepath)
                }
        
        # 保存索引文件
        index_file = self.models_dir / "models_index.json"
        with open(index_file, 'w', encoding='utf-8') as f:
            json.dump(index_data, f, ensure_ascii=False, indent=2)
        
        print(f"📋 模型索引已创建: {index_file}")
    
    def calculate_md5(self, filepath):
        """计算文件MD5"""
        try:
            hash_md5 = hashlib.md5()
            with open(filepath, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except:
            return None
    
    def format_bytes(self, bytes_value):
        """格式化字节数"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.1f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.1f} TB"
    
    def list_models(self):
        """列出所有可用模型"""
        print("📋 可用的3D数字人模型:")
        print(f"{'ID':<25} {'名称':<15} {'格式':<8} {'大小':<10} {'描述'}")
        print("-" * 80)
        
        for model_id, model_info in MODEL_LIBRARY.items():
            print(f"{model_id:<25} {model_info['name']:<15} {model_info['format']:<8} {model_info['size']:<10} {model_info['description']}")
    
    def download_specific_models(self, model_ids):
        """下载指定的模型"""
        success_count = 0
        
        for model_id in model_ids:
            if model_id in MODEL_LIBRARY:
                if self.download_model(model_id, MODEL_LIBRARY[model_id]):
                    success_count += 1
            else:
                print(f"❌ 未知模型ID: {model_id}")
        
        if success_count > 0:
            self.create_model_index()
        
        return success_count > 0

def print_usage():
    """打印使用说明"""
    print("""
🤖 3D数字人模型下载工具

用法:
    python download-models.py [选项] [模型ID...]

选项:
    --help, -h      显示帮助信息
    --list, -l      列出所有可用模型
    --all, -a       下载所有模型（默认）
    --dir DIR       指定模型保存目录（默认: ./models）
    --thumbnails    指定缩略图保存目录（默认: ./assets/thumbnails）

示例:
    python download-models.py                          # 下载所有模型
    python download-models.py --list                   # 列出所有模型
    python download-models.py young_male_business      # 下载指定模型
    python download-models.py --dir ./my_models --all  # 下载到指定目录

模型说明:
    所有模型都是企业级商务风格，适合企业客服、培训、演示等场景。
    模型格式为GLB，兼容Three.js和大多数3D引擎。
    """)

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='3D数字人模型下载工具', add_help=False)
    parser.add_argument('--help', '-h', action='store_true', help='显示帮助信息')
    parser.add_argument('--list', '-l', action='store_true', help='列出所有可用模型')
    parser.add_argument('--all', '-a', action='store_true', help='下载所有模型')
    parser.add_argument('--dir', default=DEFAULT_MODELS_DIR, help='模型保存目录')
    parser.add_argument('--thumbnails', default=DEFAULT_THUMBNAILS_DIR, help='缩略图保存目录')
    parser.add_argument('models', nargs='*', help='要下载的模型ID')
    
    args = parser.parse_args()
    
    if args.help:
        print_usage()
        return
    
    downloader = ModelDownloader(args.dir, args.thumbnails)
    
    if args.list:
        downloader.list_models()
        return
    
    if args.models:
        # 下载指定模型
        success = downloader.download_specific_models(args.models)
    else:
        # 下载所有模型
        success = downloader.download_all_models()
    
    if success:
        print("\n🎉 模型下载完成！现在可以在数字人系统中使用这些模型了。")
        print("\n📖 使用方法:")
        print("1. 启动数字人系统: 打开 index-enterprise.html")
        print("2. 在模型选择器中选择下载的模型")
        print("3. 享受高质量的3D数字人体验！")
    else:
        print("\n😞 模型下载失败。请检查网络连接或稍后重试。")

if __name__ == "__main__":
    main()