/**
 * 3D模型管理器
 * 支持GLB/FBX格式模型的下载、缓存、管理
 */
class ModelManager {
    constructor() {
        this.models = new Map();
        this.modelCache = new Map();
        this.downloadQueue = [];
        this.isDownloading = false;
        this.modelLibrary = {
            'young-male-business': {
                name: '年轻男性商务',
                type: 'business',
                gender: 'male',
                age: 'young',
                format: 'glb',
                size: '12.5MB',
                url: 'https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedFigure/glTF-Binary/RiggedFigure.glb',
                fallbackUrl: './models/male_business.glb',
                thumbnail: './assets/thumbnails/male_business.jpg',
                animations: ['idle', 'walk', 'talk', 'present', 'greet'],
                description: '专业商务男性数字人，适合企业客服、演示场景',
                tags: ['business', 'professional', 'male', 'suit']
            },
            'young-female-business': {
                name: '年轻女性商务',
                type: 'business',
                gender: 'female',
                age: 'young',
                format: 'glb',
                size: '11.8MB',
                url: 'https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedSimple/glTF-Binary/RiggedSimple.glb',
                fallbackUrl: './models/female_business.glb',
                thumbnail: './assets/thumbnails/female_business.jpg',
                animations: ['idle', 'walk', 'talk', 'present', 'greet'],
                description: '专业商务女性数字人，适合企业客服、培训场景',
                tags: ['business', 'professional', 'female', 'suit']
            },
            'casual-male': {
                name: '休闲男性',
                type: 'casual',
                gender: 'male',
                age: 'young',
                format: 'glb',
                size: '9.2MB',
                url: 'https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/BrainStem/glTF-Binary/BrainStem.glb',
                fallbackUrl: './models/male_casual.glb',
                thumbnail: './assets/thumbnails/male_casual.jpg',
                animations: ['idle', 'walk', 'wave', 'dance'],
                description: '休闲风格男性数字人，适合轻松的交互场景',
                tags: ['casual', 'friendly', 'male', 'relaxed']
            },
            'casual-female': {
                name: '休闲女性',
                type: 'casual',
                gender: 'female',
                age: 'young',
                format: 'glb',
                size: '8.9MB',
                url: 'https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/BoxAnimated/glTF-Binary/BoxAnimated.glb',
                fallbackUrl: './models/female_casual.glb',
                thumbnail: './assets/thumbnails/female_casual.jpg',
                animations: ['idle', 'walk', 'wave', 'dance'],
                description: '休闲风格女性数字人，适合友好的交互场景',
                tags: ['casual', 'friendly', 'female', 'relaxed']
            }
        };
        
        this.eventListeners = {};
        this.init();
    }
    
    /**
     * 初始化模型管理器
     */
    init() {
        this.setupLocalStorage();
        this.loadCachedModels();
        this.checkModelAvailability();
    }
    
    /**
     * 设置本地存储
     */
    setupLocalStorage() {
        if (!localStorage.getItem('digitalHuman_modelCache')) {
            localStorage.setItem('digitalHuman_modelCache', JSON.stringify({}));
        }
    }
    
    /**
     * 加载缓存的模型信息
     */
    loadCachedModels() {
        try {
            const cached = JSON.parse(localStorage.getItem('digitalHuman_modelCache') || '{}');
            this.modelCache = new Map(Object.entries(cached));
        } catch (error) {
            console.error('Failed to load cached models:', error);
            this.modelCache = new Map();
        }
    }
    
    /**
     * 保存模型缓存信息
     */
    saveCachedModels() {
        try {
            const cacheObj = Object.fromEntries(this.modelCache);
            localStorage.setItem('digitalHuman_modelCache', JSON.stringify(cacheObj));
        } catch (error) {
            console.error('Failed to save model cache:', error);
        }
    }
    
    /**
     * 检查模型可用性
     */
    async checkModelAvailability() {
        for (const [modelId, modelInfo] of Object.entries(this.modelLibrary)) {
            const isAvailable = await this.isModelAvailable(modelId);
            modelInfo.available = isAvailable;
            
            if (isAvailable) {
                this.emit('modelAvailable', { modelId, modelInfo });
            }
        }
    }
    
    /**
     * 检查单个模型是否可用
     */
    async isModelAvailable(modelId) {
        const modelInfo = this.modelLibrary[modelId];
        if (!modelInfo) return false;
        
        // 检查缓存
        if (this.modelCache.has(modelId)) {
            const cached = this.modelCache.get(modelId);
            if (cached.downloaded && cached.lastVerified > Date.now() - 24 * 60 * 60 * 1000) {
                return true;
            }
        }
        
        // 检查本地文件
        try {
            const response = await fetch(modelInfo.fallbackUrl, { method: 'HEAD' });
            if (response.ok) {
                this.updateModelCache(modelId, { downloaded: true, localPath: modelInfo.fallbackUrl });
                return true;
            }
        } catch (error) {
            // 本地文件不存在，继续检查远程
        }
        
        // 检查远程文件
        try {
            const response = await fetch(modelInfo.url, { method: 'HEAD' });
            if (response.ok) {
                this.updateModelCache(modelId, { downloaded: false, remoteAvailable: true });
                return true;
            }
        } catch (error) {
            console.warn(`Model ${modelId} not available:`, error);
        }
        
        return false;
    }
    
    /**
     * 更新模型缓存信息
     */
    updateModelCache(modelId, info) {
        const existing = this.modelCache.get(modelId) || {};
        const updated = {
            ...existing,
            ...info,
            lastVerified: Date.now()
        };
        
        this.modelCache.set(modelId, updated);
        this.saveCachedModels();
    }
    
    /**
     * 获取模型列表
     */
    getModelList() {
        return Object.entries(this.modelLibrary).map(([id, info]) => ({
            id,
            ...info,
            cached: this.modelCache.has(id),
            downloaded: this.modelCache.get(id)?.downloaded || false
        }));
    }
    
    /**
     * 获取模型信息
     */
    getModelInfo(modelId) {
        const info = this.modelLibrary[modelId];
        if (!info) return null;
        
        const cached = this.modelCache.get(modelId);
        return {
            ...info,
            id: modelId,
            cached: !!cached,
            downloaded: cached?.downloaded || false,
            lastVerified: cached?.lastVerified || null
        };
    }
    
    /**
     * 下载模型
     */
    async downloadModel(modelId, onProgress = null) {
        const modelInfo = this.modelLibrary[modelId];
        if (!modelInfo) {
            throw new Error(`Model ${modelId} not found`);
        }
        
        this.emit('downloadStart', { modelId, modelInfo });
        
        try {
            // 尝试从主URL下载
            let url = modelInfo.url;
            let response = await fetch(url);
            
            if (!response.ok) {
                // 如果主URL失败，尝试备用URL
                url = modelInfo.fallbackUrl;
                response = await fetch(url);
                
                if (!response.ok) {
                    throw new Error(`Failed to download model from ${url}: ${response.status} ${response.statusText}`);
                }
            }
            
            const contentLength = response.headers.get('content-length');
            const total = contentLength ? parseInt(contentLength, 10) : 0;
            let loaded = 0;
            
            const reader = response.body.getReader();
            const chunks = [];
            
            while (true) {
                const { done, value } = await reader.read();
                
                if (done) break;
                
                chunks.push(value);
                loaded += value.length;
                
                if (onProgress && total > 0) {
                    const progress = (loaded / total) * 100;
                    onProgress(progress, loaded, total);
                    this.emit('downloadProgress', { modelId, progress, loaded, total });
                }
            }
            
            // 组合所有chunks
            const blob = new Blob(chunks);
            const arrayBuffer = await blob.arrayBuffer();
            
            // 存储到IndexedDB或创建临时URL
            const modelUrl = await this.storeModel(modelId, arrayBuffer);
            
            // 更新缓存
            this.updateModelCache(modelId, {
                downloaded: true,
                localPath: modelUrl,
                downloadTime: Date.now(),
                size: loaded
            });
            
            this.emit('downloadComplete', { modelId, modelInfo, url: modelUrl });
            
            return modelUrl;
            
        } catch (error) {
            this.emit('downloadError', { modelId, error });
            throw error;
        }
    }
    
    /**
     * 存储模型到本地
     */
    async storeModel(modelId, arrayBuffer) {
        // 创建Blob和URL
        const blob = new Blob([arrayBuffer], { type: 'model/gltf-binary' });
        const url = URL.createObjectURL(blob);
        
        // 可以在这里实现IndexedDB存储以实现持久化
        // 现在先返回临时URL
        
        return url;
    }
    
    /**
     * 加载模型
     */
    async loadModel(modelId) {
        const modelInfo = this.getModelInfo(modelId);
        if (!modelInfo) {
            throw new Error(`Model ${modelId} not found`);
        }
        
        // 检查是否已加载
        if (this.models.has(modelId)) {
            return this.models.get(modelId);
        }
        
        let modelUrl;
        
        // 检查是否已下载
        if (modelInfo.downloaded && modelInfo.cached) {
            const cached = this.modelCache.get(modelId);
            modelUrl = cached.localPath;
        } else {
            // 先尝试本地路径
            modelUrl = modelInfo.fallbackUrl;
            
            // 检查本地文件是否存在
            try {
                const response = await fetch(modelUrl, { method: 'HEAD' });
                if (!response.ok) {
                    // 本地文件不存在，使用远程URL
                    modelUrl = modelInfo.url;
                }
            } catch (error) {
                // 网络错误，尝试远程URL
                modelUrl = modelInfo.url;
            }
        }
        
        this.emit('loadStart', { modelId, url: modelUrl });
        
        try {
            const loader = new THREE.GLTFLoader();
            const gltf = await new Promise((resolve, reject) => {
                loader.load(
                    modelUrl,
                    resolve,
                    (progress) => {
                        const percent = (progress.loaded / progress.total) * 100;
                        this.emit('loadProgress', { modelId, progress: percent });
                    },
                    reject
                );
            });
            
            // 缓存模型
            this.models.set(modelId, gltf);
            
            this.emit('loadComplete', { modelId, model: gltf });
            
            return gltf;
            
        } catch (error) {
            this.emit('loadError', { modelId, error });
            throw error;
        }
    }
    
    /**
     * 删除模型缓存
     */
    deleteModelCache(modelId) {
        // 清理内存中的模型
        if (this.models.has(modelId)) {
            const model = this.models.get(modelId);
            // 清理Three.js资源
            if (model.scene) {
                model.scene.traverse((child) => {
                    if (child.geometry) child.geometry.dispose();
                    if (child.material) {
                        if (Array.isArray(child.material)) {
                            child.material.forEach(material => material.dispose());
                        } else {
                            child.material.dispose();
                        }
                    }
                });
            }
            this.models.delete(modelId);
        }
        
        // 清理缓存信息
        this.modelCache.delete(modelId);
        this.saveCachedModels();
        
        this.emit('modelDeleted', { modelId });
    }
    
    /**
     * 获取缓存统计信息
     */
    getCacheStats() {
        let totalSize = 0;
        let downloadedCount = 0;
        
        for (const [modelId, cacheInfo] of this.modelCache) {
            if (cacheInfo.downloaded) {
                downloadedCount++;
                totalSize += cacheInfo.size || 0;
            }
        }
        
        return {
            totalModels: Object.keys(this.modelLibrary).length,
            downloadedCount,
            totalSize,
            formattedSize: this.formatBytes(totalSize)
        };
    }
    
    /**
     * 格式化字节数
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    /**
     * 清理所有缓存
     */
    clearAllCache() {
        // 清理内存中的模型
        for (const modelId of this.models.keys()) {
            this.deleteModelCache(modelId);
        }
        
        // 清理localStorage
        localStorage.removeItem('digitalHuman_modelCache');
        
        this.emit('cacheCleared');
    }
    
    /**
     * 批量下载模型
     */
    async downloadBatch(modelIds, onProgress = null) {
        this.downloadQueue = [...modelIds];
        this.isDownloading = true;
        
        const results = [];
        let completed = 0;
        
        for (const modelId of modelIds) {
            try {
                const url = await this.downloadModel(modelId, (progress) => {
                    if (onProgress) {
                        const overallProgress = ((completed + progress / 100) / modelIds.length) * 100;
                        onProgress(overallProgress, modelId, progress);
                    }
                });
                
                results.push({ modelId, success: true, url });
                completed++;
                
            } catch (error) {
                results.push({ modelId, success: false, error: error.message });
                completed++;
            }
        }
        
        this.isDownloading = false;
        this.downloadQueue = [];
        
        this.emit('batchDownloadComplete', { results });
        
        return results;
    }
    
    /**
     * 事件监听器
     */
    on(event, callback) {
        if (!this.eventListeners[event]) {
            this.eventListeners[event] = [];
        }
        this.eventListeners[event].push(callback);
    }
    
    /**
     * 触发事件
     */
    emit(event, data) {
        if (this.eventListeners[event]) {
            this.eventListeners[event].forEach(callback => callback(data));
        }
    }
    
    /**
     * 移除事件监听器
     */
    off(event, callback) {
        if (this.eventListeners[event]) {
            const index = this.eventListeners[event].indexOf(callback);
            if (index > -1) {
                this.eventListeners[event].splice(index, 1);
            }
        }
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ModelManager;
} else {
    window.ModelManager = ModelManager;
}