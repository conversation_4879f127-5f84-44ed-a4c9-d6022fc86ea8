@echo off
chcp 65001 >nul
title 企业数字人本地服务器

echo.
echo ████████████████████████████████████████████████████████
echo ████████████████ 企业数字人本地服务器 ████████████████████
echo ████████████████████████████████████████████████████████
echo.

cd /d "%~dp0"

echo 📋 正在检查系统环境...

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python，正在尝试其他启动方式...
    goto :try_node
)

echo ✅ Python 环境已就绪
echo.
echo 🚀 正在启动企业数字人系统...
echo 💡 系统将自动打开浏览器，请稍等...
echo.

python start-server.py
goto :end

:try_node
echo.
echo 📋 正在尝试Node.js启动方式...

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Node.js，使用备用启动方式...
    goto :try_simple
)

echo ✅ Node.js 环境已就绪

REM 创建简单的Node.js服务器
echo const http = require('http'); > temp_server.js
echo const fs = require('fs'); >> temp_server.js
echo const path = require('path'); >> temp_server.js
echo const url = require('url'); >> temp_server.js
echo. >> temp_server.js
echo const port = 8000; >> temp_server.js
echo. >> temp_server.js
echo const server = http.createServer((req, res) => { >> temp_server.js
echo   res.setHeader('Access-Control-Allow-Origin', '*'); >> temp_server.js
echo   res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS'); >> temp_server.js
echo   res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization'); >> temp_server.js
echo. >> temp_server.js
echo   if (req.method === 'OPTIONS') { >> temp_server.js
echo     res.writeHead(200); >> temp_server.js
echo     res.end(); >> temp_server.js
echo     return; >> temp_server.js
echo   } >> temp_server.js
echo. >> temp_server.js
echo   let filePath = path.join(__dirname, req.url === '/' ? '/index-complete-customer-service.html' : req.url); >> temp_server.js
echo   fs.readFile(filePath, (err, data) => { >> temp_server.js
echo     if (err) { >> temp_server.js
echo       res.writeHead(404); >> temp_server.js
echo       res.end('File not found'); >> temp_server.js
echo       return; >> temp_server.js
echo     } >> temp_server.js
echo     const ext = path.extname(filePath); >> temp_server.js
echo     if (ext === '.glb') res.setHeader('Content-Type', 'model/gltf-binary'); >> temp_server.js
echo     else if (ext === '.js') res.setHeader('Content-Type', 'application/javascript'); >> temp_server.js
echo     else if (ext === '.html') res.setHeader('Content-Type', 'text/html; charset=utf-8'); >> temp_server.js
echo     res.writeHead(200); >> temp_server.js
echo     res.end(data); >> temp_server.js
echo   }); >> temp_server.js
echo }); >> temp_server.js
echo. >> temp_server.js
echo server.listen(port, () => { >> temp_server.js
echo   console.log('🏢 企业数字人服务器已启动!'); >> temp_server.js
echo   console.log('🌐 访问地址: http://localhost:' + port + '/index-complete-customer-service.html'); >> temp_server.js
echo   require('child_process').exec('start http://localhost:' + port + '/index-complete-customer-service.html'); >> temp_server.js
echo }); >> temp_server.js

echo.
echo 🚀 正在启动Node.js服务器...
node temp_server.js
del temp_server.js >nul 2>&1
goto :end

:try_simple
echo.
echo 📋 使用简单启动方式...
echo.
echo ⚠️ 检测到缺少Python和Node.js环境
echo 💡 请选择以下解决方案之一:
echo.
echo 1. 安装Python 3.x (推荐)
echo    下载地址: https://www.python.org/downloads/
echo.
echo 2. 安装Node.js (备选)
echo    下载地址: https://nodejs.org/
echo.
echo 3. 使用在线IDE (临时方案)
echo    - 将项目文件上传到GitHub
echo    - 使用Gitpod或CodeSandbox运行
echo.
echo 4. 使用Chrome扩展 (不推荐)
echo    - 启动Chrome时添加 --disable-web-security --user-data-dir
echo    - 直接打开HTML文件
echo.

choice /c 1234 /m "请选择解决方案 (1-4): "

if %errorlevel%==1 (
    echo 正在打开Python官网...
    start https://www.python.org/downloads/
)
if %errorlevel%==2 (
    echo 正在打开Node.js官网...
    start https://nodejs.org/
)
if %errorlevel%==3 (
    echo 正在打开GitHub...
    start https://github.com/
)
if %errorlevel%==4 (
    echo 正在尝试直接打开（可能有CORS问题）...
    start index-complete-customer-service.html
)

:end
echo.
echo 📋 如有问题，请查看以下文件:
echo   - Female-Customer-Service-Guide.md (使用指南)
echo   - PBR-System-Guide.md (技术文档)
echo.
pause