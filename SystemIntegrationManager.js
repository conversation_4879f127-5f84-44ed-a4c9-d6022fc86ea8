/**
 * 系统集成管理器
 * 统一管理所有数字人模块，提供完整的企业级解决方案
 */

class SystemIntegrationManager {
    constructor() {
        // 核心模块管理
        this.modules = {
            digitalHuman: null,           // 数字人核心
            customerService: null,        // 客服系统
            emotionSystem: null,          // 情感系统
            appearanceEnhancer: null,     // 外观增强
            modelManager: null,           // 模型管理
            voiceAdapter: null,           // 语音适配
            realDigitalHuman: null        // 简化接口
        };
        
        // 系统状态
        this.isInitialized = false;
        this.currentMode = 'enterprise'; // 'enterprise', 'simple', 'custom'
        this.systemHealth = {
            status: 'initializing',
            uptime: 0,
            lastCheck: Date.now(),
            errors: [],
            warnings: []
        };
        
        // 配置管理
        this.config = {
            enterprise: {
                enableAllFeatures: true,
                professionalMode: true,
                multiModelSupport: true,
                advancedVoice: true,
                performanceOptimization: true
            },
            simple: {
                enableAllFeatures: false,
                quickStart: true,
                basicFeatures: true,
                lightweightMode: true
            },
            custom: {
                allowModuleSelection: true,
                configurableSettings: true
            }
        };
        
        // 事件系统
        this.eventListeners = new Map();
        
        this.init();
    }
    
    /**
     * 系统初始化
     */
    async init() {
        console.log('🚀 启动系统集成管理器...');
        
        try {
            // 1. 检查环境
            await this.checkEnvironment();
            
            // 2. 加载配置
            await this.loadConfiguration();
            
            // 3. 初始化模块
            await this.initializeModules();
            
            // 4. 建立模块间通信
            this.setupModuleCommunication();
            
            // 5. 启动健康监控
            this.startHealthMonitoring();
            
            this.isInitialized = true;
            this.systemHealth.status = 'ready';
            
            console.log('✅ 系统集成管理器初始化完成');
            this.emit('systemReady', { timestamp: Date.now() });
            
        } catch (error) {
            console.error('❌ 系统初始化失败:', error);
            this.systemHealth.status = 'error';
            this.systemHealth.errors.push({
                timestamp: Date.now(),
                error: error.message,
                stack: error.stack
            });
            throw error;
        }
    }
    
    /**
     * 检查运行环境
     */
    async checkEnvironment() {
        const checks = {
            webgl: this.checkWebGL(),
            audio: this.checkAudioSupport(),
            storage: this.checkStorageSupport(),
            performance: this.checkPerformance()
        };
        
        const results = await Promise.all(Object.values(checks));
        const failed = results.filter(r => !r.passed);
        
        if (failed.length > 0) {
            const warnings = failed.map(f => f.message);
            this.systemHealth.warnings.push(...warnings);
            console.warn('⚠️ 环境检查警告:', warnings);
        }
        
        return {
            passed: failed.length === 0,
            results: results
        };
    }
    
    checkWebGL() {
        try {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            return {
                passed: !!gl,
                message: gl ? 'WebGL支持正常' : 'WebGL不支持'
            };
        } catch (error) {
            return { passed: false, message: 'WebGL检查失败' };
        }
    }
    
    checkAudioSupport() {
        const audioSupport = {
            speechSynthesis: 'speechSynthesis' in window,
            audioContext: 'AudioContext' in window || 'webkitAudioContext' in window,
            mediaRecorder: 'MediaRecorder' in window
        };
        
        const passed = Object.values(audioSupport).some(Boolean);
        return {
            passed,
            message: passed ? '音频功能支持' : '音频功能受限',
            details: audioSupport
        };
    }
    
    checkStorageSupport() {
        try {
            const testKey = '__test__';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            return { passed: true, message: '本地存储支持' };
        } catch (error) {
            return { passed: false, message: '本地存储不支持' };
        }
    }
    
    checkPerformance() {
        const memory = performance.memory;
        const passed = !memory || memory.usedJSHeapSize < 100 * 1024 * 1024; // 100MB
        
        return {
            passed,
            message: passed ? '性能正常' : '内存使用较高',
            details: memory
        };
    }
    
    /**
     * 加载系统配置
     */
    async loadConfiguration() {
        // 从localStorage加载用户配置
        const userConfig = this.loadUserConfig();
        
        // 合并默认配置
        this.currentConfig = {
            ...this.config[this.currentMode],
            ...userConfig
        };
        
        console.log('📋 配置加载完成:', this.currentConfig);
    }
    
    loadUserConfig() {
        try {
            const saved = localStorage.getItem('digitalHuman_config');
            return saved ? JSON.parse(saved) : {};
        } catch (error) {
            console.warn('配置加载失败，使用默认配置');
            return {};
        }
    }
    
    /**
     * 初始化模块
     */
    async initializeModules() {
        console.log('🔧 初始化系统模块...');
        
        // 根据模式初始化不同模块
        switch (this.currentMode) {
            case 'enterprise':
                await this.initializeEnterpriseModules();
                break;
            case 'simple':
                await this.initializeSimpleModules();
                break;
            case 'custom':
                await this.initializeCustomModules();
                break;
        }
    }
    
    async initializeEnterpriseModules() {
        const container = document.getElementById('digital-human-container');
        if (!container) {
            throw new Error('数字人容器未找到');
        }
        
        // 1. 模型管理器
        this.modules.modelManager = new ModelManager();
        await this.modules.modelManager.initialize();
        
        // 2. 高级语音适配器
        this.modules.voiceAdapter = new VoiceAdapter({
            mode: 'company_integration',
            fallbackToWebAPI: true
        });
        
        // 3. 企业数字人核心
        this.modules.digitalHuman = new EnterpriseDigitalHuman(container, {
            width: container.clientWidth,
            height: container.clientHeight,
            modelPath: './models/business_female.glb',
            enableAudio: true,
            enableControls: true
        });
        
        // 4. 客服系统
        this.modules.customerService = new EnterpriseCustomerService(this.modules.digitalHuman);
        
        // 5. 情感系统
        this.modules.emotionSystem = new EnhancedEmotionSystem(this.modules.digitalHuman);
        
        // 6. 外观增强器
        this.modules.appearanceEnhancer = new RealisticAppearanceEnhancer(this.modules.digitalHuman);
        
        console.log('✅ 企业级模块初始化完成');
    }
    
    async initializeSimpleModules() {
        const container = document.getElementById('digital-human-container');
        
        // 简化模式只使用核心功能
        this.modules.realDigitalHuman = new RealDigitalHuman(container, {
            modelPath: './models/RiggedSimple.glb',
            enableAudio: true,
            enableControls: true
        });
        
        console.log('✅ 简化模块初始化完成');
    }
    
    async initializeCustomModules() {
        // 根据用户配置选择性初始化模块
        const config = this.currentConfig;
        
        if (config.enableDigitalHuman) {
            // 初始化数字人核心...
        }
        
        if (config.enableCustomerService) {
            // 初始化客服系统...
        }
        
        // 其他模块...
        
        console.log('✅ 自定义模块初始化完成');
    }
    
    /**
     * 建立模块间通信
     */
    setupModuleCommunication() {
        console.log('🔗 建立模块间通信...');
        
        // 情感系统 -> 客服系统
        if (this.modules.emotionSystem && this.modules.customerService) {
            this.modules.customerService.emotionAnalyzer = this.modules.emotionSystem;
        }
        
        // 模型管理器 -> 数字人核心
        if (this.modules.modelManager && this.modules.digitalHuman) {
            this.modules.modelManager.setTargetRenderer(this.modules.digitalHuman);
        }
        
        // 语音适配器 -> 数字人核心
        if (this.modules.voiceAdapter && this.modules.digitalHuman) {
            this.modules.digitalHuman.voiceAdapter = this.modules.voiceAdapter;
        }
        
        // 外观增强器 -> 数字人核心
        if (this.modules.appearanceEnhancer && this.modules.digitalHuman) {
            // 等待模型加载后应用增强
            this.modules.digitalHuman.on('modelLoaded', () => {
                this.modules.appearanceEnhancer.onModelLoaded(this.modules.digitalHuman.model);
            });
        }
        
        console.log('✅ 模块间通信建立完成');
    }
    
    /**
     * 启动健康监控
     */
    startHealthMonitoring() {
        console.log('📊 启动系统健康监控...');
        
        setInterval(() => {
            this.performHealthCheck();
        }, 30000); // 30秒检查一次
        
        // 性能监控
        setInterval(() => {
            this.updatePerformanceMetrics();
        }, 5000); // 5秒更新一次性能指标
    }
    
    performHealthCheck() {
        const now = Date.now();
        this.systemHealth.uptime = now - this.systemHealth.lastCheck;
        this.systemHealth.lastCheck = now;
        
        // 检查各模块状态
        const moduleStatus = {};
        Object.entries(this.modules).forEach(([name, module]) => {
            if (module) {
                moduleStatus[name] = {
                    initialized: !!module,
                    functioning: this.checkModuleHealth(module)
                };
            }
        });
        
        // 更新系统状态
        const allHealthy = Object.values(moduleStatus).every(status => status.functioning);
        this.systemHealth.status = allHealthy ? 'healthy' : 'degraded';
        
        // 触发健康检查事件
        this.emit('healthCheck', {
            systemHealth: this.systemHealth,
            moduleStatus: moduleStatus
        });
    }
    
    checkModuleHealth(module) {
        // 基础健康检查
        if (!module) return false;
        
        // 检查是否有错误状态
        if (module.hasErrors && module.hasErrors()) return false;
        
        // 检查是否正在运行
        if (module.isRunning && !module.isRunning()) return false;
        
        return true;
    }
    
    updatePerformanceMetrics() {
        if (performance.memory) {
            const memory = performance.memory;
            this.systemHealth.performance = {
                usedJSHeapSize: memory.usedJSHeapSize,
                totalJSHeapSize: memory.totalJSHeapSize,
                jsHeapSizeLimit: memory.jsHeapSizeLimit,
                memoryUsagePercent: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
            };
        }
        
        // FPS监控
        if (this.modules.digitalHuman && this.modules.digitalHuman.getFPS) {
            this.systemHealth.fps = this.modules.digitalHuman.getFPS();
        }
    }
    
    /**
     * 切换系统模式
     */
    async switchMode(newMode) {
        if (this.currentMode === newMode) return;
        
        console.log(`🔄 切换系统模式: ${this.currentMode} -> ${newMode}`);
        
        // 保存当前状态
        this.saveCurrentState();
        
        // 停止当前模块
        await this.shutdownModules();
        
        // 切换模式
        this.currentMode = newMode;
        
        // 重新初始化
        await this.initializeModules();
        this.setupModuleCommunication();
        
        this.emit('modeChanged', { from: this.currentMode, to: newMode });
    }
    
    /**
     * 获取系统状态
     */
    getSystemStatus() {
        return {
            isInitialized: this.isInitialized,
            currentMode: this.currentMode,
            health: this.systemHealth,
            modules: Object.keys(this.modules).reduce((acc, key) => {
                acc[key] = !!this.modules[key];
                return acc;
            }, {}),
            config: this.currentConfig
        };
    }
    
    /**
     * 获取特定模块
     */
    getModule(moduleName) {
        return this.modules[moduleName] || null;
    }
    
    /**
     * 保存用户配置
     */
    saveUserConfig(config) {
        try {
            const merged = { ...this.currentConfig, ...config };
            localStorage.setItem('digitalHuman_config', JSON.stringify(merged));
            this.currentConfig = merged;
            
            this.emit('configUpdated', { config: merged });
            console.log('💾 用户配置已保存');
        } catch (error) {
            console.error('配置保存失败:', error);
        }
    }
    
    /**
     * 事件系统
     */
    on(event, callback) {
        if (!this.eventListeners.has(event)) {
            this.eventListeners.set(event, []);
        }
        this.eventListeners.get(event).push(callback);
    }
    
    off(event, callback) {
        if (this.eventListeners.has(event)) {
            const callbacks = this.eventListeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }
    
    emit(event, data) {
        if (this.eventListeners.has(event)) {
            this.eventListeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件处理错误 [${event}]:`, error);
                }
            });
        }
    }
    
    /**
     * 优雅关闭系统
     */
    async shutdown() {
        console.log('🔄 正在关闭系统...');
        
        this.saveCurrentState();
        await this.shutdownModules();
        
        this.isInitialized = false;
        this.systemHealth.status = 'shutdown';
        
        console.log('✅ 系统已关闭');
    }
    
    async shutdownModules() {
        // 按依赖顺序关闭模块
        const shutdownOrder = [
            'customerService',
            'emotionSystem',
            'appearanceEnhancer',
            'realDigitalHuman',
            'digitalHuman',
            'voiceAdapter',
            'modelManager'
        ];
        
        for (const moduleName of shutdownOrder) {
            const module = this.modules[moduleName];
            if (module && typeof module.destroy === 'function') {
                try {
                    await module.destroy();
                    console.log(`✅ ${moduleName} 模块已关闭`);
                } catch (error) {
                    console.error(`❌ ${moduleName} 模块关闭失败:`, error);
                }
            }
            this.modules[moduleName] = null;
        }
    }
    
    saveCurrentState() {
        // 保存当前系统状态
        const state = {
            mode: this.currentMode,
            timestamp: Date.now(),
            health: this.systemHealth
        };
        
        try {
            localStorage.setItem('digitalHuman_lastState', JSON.stringify(state));
        } catch (error) {
            console.warn('状态保存失败:', error);
        }
    }
}

// 全局系统管理器实例
let globalSystemManager = null;

/**
 * 获取全局系统管理器
 */
function getSystemManager() {
    if (!globalSystemManager) {
        globalSystemManager = new SystemIntegrationManager();
    }
    return globalSystemManager;
}

/**
 * 便捷初始化函数
 */
async function initializeDigitalHumanSystem(mode = 'enterprise') {
    const manager = getSystemManager();
    manager.currentMode = mode;
    await manager.init();
    return manager;
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SystemIntegrationManager, getSystemManager, initializeDigitalHumanSystem };
} else {
    window.SystemIntegrationManager = SystemIntegrationManager;
    window.getSystemManager = getSystemManager;
    window.initializeDigitalHumanSystem = initializeDigitalHumanSystem;
}