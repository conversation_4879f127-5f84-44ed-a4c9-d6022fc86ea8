#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级企业数字人模型下载器 - 2024增强版
专门下载高质量的企业级3D数字人模型
"""

import os
import requests
import json
from pathlib import Path
import time
import urllib.parse

class AdvancedEnterpriseModelDownloader:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.models_dir = self.base_dir / "models"
        self.models_dir.mkdir(exist_ok=True)
        
        # 高质量企业模型源
        self.premium_models = {
            # Khronos官方高质量模型
            "cesium_man": {
                "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/main/2.0/CesiumMan/glTF-Binary/CesiumMan.glb",
                "filename": "cesium_man_professional.glb",
                "description": "专业男性数字人模型",
                "category": "professional_male"
            },
            
            "brain_stem": {
                "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/main/2.0/BrainStem/glTF-Binary/BrainStem.glb",
                "filename": "brain_stem_avatar.glb",
                "description": "高级头部模型",
                "category": "head_model"
            },
            
            "fox_animated": {
                "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/main/2.0/Fox/glTF-Binary/Fox.glb",
                "filename": "fox_expression_demo.glb",
                "description": "表情动画演示模型",
                "category": "animation_demo"
            },
            
            # 更多高质量模型
            "rigged_figure": {
                "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/main/2.0/RiggedFigure/glTF-Binary/RiggedFigure.glb",
                "filename": "enterprise_female_rigged.glb",
                "description": "企业女性骨骼模型",
                "category": "professional_female"
            },
            
            "rigged_simple": {
                "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/main/2.0/RiggedSimple/glTF-Binary/RiggedSimple.glb",
                "filename": "enterprise_male_simple.glb",
                "description": "企业男性简化模型",
                "category": "professional_male"
            },
            
            # 动画模型
            "animated_morph_cube": {
                "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/main/2.0/AnimatedMorphCube/glTF-Binary/AnimatedMorphCube.glb",
                "filename": "morph_animation_demo.glb",
                "description": "变形动画演示",
                "category": "animation_demo"
            }
        }
        
        # Ready Player Me 示例配置
        self.rpm_config = {
            "api_base": "https://models.readyplayer.me",
            "examples": [
                "64bfa15f0e72c63d7c3934a6.glb",  # 示例ID
                "64bfa15f0e72c63d7c3934a7.glb"   # 示例ID
            ]
        }
    
    def download_with_progress(self, url, filepath, description):
        """带进度显示的下载函数"""
        try:
            print(f"\n🔄 开始下载: {description}")
            print(f"📁 目标文件: {filepath}")
            print(f"🌐 下载地址: {url}")
            
            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            
            response = requests.get(url, stream=True, headers=headers, timeout=30)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if total_size > 0:
                            percent = (downloaded / total_size) * 100
                            mb_downloaded = downloaded / 1024 / 1024
                            mb_total = total_size / 1024 / 1024
                            print(f"\r📊 进度: {percent:.1f}% ({mb_downloaded:.1f}MB/{mb_total:.1f}MB)", end='')
            
            print(f"\n✅ 下载完成: {description}")
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"\n❌ 下载失败: {description}")
            print(f"🔍 网络错误: {str(e)}")
            return False
        except Exception as e:
            print(f"\n❌ 下载失败: {description}")
            print(f"🔍 错误信息: {str(e)}")
            return False
    
    def verify_glb_model(self, filepath):
        """验证GLB模型文件"""
        try:
            if not filepath.exists():
                return False, "文件不存在"
            
            file_size = filepath.stat().st_size
            if file_size < 1024:  # 小于1KB
                return False, f"文件过小: {file_size}字节"
            
            # 检查GLB文件头
            with open(filepath, 'rb') as f:
                header = f.read(4)
                if header != b'glTF':
                    return False, "不是有效的GLB文件"
                
                # 读取版本信息
                version = int.from_bytes(f.read(4), byteorder='little')
                length = int.from_bytes(f.read(4), byteorder='little')
                
                if version != 2:
                    return False, f"不支持的glTF版本: {version}"
                
                if length != file_size:
                    return False, f"文件长度不匹配: 期望{length}, 实际{file_size}"
            
            return True, f"验证通过, 大小: {file_size/1024/1024:.1f}MB, glTF 2.0"
            
        except Exception as e:
            return False, f"验证出错: {str(e)}"
    
    def download_premium_models(self):
        """下载高级企业模型"""
        print("🚀 高级企业数字人模型下载器 - 2024版")
        print("=" * 60)
        
        success_count = 0
        total_count = len(self.premium_models)
        
        for model_id, config in self.premium_models.items():
            print(f"\n📦 [{success_count+1}/{total_count}] 处理模型: {model_id}")
            print(f"🏷️ 类别: {config['category']}")
            
            filepath = self.models_dir / config["filename"]
            
            # 检查文件是否已存在
            if filepath.exists():
                is_valid, msg = self.verify_glb_model(filepath)
                if is_valid:
                    print(f"✅ 文件已存在且有效: {config['filename']} ({msg})")
                    success_count += 1
                    continue
                else:
                    print(f"⚠️ 文件存在但无效: {msg}, 重新下载...")
                    filepath.unlink()
            
            # 下载文件
            if self.download_with_progress(config["url"], filepath, config["description"]):
                # 验证下载的文件
                is_valid, msg = self.verify_glb_model(filepath)
                if is_valid:
                    print(f"✅ 模型验证通过: {msg}")
                    success_count += 1
                else:
                    print(f"❌ 模型验证失败: {msg}")
                    filepath.unlink(missing_ok=True)
            
            time.sleep(1)  # 避免请求过快
        
        print("\n" + "=" * 60)
        print(f"📊 下载统计: {success_count}/{total_count} 成功")
        
        if success_count == total_count:
            print("🎉 所有高级模型下载完成！")
            self.generate_premium_report()
        elif success_count > 0:
            print("⚠️ 部分模型下载成功，请检查网络连接")
        else:
            print("❌ 所有模型下载失败，请检查网络连接")
        
        return success_count
    
    def generate_premium_report(self):
        """生成高级模型报告"""
        report_file = self.base_dir / "premium_models_report.md"
        
        report_content = f"""# 高级企业数字人模型报告

**生成时间**: {time.strftime("%Y-%m-%d %H:%M:%S")}

## 📁 已下载的高级模型

| 文件名 | 大小 | 状态 | 类别 | 描述 |
|--------|------|------|------|------|
"""
        
        categories = {}
        
        for model_file in self.models_dir.glob("*.glb"):
            size_mb = model_file.stat().st_size / 1024 / 1024
            is_valid, msg = self.verify_glb_model(model_file)
            status = "✅ 正常" if is_valid else "❌ 异常"
            
            # 获取模型信息
            category = "未分类"
            description = "未知模型"
            
            for config in self.premium_models.values():
                if config["filename"] == model_file.name:
                    category = config["category"]
                    description = config["description"]
                    break
            
            report_content += f"| {model_file.name} | {size_mb:.1f}MB | {status} | {category} | {description} |\n"
            
            # 统计分类
            if category not in categories:
                categories[category] = []
            categories[category].append(model_file.name)
        
        report_content += f"""

## 📊 模型分类统计

"""
        
        for category, models in categories.items():
            report_content += f"### {category}\n"
            for model in models:
                report_content += f"- {model}\n"
            report_content += "\n"
        
        report_content += f"""
## 🎯 企业应用建议

### 客服数字人推荐
1. **主力模型**: enterprise_female_rigged.glb (女性客服)
2. **备选方案**: enterprise_male_simple.glb (男性客服)
3. **表情演示**: fox_expression_demo.glb (表情系统测试)

### 专业展示推荐
1. **高端展示**: brain_stem_avatar.glb (专业头像)
2. **动画演示**: cesium_man_professional.glb (完整人物)
3. **技术测试**: morph_animation_demo.glb (变形动画)

## 🔧 技术集成指南

### 在Three.js中使用
```javascript
import {{ GLTFLoader }} from 'three/examples/jsm/loaders/GLTFLoader.js';

const loader = new GLTFLoader();
loader.load('models/enterprise_female_rigged.glb', (gltf) => {{
    scene.add(gltf.scene);
    // 设置动画
    if (gltf.animations.length > 0) {{
        const mixer = new THREE.AnimationMixer(gltf.scene);
        const action = mixer.clipAction(gltf.animations[0]);
        action.play();
    }}
}});
```

### 性能优化建议
1. 使用Draco压缩减小文件大小
2. 合理设置LOD (Level of Detail)
3. 优化纹理分辨率
4. 使用实例化渲染多个角色

## 📞 技术支持

- 模型测试: 使用 test-model-loading-debug.html
- 性能监控: 查看 PerformanceMonitor.js
- 错误诊断: 检查浏览器开发者工具
"""
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📊 高级模型报告已生成: {report_file}")

def main():
    """主函数"""
    downloader = AdvancedEnterpriseModelDownloader()
    
    print("🎯 高级企业数字人模型下载器")
    print("选择操作:")
    print("1. 下载所有高级模型")
    print("2. 生成模型报告")
    print("3. 验证现有模型")
    
    choice = input("\n请输入选择 (1-3): ").strip()
    
    if choice == "1":
        success_count = downloader.download_premium_models()
        if success_count > 0:
            print("\n🎉 下载完成！建议接下来:")
            print("1. 使用 test-model-loading-debug.html 测试模型")
            print("2. 查看生成的报告了解使用建议")
            print("3. 考虑使用 Ready Player Me 创建定制模型")
    
    elif choice == "2":
        downloader.generate_premium_report()
    
    elif choice == "3":
        print("\n🔍 验证现有模型...")
        models_dir = Path(__file__).parent / "models"
        
        for model_file in models_dir.glob("*.glb"):
            is_valid, msg = downloader.verify_glb_model(model_file)
            status = "✅" if is_valid else "❌"
            print(f"{status} {model_file.name}: {msg}")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
