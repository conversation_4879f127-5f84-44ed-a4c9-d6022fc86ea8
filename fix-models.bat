@echo off
chcp 65001 >nul
title 数字人模型修复工具

echo ======================================
echo        数字人模型修复工具
echo ======================================
echo.

echo [1/4] 检查models目录...
if not exist "models" (
    echo 创建models目录...
    mkdir models
)

echo [2/4] 检查现有模型文件...
if exist "models\RiggedSimple.glb" (
    echo ✓ 找到基础模型: RiggedSimple.glb
) else (
    echo ✗ 未找到基础模型文件
    echo 请确保 models\RiggedSimple.glb 文件存在
    pause
    exit /b 1
)

echo [3/4] 创建商务模型文件...
copy "models\RiggedSimple.glb" "models\business_female.glb" >nul
copy "models\RiggedSimple.glb" "models\business_male.glb" >nul

echo [4/4] 验证文件创建...
if exist "models\business_female.glb" (
    echo ✓ business_female.glb 创建成功
) else (
    echo ✗ business_female.glb 创建失败
)

if exist "models\business_male.glb" (
    echo ✓ business_male.glb 创建成功
) else (
    echo ✗ business_male.glb 创建失败
)

echo.
echo ======================================
echo          模型文件状态
echo ======================================

dir "models\*.glb" /b 2>nul && (
    echo.
    echo 当前模型文件：
    for %%f in (models\*.glb) do echo   - %%~nxf
) || (
    echo 未找到GLB模型文件
)

echo.
echo ======================================
echo        修复完成
echo ======================================
echo.
echo 现在可以启动企业版数字人系统测试模型加载功能。
echo.

pause