@echo off
chcp 65001
echo.
echo ================================================
echo           启动数字人HTTP服务器
echo ================================================
echo.
echo 正在启动本地HTTP服务器...
echo 服务器地址: http://localhost:8000
echo 访问地址: http://localhost:8000/index-complete-customer-service.html
echo.
echo 按 Ctrl+C 停止服务器
echo ================================================
echo.

cd /d "%~dp0"

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo 使用Python启动服务器...
    python -m http.server 8000
) else (
    echo Python未找到，尝试使用Node.js...
    node --version >nul 2>&1
    if %errorlevel% equ 0 (
        npx http-server -p 8000 -c-1
    ) else (
        echo.
        echo ❌ 未找到Python或Node.js
        echo 请安装Python 3.x 或 Node.js
        echo.
        echo Python下载: https://www.python.org/downloads/
        echo Node.js下载: https://nodejs.org/
        echo.
        pause
        exit /b 1
    )
)

pause