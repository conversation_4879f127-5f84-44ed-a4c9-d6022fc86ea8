@echo off
chcp 65001 >nul
:: Compile Digital Human Server with MSYS2 Environment
:: Sets up proper environment before compilation

echo ======================================
echo    Compile with MSYS2 Environment
echo ======================================
echo.

:: Try different MSYS2 paths
set MSYS2_ROOT=C:\msys64
if not exist "%MSYS2_ROOT%" set MSYS2_ROOT=C:\msys2
if not exist "%MSYS2_ROOT%" set MSYS2_ROOT=D:\msys64

if not exist "%MSYS2_ROOT%" (
    echo [Error] MSYS2 installation not found
    echo Please install MSYS2 from https://www.msys2.org/
    pause
    exit /b 1
)

echo [Info] Found MSYS2 at: %MSYS2_ROOT%

:: Set up MSYS2 environment
set MSYS2_BIN=%MSYS2_ROOT%\mingw64\bin
set PATH=%MSYS2_BIN%;%PATH%

:: Check if GCC is now available
where gcc >nul 2>&1
if errorlevel 1 (
    echo [Error] GCC still not found in MSYS2 environment
    echo Try installing with: pacman -S mingw-w64-x86_64-toolchain
    pause
    exit /b 1
)

echo [Info] GCC found, version:
gcc --version | head -1

:: Check dependencies
echo [Info] Checking libraries...
if exist "%MSYS2_BIN%\..\lib\libwebsockets.dll.a" (
    echo [Found] libwebsockets
) else (
    echo [Warning] libwebsockets not found
    echo Install with: pacman -S mingw-w64-x86_64-libwebsockets
)

if exist "%MSYS2_BIN%\..\lib\libjson-c.dll.a" (
    echo [Found] json-c
) else (
    echo [Warning] json-c not found  
    echo Install with: pacman -S mingw-w64-x86_64-json-c
)

echo.
echo [Info] Starting compilation...
cd /d "F:\张剑虹\数字人4"

:: Clean old files
if exist digital_human_server.exe del digital_human_server.exe
if exist compile.log del compile.log

:: Compile with detailed output
gcc -Wall -Wextra -std=c99 -O2 -g ^
    -D_WIN32_WINNT=0x0600 ^
    -I"%MSYS2_ROOT%\mingw64\include" ^
    -L"%MSYS2_ROOT%\mingw64\lib" ^
    -o digital_human_server.exe server.c ^
    -lws2_32 -lwebsockets -ljson-c -lpthread 2>&1 | tee compile.log

if exist digital_human_server.exe (
    echo.
    echo [Success] Digital Human Server compiled successfully!
    echo File info:
    dir digital_human_server.exe
    echo.
    
    set /p choice="Start server now? (y/n): "
    if /i "%choice%"=="y" (
        echo [Info] Starting Digital Human Server on port 8080...
        echo Press Ctrl+C to stop server
        echo.
        digital_human_server.exe 8080
    )
) else (
    echo.
    echo [Error] Compilation failed
    echo Check compile.log for details:
    type compile.log
    echo.
    echo Common solutions:
    echo 1. Install missing libraries:
    echo    pacman -S mingw-w64-x86_64-libwebsockets mingw-w64-x86_64-json-c
    echo 2. Update MSYS2: pacman -Syu
    echo 3. Reinstall toolchain: pacman -S mingw-w64-x86_64-toolchain
)

pause