/**
 * 性能监控器
 * 实时监控数字人系统的性能指标，优化用户体验
 */

class PerformanceMonitor {
    constructor() {
        this.metrics = {
            fps: { current: 0, average: 0, min: Infinity, max: 0, history: [] },
            memory: { used: 0, total: 0, limit: 0, percentage: 0 },
            rendering: { drawCalls: 0, triangles: 0, textures: 0 },
            network: { requests: 0, totalTime: 0, averageTime: 0 },
            system: { cpuUsage: 0, batteryLevel: 0, connectionType: 'unknown' }
        };
        
        this.thresholds = {
            fps: { warning: 30, critical: 15 },
            memory: { warning: 70, critical: 85 }, // percentage
            responseTime: { warning: 500, critical: 1000 } // ms
        };
        
        this.isMonitoring = false;
        this.monitoringInterval = null;
        this.lastFrameTime = performance.now();
        this.frameCount = 0;
        
        // 性能警告回调
        this.onWarning = null;
        this.onCritical = null;
        
        // 数据收集
        this.dataPoints = {
            fps: [],
            memory: [],
            responseTime: []
        };
        
        this.init();
    }
    
    init() {
        console.log('📊 性能监控器初始化...');
        
        // 检测浏览器性能API支持
        this.checkPerformanceSupport();
        
        // 初始化网络监控
        this.initNetworkMonitoring();
        
        // 初始化系统信息监控
        this.initSystemMonitoring();
        
        console.log('✅ 性能监控器初始化完成');
    }
    
    checkPerformanceSupport() {
        this.support = {
            performance: 'performance' in window,
            memory: 'memory' in performance,
            navigation: 'navigation' in performance,
            observer: 'PerformanceObserver' in window,
            battery: 'getBattery' in navigator,
            connection: 'connection' in navigator
        };
        
        console.log('🔍 性能API支持情况:', this.support);
    }
    
    /**
     * 开始监控
     */
    startMonitoring(digitalHuman = null) {
        if (this.isMonitoring) return;
        
        this.isMonitoring = true;
        this.digitalHuman = digitalHuman;
        
        console.log('🚀 开始性能监控...');
        
        // FPS监控
        this.startFPSMonitoring();
        
        // 内存监控
        this.startMemoryMonitoring();
        
        // 渲染性能监控
        this.startRenderingMonitoring();
        
        // 定期数据收集
        this.monitoringInterval = setInterval(() => {
            this.collectMetrics();
            this.analyzePerformance();
        }, 1000);
        
        // 性能观察器
        this.initPerformanceObserver();
    }
    
    /**
     * 停止监控
     */
    stopMonitoring() {
        if (!this.isMonitoring) return;
        
        this.isMonitoring = false;
        
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        if (this.fpsMonitor) {
            cancelAnimationFrame(this.fpsMonitor);
        }
        
        console.log('⏹️ 性能监控已停止');
    }
    
    /**
     * FPS监控
     */
    startFPSMonitoring() {
        let frames = 0;
        let lastTime = performance.now();
        
        const measureFPS = (currentTime) => {
            frames++;
            
            if (currentTime - lastTime >= 1000) {
                const fps = Math.round((frames * 1000) / (currentTime - lastTime));
                this.updateFPS(fps);
                
                frames = 0;
                lastTime = currentTime;
            }
            
            if (this.isMonitoring) {
                this.fpsMonitor = requestAnimationFrame(measureFPS);
            }
        };
        
        this.fpsMonitor = requestAnimationFrame(measureFPS);
    }
    
    updateFPS(fps) {
        const fpsMetrics = this.metrics.fps;
        
        fpsMetrics.current = fps;
        fpsMetrics.history.push({ time: Date.now(), value: fps });
        
        // 保持历史记录在合理范围内
        if (fpsMetrics.history.length > 60) {
            fpsMetrics.history = fpsMetrics.history.slice(-30);
        }
        
        // 计算统计数据
        fpsMetrics.min = Math.min(fpsMetrics.min, fps);
        fpsMetrics.max = Math.max(fpsMetrics.max, fps);
        
        const recent = fpsMetrics.history.slice(-10);
        fpsMetrics.average = recent.reduce((sum, item) => sum + item.value, 0) / recent.length;
        
        // 性能警告检查
        this.checkFPSThresholds(fps);
    }
    
    checkFPSThresholds(fps) {
        if (fps <= this.thresholds.fps.critical) {
            this.triggerAlert('critical', 'fps', `FPS严重不足: ${fps}`);
        } else if (fps <= this.thresholds.fps.warning) {
            this.triggerAlert('warning', 'fps', `FPS偏低: ${fps}`);
        }
    }
    
    /**
     * 内存监控
     */
    startMemoryMonitoring() {
        if (!this.support.memory) {
            console.warn('⚠️ 浏览器不支持内存监控');
            return;
        }
        
        setInterval(() => {
            const memory = performance.memory;
            
            this.metrics.memory = {
                used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
                total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
                limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024), // MB
                percentage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100)
            };
            
            // 内存警告检查
            this.checkMemoryThresholds();
            
        }, 2000);
    }
    
    checkMemoryThresholds() {
        const memoryPercentage = this.metrics.memory.percentage;
        
        if (memoryPercentage >= this.thresholds.memory.critical) {
            this.triggerAlert('critical', 'memory', `内存使用率过高: ${memoryPercentage}%`);
        } else if (memoryPercentage >= this.thresholds.memory.warning) {
            this.triggerAlert('warning', 'memory', `内存使用率偏高: ${memoryPercentage}%`);
        }
    }
    
    /**
     * 渲染性能监控
     */
    startRenderingMonitoring() {
        if (!this.digitalHuman || !this.digitalHuman.renderer) return;
        
        setInterval(() => {
            const renderer = this.digitalHuman.renderer;
            if (renderer.info) {
                this.metrics.rendering = {
                    drawCalls: renderer.info.render.calls,
                    triangles: renderer.info.render.triangles,
                    textures: renderer.info.memory.textures,
                    geometries: renderer.info.memory.geometries
                };
            }
        }, 3000);
    }
    
    /**
     * 网络监控初始化
     */
    initNetworkMonitoring() {
        // 监控XMLHttpRequest和Fetch
        this.monitorNetworkRequests();
        
        // 连接类型监控
        if (this.support.connection) {
            const connection = navigator.connection;
            this.metrics.system.connectionType = connection.effectiveType || connection.type || 'unknown';
            
            connection.addEventListener('change', () => {
                this.metrics.system.connectionType = connection.effectiveType || connection.type || 'unknown';
            });
        }
    }
    
    monitorNetworkRequests() {
        const originalFetch = window.fetch;
        const monitor = this;
        
        window.fetch = async function(...args) {
            const startTime = performance.now();
            monitor.metrics.network.requests++;
            
            try {
                const response = await originalFetch.apply(this, args);
                const endTime = performance.now();
                const duration = endTime - startTime;
                
                monitor.updateNetworkMetrics(duration);
                
                return response;
            } catch (error) {
                const endTime = performance.now();
                const duration = endTime - startTime;
                monitor.updateNetworkMetrics(duration, true);
                throw error;
            }
        };
    }
    
    updateNetworkMetrics(duration, isError = false) {
        this.metrics.network.totalTime += duration;
        this.metrics.network.averageTime = this.metrics.network.totalTime / this.metrics.network.requests;
        
        if (duration > this.thresholds.responseTime.critical) {
            this.triggerAlert('critical', 'network', `网络响应时间过长: ${Math.round(duration)}ms`);
        } else if (duration > this.thresholds.responseTime.warning) {
            this.triggerAlert('warning', 'network', `网络响应时间偏长: ${Math.round(duration)}ms`);
        }
    }
    
    /**
     * 系统监控初始化
     */
    initSystemMonitoring() {
        // 电池监控
        if (this.support.battery) {
            navigator.getBattery().then(battery => {
                this.metrics.system.batteryLevel = Math.round(battery.level * 100);
                
                battery.addEventListener('levelchange', () => {
                    this.metrics.system.batteryLevel = Math.round(battery.level * 100);
                });
            });
        }
    }
    
    /**
     * 性能观察器
     */
    initPerformanceObserver() {
        if (!this.support.observer) return;
        
        try {
            // 监控长任务
            const longTaskObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.duration > 50) { // 超过50ms的任务
                        this.triggerAlert('warning', 'performance', 
                            `检测到长任务: ${Math.round(entry.duration)}ms`);
                    }
                }
            });
            
            longTaskObserver.observe({ entryTypes: ['longtask'] });
            
            // 监控内存泄漏
            const measureObserver = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    if (entry.name === 'measure' && entry.duration > 100) {
                        console.warn('长时间测量:', entry);
                    }
                }
            });
            
            measureObserver.observe({ entryTypes: ['measure'] });
            
        } catch (error) {
            console.warn('性能观察器初始化失败:', error);
        }
    }
    
    /**
     * 收集指标
     */
    collectMetrics() {
        const timestamp = Date.now();
        
        // 收集FPS数据点
        this.dataPoints.fps.push({
            timestamp,
            value: this.metrics.fps.current
        });
        
        // 收集内存数据点
        this.dataPoints.memory.push({
            timestamp,
            value: this.metrics.memory.percentage
        });
        
        // 收集响应时间数据点
        this.dataPoints.responseTime.push({
            timestamp,
            value: this.metrics.network.averageTime
        });
        
        // 保持数据点在合理范围内
        Object.keys(this.dataPoints).forEach(key => {
            if (this.dataPoints[key].length > 100) {
                this.dataPoints[key] = this.dataPoints[key].slice(-50);
            }
        });
    }
    
    /**
     * 性能分析
     */
    analyzePerformance() {
        const analysis = {
            overall: 'good',
            issues: [],
            recommendations: []
        };
        
        // FPS分析
        if (this.metrics.fps.average < 30) {
            analysis.overall = 'poor';
            analysis.issues.push('FPS过低');
            analysis.recommendations.push('降低渲染质量或模型复杂度');
        }
        
        // 内存分析
        if (this.metrics.memory.percentage > 80) {
            analysis.overall = 'poor';
            analysis.issues.push('内存使用率过高');
            analysis.recommendations.push('清理不必要的资源或重启应用');
        }
        
        // 网络分析
        if (this.metrics.network.averageTime > 1000) {
            analysis.overall = analysis.overall === 'good' ? 'fair' : analysis.overall;
            analysis.issues.push('网络响应缓慢');
            analysis.recommendations.push('检查网络连接或使用缓存');
        }
        
        this.lastAnalysis = analysis;
        return analysis;
    }
    
    /**
     * 触发警告
     */
    triggerAlert(level, type, message) {
        const alert = {
            level,
            type,
            message,
            timestamp: Date.now(),
            metrics: { ...this.metrics }
        };
        
        console.warn(`⚠️ 性能警告 [${level}/${type}]: ${message}`);
        
        if (level === 'critical' && this.onCritical) {
            this.onCritical(alert);
        } else if (level === 'warning' && this.onWarning) {
            this.onWarning(alert);
        }
        
        // 触发自定义事件
        window.dispatchEvent(new CustomEvent('performanceAlert', {
            detail: alert
        }));
    }
    
    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        return {
            timestamp: Date.now(),
            metrics: { ...this.metrics },
            analysis: this.lastAnalysis || this.analyzePerformance(),
            dataPoints: { ...this.dataPoints },
            support: { ...this.support },
            uptime: this.isMonitoring ? Date.now() - this.startTime : 0
        };
    }
    
    /**
     * 获取优化建议
     */
    getOptimizationSuggestions() {
        const suggestions = [];
        
        // FPS优化建议
        if (this.metrics.fps.average < 30) {
            suggestions.push({
                type: 'rendering',
                priority: 'high',
                title: '渲染性能优化',
                description: '降低模型复杂度或减少特效',
                actions: [
                    '降低材质质量',
                    '减少灯光数量',
                    '使用LOD技术',
                    '优化几何体'
                ]
            });
        }
        
        // 内存优化建议
        if (this.metrics.memory.percentage > 70) {
            suggestions.push({
                type: 'memory',
                priority: 'medium',
                title: '内存使用优化',
                description: '减少内存占用',
                actions: [
                    '释放未使用的纹理',
                    '优化几何体缓存',
                    '使用对象池',
                    '定期垃圾回收'
                ]
            });
        }
        
        // 网络优化建议
        if (this.metrics.network.averageTime > 500) {
            suggestions.push({
                type: 'network',
                priority: 'low',
                title: '网络性能优化',
                description: '改善网络请求效率',
                actions: [
                    '启用资源缓存',
                    '压缩传输数据',
                    '使用CDN',
                    '批量处理请求'
                ]
            });
        }
        
        return suggestions;
    }
    
    /**
     * 导出性能数据
     */
    exportData(format = 'json') {
        const data = this.getPerformanceReport();
        
        switch (format) {
            case 'json':
                return JSON.stringify(data, null, 2);
            case 'csv':
                return this.convertToCSV(data);
            default:
                return data;
        }
    }
    
    convertToCSV(data) {
        const fps = data.dataPoints.fps;
        let csv = 'Timestamp,FPS,Memory%,ResponseTime\n';
        
        fps.forEach((point, index) => {
            const memory = data.dataPoints.memory[index] || { value: 0 };
            const response = data.dataPoints.responseTime[index] || { value: 0 };
            
            csv += `${new Date(point.timestamp).toISOString()},${point.value},${memory.value},${response.value}\n`;
        });
        
        return csv;
    }
    
    /**
     * 重置监控数据
     */
    reset() {
        this.metrics.fps = { current: 0, average: 0, min: Infinity, max: 0, history: [] };
        this.metrics.network = { requests: 0, totalTime: 0, averageTime: 0 };
        this.dataPoints = { fps: [], memory: [], responseTime: [] };
        
        console.log('🔄 性能监控数据已重置');
    }
    
    /**
     * 销毁监控器
     */
    destroy() {
        this.stopMonitoring();
        
        // 恢复原始方法
        if (this.originalFetch) {
            window.fetch = this.originalFetch;
        }
        
        console.log('🔄 性能监控器已销毁');
    }
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceMonitor;
} else {
    window.PerformanceMonitor = PerformanceMonitor;
}