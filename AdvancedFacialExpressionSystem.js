/**
 * 高级面部表情系统 - 企业级实时面部表情控制
 * 基于FACS (Facial Action Coding System) 和最新AI表情技术
 * 支持微表情、情感分析、实时响应、文化适应
 */

class AdvancedFacialExpressionSystem {
    constructor(digitalHumanModel) {
        this.model = digitalHumanModel;
        
        // 核心组件
        this.facsController = new FACSController(digitalHumanModel);
        this.emotionEngine = new EmotionEngine();
        this.microExpressionDetector = new MicroExpressionDetector();
        this.contextAnalyzer = new ContextAnalyzer();
        this.culturalAdapter = new CulturalAdapter();
        this.performanceOptimizer = new PerformanceOptimizer();
        
        // 状态管理
        this.currentEmotion = { type: 'neutral', intensity: 0.0, valence: 0.0, arousal: 0.0 };
        this.activeExpressions = new Map();
        this.expressionQueue = [];
        this.isProcessing = false;
        
        // 配置参数 - 性能优化版本
        this.config = {
            enableMicroExpressions: false, // 禁用微表情以提升性能
            enableContextAwareness: false, // 禁用上下文感知
            enableCulturalAdaptation: false, // 禁用文化适配
            enablePredictiveAnimation: false, // 禁用预测动画
            responseLatency: 300, // 增加延迟以减少计算频率
            transitionDuration: 1200, // 增加过渡时间以减少更新频率
            expressionIntensity: 0.6, // 降低表情强度
            businessMode: true,
            performanceMode: true // 启用性能模式
        };
        
        // 性能监控
        this.performance = {
            avgProcessingTime: 0,
            frameRate: 60,
            lastUpdate: performance.now()
        };
        
        this.init();
    }
    
    /**
     * 初始化面部表情系统
     */
    init() {
        this.loadExpressionTemplates();
        this.initializeEmotionEngine();
        
        // 延迟启动性能监控，等待模型加载
        setTimeout(() => {
            this.startPerformanceMonitoring();
        }, 3000);
        
        console.log('✅ 高级面部表情系统初始化完成');
    }
    
    /**
     * 当模型加载完成后调用
     */
    onModelLoaded(model) {
        this.model = model;
        this.isActive = true;
        this.setupMorphTargets();
        
        // 停止之前的监控，重新开始
        if (this.performanceMonitor) {
            clearInterval(this.performanceMonitor);
        }
        this.startPerformanceMonitoring();
        
        console.log('✅ 面部表情系统已连接到模型');
    }
    
    /**
     * 设置面部变形目标
     */
    setupMorphTargets() {
        this.morphTargets = new Map();
        
        if (!this.model) return;
        
        this.model.traverse((child) => {
            if (child.isMesh && child.morphTargetDictionary) {
                const influences = child.morphTargetInfluences;
                if (influences) {
                    Object.entries(child.morphTargetDictionary).forEach(([name, index]) => {
                        this.morphTargets.set(name, {
                            mesh: child,
                            index: index,
                            currentValue: 0,
                            targetValue: 0,
                            velocity: 0
                        });
                    });
                }
            }
        });
        
        console.log(`🎭 发现 ${this.morphTargets.size} 个面部变形目标`);
    }
    
    /**
     * 加载表情模板
     */
    loadExpressionTemplates() {
        this.expressionTemplates = {
            // 基础情感表情
            happiness: new EmotionTemplate('happiness', [
                { au: 'AU6', intensity: 0.8 },  // 面颊上提
                { au: 'AU12', intensity: 0.9 }, // 嘴角上扬
                { au: 'AU25', intensity: 0.3 }, // 嘴唇微分
                { au: 'AU26', intensity: 0.1 }  // 下颌轻微下垂
            ]),
            
            sadness: new EmotionTemplate('sadness', [
                { au: 'AU1', intensity: 0.6 },  // 内眉上扬
                { au: 'AU4', intensity: 0.4 },  // 眉毛下压
                { au: 'AU15', intensity: 0.7 }, // 嘴角下压
                { au: 'AU17', intensity: 0.3 }  // 下巴上提
            ]),
            
            surprise: new EmotionTemplate('surprise', [
                { au: 'AU1', intensity: 0.8 },  // 内眉上扬
                { au: 'AU2', intensity: 0.8 },  // 外眉上扬
                { au: 'AU5', intensity: 0.9 },  // 上眼睑提升
                { au: 'AU25', intensity: 0.6 }, // 嘴唇分开
                { au: 'AU26', intensity: 0.8 }  // 下颌下垂
            ]),
            
            anger: new EmotionTemplate('anger', [
                { au: 'AU4', intensity: 0.9 },  // 眉毛下压
                { au: 'AU7', intensity: 0.6 },  // 眼睑收紧
                { au: 'AU23', intensity: 0.5 }, // 唇部收紧
                { au: 'AU24', intensity: 0.4 }  // 唇部按压
            ]),
            
            fear: new EmotionTemplate('fear', [
                { au: 'AU1', intensity: 0.7 },  // 内眉上扬
                { au: 'AU2', intensity: 0.5 },  // 外眉上扬
                { au: 'AU4', intensity: 0.3 },  // 眉毛轻微下压
                { au: 'AU5', intensity: 0.8 },  // 上眼睑提升
                { au: 'AU20', intensity: 0.6 }  // 唇部拉伸
            ]),
            
            disgust: new EmotionTemplate('disgust', [
                { au: 'AU9', intensity: 0.7 },  // 鼻翼皱起
                { au: 'AU10', intensity: 0.6 }, // 上唇提升
                { au: 'AU25', intensity: 0.3 }, // 嘴唇轻微分开
                { au: 'AU26', intensity: 0.2 }  // 下颌轻微下垂
            ]),
            
            // 企业级专业表情
            professional_smile: new EmotionTemplate('professional_smile', [
                { au: 'AU6', intensity: 0.4 },  // 面颊轻微上提
                { au: 'AU12', intensity: 0.6 }, // 嘴角适度上扬
                { au: 'AU25', intensity: 0.2 }  // 嘴唇轻微分开
            ]),
            
            attentive_listening: new EmotionTemplate('attentive_listening', [
                { au: 'AU1', intensity: 0.3 },  // 内眉轻微上扬
                { au: 'AU5', intensity: 0.2 },  // 上眼睑轻微提升
                { au: 'AU43', intensity: 0.4 }  // 眼睛闭合（专注）
            ]),
            
            confident_speaking: new EmotionTemplate('confident_speaking', [
                { au: 'AU2', intensity: 0.3 },  // 外眉轻微上扬
                { au: 'AU12', intensity: 0.4 }, // 嘴角轻微上扬
                { au: 'AU25', intensity: 0.5 }, // 嘴唇分开（说话）
                { au: 'AU26', intensity: 0.3 }  // 下颌适度下垂
            ]),
            
            thinking: new EmotionTemplate('thinking', [
                { au: 'AU1', intensity: 0.2 },  // 内眉轻微上扬
                { au: 'AU4', intensity: 0.3 },  // 眉毛轻微下压
                { au: 'AU7', intensity: 0.2 },  // 眼睑轻微收紧
                { au: 'AU23', intensity: 0.2 }  // 唇部轻微收紧
            ]),
            
            // 微表情模板
            micro_doubt: new MicroExpressionTemplate('micro_doubt', [
                { au: 'AU4', intensity: 0.15, duration: 200 },
                { au: 'AU7', intensity: 0.1, duration: 150 }
            ]),
            
            micro_interest: new MicroExpressionTemplate('micro_interest', [
                { au: 'AU1', intensity: 0.2, duration: 300 },
                { au: 'AU5', intensity: 0.15, duration: 250 }
            ])
        };
    }
    
    /**
     * 初始化情感引擎
     */
    initializeEmotionEngine() {
        this.emotionEngine.setBusinessMode(this.config.businessMode);
        this.emotionEngine.setCulturalContext('business_chinese');
        this.emotionEngine.setPersonalityProfile({
            openness: 0.7,
            conscientiousness: 0.8,
            extraversion: 0.6,
            agreeableness: 0.8,
            neuroticism: 0.2
        });
    }
    
    /**
     * 分析文本内容并生成适当表情
     */
    async analyzeAndExpressFromText(text, context = {}) {
        const startTime = performance.now();
        
        try {
            // 1. 情感分析
            const emotionAnalysis = await this.emotionEngine.analyzeText(text, context);
            
            // 2. 上下文分析
            const contextualInfo = this.contextAnalyzer.analyze(text, context);
            
            // 3. 文化适应
            const culturalAdjustment = this.culturalAdapter.adjust(emotionAnalysis, contextualInfo);
            
            // 4. 生成表情序列
            const expressionSequence = this.generateExpressionSequence(
                culturalAdjustment,
                contextualInfo
            );
            
            // 5. 预测性动画
            if (this.config.enablePredictiveAnimation) {
                this.predictiveAnimate(text, expressionSequence);
            }
            
            // 6. 执行表情
            await this.executeExpressionSequence(expressionSequence);
            
            // 7. 更新性能统计
            this.updatePerformanceStats(startTime);
            
            return {
                success: true,
                emotion: emotionAnalysis,
                expressions: expressionSequence,
                processingTime: performance.now() - startTime
            };
            
        } catch (error) {
            console.error('表情分析执行错误:', error);
            return { success: false, error: error.message };
        }
    }
    
    /**
     * 实时情感表达
     */
    expressEmotion(emotionType, intensity = 0.8, options = {}) {
        const expression = this.expressionTemplates[emotionType];
        if (!expression) {
            console.warn(`未找到表情模板: ${emotionType}`);
            return;
        }
        
        const adjustedIntensity = this.config.businessMode ? 
            Math.min(intensity * 0.7, 0.8) : intensity;
        
        this.queueExpression({
            template: expression,
            intensity: adjustedIntensity,
            duration: options.duration || this.config.transitionDuration,
            priority: options.priority || 'normal',
            blendMode: options.blendMode || 'replace'
        });
    }
    
    /**
     * 播放微表情
     */
    playMicroExpression(microType, options = {}) {
        if (!this.config.enableMicroExpressions) return;
        
        const microTemplate = this.expressionTemplates[`micro_${microType}`];
        if (!microTemplate) return;
        
        this.queueExpression({
            template: microTemplate,
            intensity: options.intensity || 0.3,
            duration: microTemplate.duration || 200,
            priority: 'high',
            blendMode: 'additive'
        });
    }
    
    /**
     * 生成表情序列
     */
    generateExpressionSequence(emotionAnalysis, contextInfo) {
        const sequence = [];
        const baseEmotion = emotionAnalysis.primaryEmotion;
        const intensity = emotionAnalysis.intensity;
        
        // 主表情
        if (this.expressionTemplates[baseEmotion]) {
            sequence.push({
                type: 'primary',
                template: this.expressionTemplates[baseEmotion],
                intensity: intensity,
                timing: 0,
                duration: this.config.transitionDuration
            });
        }
        
        // 添加微表情
        if (this.config.enableMicroExpressions) {
            const microExpressions = this.generateMicroExpressions(emotionAnalysis, contextInfo);
            sequence.push(...microExpressions);
        }
        
        // 添加说话表情
        if (contextInfo.isSpeaking) {
            sequence.push({
                type: 'speaking',
                template: this.expressionTemplates.confident_speaking,
                intensity: 0.6,
                timing: 100,
                duration: contextInfo.speechDuration || 2000
            });
        }
        
        return sequence;
    }
    
    /**
     * 生成微表情
     */
    generateMicroExpressions(emotionAnalysis, contextInfo) {
        const microExpressions = [];
        const confidence = emotionAnalysis.confidence;
        
        // 低置信度时添加疑虑微表情
        if (confidence < 0.7) {
            microExpressions.push({
                type: 'micro',
                template: this.expressionTemplates.micro_doubt,
                intensity: 0.2,
                timing: 500,
                duration: 200
            });
        }
        
        // 问句时添加兴趣微表情
        if (contextInfo.isQuestion) {
            microExpressions.push({
                type: 'micro',
                template: this.expressionTemplates.micro_interest,
                intensity: 0.25,
                timing: 200,
                duration: 300
            });
        }
        
        return microExpressions;
    }
    
    /**
     * 执行表情序列
     */
    async executeExpressionSequence(sequence) {
        for (const expressionStep of sequence) {
            setTimeout(() => {
                this.applyExpressionTemplate(
                    expressionStep.template,
                    expressionStep.intensity,
                    expressionStep.duration
                );
            }, expressionStep.timing);
        }
    }
    
    /**
     * 应用表情模板
     */
    applyExpressionTemplate(template, intensity, duration) {
        if (!template || !template.actionUnits) return;
        
        const startTime = performance.now();
        const initialStates = new Map();
        
        // 记录初始状态
        template.actionUnits.forEach(({ au, intensity: auIntensity }) => {
            const morphName = this.auToMorphName(au);
            const morphTarget = this.morphTargets.get(morphName);
            
            if (morphTarget) {
                initialStates.set(morphName, morphTarget.currentValue);
                morphTarget.targetValue = auIntensity * intensity;
            }
        });
        
        // 平滑动画过渡
        const animate = () => {
            const elapsed = performance.now() - startTime;
            const progress = Math.min(elapsed / duration, 1.0);
            const easeProgress = this.easeInOutCubic(progress);
            
            template.actionUnits.forEach(({ au, intensity: auIntensity }) => {
                const morphName = this.auToMorphName(au);
                const morphTarget = this.morphTargets.get(morphName);
                const initialValue = initialStates.get(morphName) || 0;
                
                if (morphTarget) {
                    const targetValue = auIntensity * intensity;
                    morphTarget.currentValue = initialValue + (targetValue - initialValue) * easeProgress;
                    morphTarget.mesh.morphTargetInfluences[morphTarget.index] = morphTarget.currentValue;
                }
            });
            
            if (progress < 1.0) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
    
    /**
     * 动作单元到变形目标名称映射
     */
    auToMorphName(actionUnit) {
        const auMapping = {
            'AU1': 'brow_inner_up',
            'AU2': 'brow_outer_up',
            'AU4': 'brow_down',
            'AU5': 'eye_wide',
            'AU6': 'cheek_squint',
            'AU7': 'eye_squint',
            'AU9': 'nose_sneer',
            'AU10': 'mouth_upper_up',
            'AU12': 'mouth_smile',
            'AU15': 'mouth_frown',
            'AU17': 'mouth_dimple',
            'AU20': 'mouth_stretch',
            'AU23': 'mouth_tighten',
            'AU24': 'mouth_press',
            'AU25': 'mouth_open',
            'AU26': 'jaw_open',
            'AU43': 'eye_close'
        };
        
        return auMapping[actionUnit] || actionUnit.toLowerCase();
    }
    
    /**
     * 预测性动画
     */
    predictiveAnimate(text, expressionSequence) {
        // 基于文本内容预测即将到来的情感变化
        const words = text.split(' ');
        const emotionalWords = this.emotionEngine.extractEmotionalWords(words);
        
        emotionalWords.forEach((word, index) => {
            const delay = (index + 1) * 500; // 根据单词位置计算延迟
            const predictedEmotion = this.emotionEngine.predictEmotionFromWord(word);
            
            if (predictedEmotion) {
                setTimeout(() => {
                    this.expressEmotion(predictedEmotion.type, predictedEmotion.intensity * 0.3, {
                        duration: 300,
                        blendMode: 'additive'
                    });
                }, delay);
            }
        });
    }
    
    /**
     * 队列表情
     */
    queueExpression(expressionConfig) {
        this.expressionQueue.push({
            ...expressionConfig,
            timestamp: performance.now(),
            id: Math.random().toString(36).substr(2, 9)
        });
        
        if (!this.isProcessing) {
            this.processExpressionQueue();
        }
    }
    
    /**
     * 处理表情队列
     */
    async processExpressionQueue() {
        this.isProcessing = true;
        
        while (this.expressionQueue.length > 0) {
            // 按优先级排序
            this.expressionQueue.sort((a, b) => {
                const priorityOrder = { 'high': 3, 'normal': 2, 'low': 1 };
                return priorityOrder[b.priority] - priorityOrder[a.priority];
            });
            
            const expression = this.expressionQueue.shift();
            
            try {
                await this.executeExpression(expression);
            } catch (error) {
                console.error('表情执行错误:', error);
            }
            
            // 避免阻塞
            await new Promise(resolve => setTimeout(resolve, 50));
        }
        
        this.isProcessing = false;
    }
    
    /**
     * 执行单个表情
     */
    async executeExpression(expressionConfig) {
        const { template, intensity, duration, blendMode } = expressionConfig;
        
        if (blendMode === 'additive') {
            // 叠加模式：在现有表情基础上添加
            this.applyAdditiveExpression(template, intensity, duration);
        } else {
            // 替换模式：替换当前表情
            this.applyExpressionTemplate(template, intensity, duration);
        }
        
        return new Promise(resolve => setTimeout(resolve, duration));
    }
    
    /**
     * 应用叠加表情
     */
    applyAdditiveExpression(template, intensity, duration) {
        const startTime = performance.now();
        
        const animate = () => {
            const elapsed = performance.now() - startTime;
            const progress = Math.min(elapsed / duration, 1.0);
            
            // 使用正弦波形进行叠加动画
            const additiveFactor = Math.sin(progress * Math.PI) * intensity;
            
            template.actionUnits.forEach(({ au, intensity: auIntensity }) => {
                const morphName = this.auToMorphName(au);
                const morphTarget = this.morphTargets.get(morphName);
                
                if (morphTarget) {
                    const additiveValue = auIntensity * additiveFactor;
                    const newValue = Math.max(0, Math.min(1, morphTarget.currentValue + additiveValue));
                    morphTarget.mesh.morphTargetInfluences[morphTarget.index] = newValue;
                }
            });
            
            if (progress < 1.0) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
    
    /**
     * 重置表情到中性状态
     */
    resetToNeutral(duration = 1000) {
        const startTime = performance.now();
        const initialStates = new Map();
        
        // 记录当前状态
        this.morphTargets.forEach((target, name) => {
            initialStates.set(name, target.currentValue);
        });
        
        const animate = () => {
            const elapsed = performance.now() - startTime;
            const progress = Math.min(elapsed / duration, 1.0);
            const easeProgress = this.easeInOutCubic(progress);
            
            this.morphTargets.forEach((target, name) => {
                const initialValue = initialStates.get(name);
                target.currentValue = initialValue * (1 - easeProgress);
                target.mesh.morphTargetInfluences[target.index] = target.currentValue;
            });
            
            if (progress < 1.0) {
                requestAnimationFrame(animate);
            }
        };
        
        animate();
    }
    
    /**
     * 平滑过渡函数
     */
    easeInOutCubic(t) {
        return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;
    }
    
    /**
     * 开始性能监控
     */
    startPerformanceMonitoring() {
        // 只有在模型加载后才开始监控
        if (!this.model) {
            console.log('⏳ 等待模型加载完成后开始性能监控');
            return;
        }
        
        this.performanceMonitor = setInterval(() => {
            // 只有在有活动表情时才进行监控
            if (!this.isActive) return;
            
            const currentTime = performance.now();
            const deltaTime = currentTime - this.performance.lastUpdate;
            
            // 防止异常的deltaTime值
            if (deltaTime > 0 && deltaTime < 1000) {
                this.performance.frameRate = 1000 / deltaTime;
                this.performance.lastUpdate = currentTime;
                
                // 性能预警 - 只在真正需要时触发
                if (this.performance.frameRate < 30 && this.hasActiveExpressions()) {
                    console.warn('⚠️ 面部表情系统性能预警: FPS低于30');
                    this.performanceOptimizer.optimize();
                    
                    // 自动降级到超低性能模式
                    if (this.performance.frameRate < 15) {
                        this.enableUltraPerformanceMode();
                    }
                }
            }
        }, 2000); // 降低监控频率
    }
    
    /**
     * 检查是否有活动的表情
     */
    hasActiveExpressions() {
        return this.activeExpressions && this.activeExpressions.size > 0;
    }
    
    /**
     * 更新性能统计
     */
    updatePerformanceStats(startTime) {
        const processingTime = performance.now() - startTime;
        this.performance.avgProcessingTime = 
            (this.performance.avgProcessingTime * 0.9) + (processingTime * 0.1);
    }
    
    /**
     * 获取系统状态
     */
    getSystemStatus() {
        return {
            currentEmotion: this.currentEmotion,
            activeExpressions: this.activeExpressions.size,
            queueLength: this.expressionQueue.length,
            performance: this.performance,
            morphTargetsCount: this.morphTargets.size,
            isProcessing: this.isProcessing
        };
    }
    
    /**
     * 设置配置
     */
    setConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        
        if (newConfig.businessMode !== undefined) {
            this.emotionEngine.setBusinessMode(newConfig.businessMode);
        }
    }
    
    /**
     * 启用超低性能模式
     */
    enableUltraPerformanceMode() {
        console.log('🚨 启用超低性能模式 - 禁用所有高级功能');
        
        this.config = {
            ...this.config,
            enableMicroExpressions: false,
            enableContextAwareness: false,
            enableCulturalAdaptation: false,
            enablePredictiveAnimation: false,
            responseLatency: 500, // 进一步增加延迟
            transitionDuration: 2000, // 更长的过渡时间
            expressionIntensity: 0.3, // 更低的强度
            performanceMode: true,
            ultraPerformanceMode: true
        };
        
        // 清空表情队列以减少负载
        this.expressionQueue = [];
        
        // 重置到中性状态
        this.resetToNeutral(500);
        
        console.log('✅ 超低性能模式已启用');
    }
    
    /**
     * 销毁系统
     */
    destroy() {
        this.expressionQueue = [];
        this.isProcessing = false;
        this.morphTargets.clear();
        this.activeExpressions.clear();
        
        if (this.performanceMonitor) {
            clearInterval(this.performanceMonitor);
        }
        
        console.log('🔄 高级面部表情系统已销毁');
    }
}

/**
 * 表情模板类
 */
class EmotionTemplate {
    constructor(name, actionUnits) {
        this.name = name;
        this.actionUnits = actionUnits;
        this.type = 'emotion';
    }
}

/**
 * 微表情模板类
 */
class MicroExpressionTemplate extends EmotionTemplate {
    constructor(name, actionUnits, duration = 200) {
        super(name, actionUnits);
        this.type = 'micro';
        this.duration = duration;
    }
}

/**
 * FACS控制器
 */
class FACSController {
    constructor(model) {
        this.model = model;
        this.actionUnits = new Map();
        this.initializeActionUnits();
    }
    
    initializeActionUnits() {
        // 初始化46个FACS动作单元
        const auDefinitions = {
            'AU1': { name: 'Inner Brow Raiser', muscles: ['frontalis_inner'] },
            'AU2': { name: 'Outer Brow Raiser', muscles: ['frontalis_outer'] },
            'AU4': { name: 'Brow Lowerer', muscles: ['corrugator', 'procerus'] },
            'AU5': { name: 'Upper Lid Raiser', muscles: ['levator_palpebrae'] },
            'AU6': { name: 'Cheek Raiser', muscles: ['orbicularis_oculi'] },
            'AU7': { name: 'Lid Tightener', muscles: ['orbicularis_oculi'] },
            'AU9': { name: 'Nose Wrinkler', muscles: ['levator_labii_alaeque_nasi'] },
            'AU10': { name: 'Upper Lip Raiser', muscles: ['levator_labii_superioris'] },
            'AU12': { name: 'Lip Corner Puller', muscles: ['zygomaticus_major'] },
            'AU15': { name: 'Lip Corner Depressor', muscles: ['depressor_anguli_oris'] },
            'AU17': { name: 'Chin Raiser', muscles: ['mentalis'] },
            'AU20': { name: 'Lip Stretcher', muscles: ['risorius'] },
            'AU23': { name: 'Lip Tightener', muscles: ['orbicularis_oris'] },
            'AU24': { name: 'Lip Pressor', muscles: ['orbicularis_oris'] },
            'AU25': { name: 'Lips Part', muscles: ['depressor_labii_inferioris'] },
            'AU26': { name: 'Jaw Drop', muscles: ['masseter'] },
            'AU43': { name: 'Eyes Closed', muscles: ['orbicularis_oculi'] }
        };
        
        Object.entries(auDefinitions).forEach(([au, definition]) => {
            this.actionUnits.set(au, definition);
        });
    }
    
    activateActionUnit(au, intensity) {
        const definition = this.actionUnits.get(au);
        if (definition) {
            // 激活指定的动作单元
            console.log(`激活 ${au}: ${definition.name} (强度: ${intensity})`);
        }
    }
}

/**
 * 情感引擎
 */
class EmotionEngine {
    constructor() {
        this.businessMode = true;
        this.culturalContext = 'business_chinese';
        this.personalityProfile = null;
        this.emotionHistory = [];
    }
    
    setBusinessMode(enabled) {
        this.businessMode = enabled;
    }
    
    setCulturalContext(context) {
        this.culturalContext = context;
    }
    
    setPersonalityProfile(profile) {
        this.personalityProfile = profile;
    }
    
    async analyzeText(text, context = {}) {
        // 简化的情感分析逻辑
        const emotionalKeywords = {
            happiness: ['高兴', '开心', '快乐', '满意', '好的', '谢谢', '太棒了'],
            sadness: ['难过', '悲伤', '失望', '遗憾', '不好', '糟糕'],
            surprise: ['惊讶', '意外', '没想到', '真的吗', '哇'],
            anger: ['生气', '愤怒', '讨厌', '烦人', '不满'],
            fear: ['害怕', '担心', '忧虑', '紧张', '不安'],
            disgust: ['恶心', '厌恶', '反感', '不喜欢']
        };
        
        let primaryEmotion = 'neutral';
        let maxScore = 0;
        
        Object.entries(emotionalKeywords).forEach(([emotion, keywords]) => {
            const score = keywords.reduce((sum, keyword) => {
                return sum + (text.includes(keyword) ? 1 : 0);
            }, 0);
            
            if (score > maxScore) {
                maxScore = score;
                primaryEmotion = emotion;
            }
        });
        
        const intensity = this.businessMode ? 
            Math.min(maxScore * 0.3, 0.7) : maxScore * 0.5;
        
        return {
            primaryEmotion,
            intensity,
            confidence: maxScore > 0 ? 0.8 : 0.3,
            valence: this.getValence(primaryEmotion),
            arousal: this.getArousal(primaryEmotion)
        };
    }
    
    getValence(emotion) {
        const valenceMap = {
            happiness: 0.8,
            sadness: -0.7,
            surprise: 0.1,
            anger: -0.6,
            fear: -0.5,
            disgust: -0.8,
            neutral: 0.0
        };
        return valenceMap[emotion] || 0.0;
    }
    
    getArousal(emotion) {
        const arousalMap = {
            happiness: 0.6,
            sadness: 0.2,
            surprise: 0.9,
            anger: 0.8,
            fear: 0.7,
            disgust: 0.5,
            neutral: 0.1
        };
        return arousalMap[emotion] || 0.1;
    }
    
    extractEmotionalWords(words) {
        const emotionalWords = ['爱', '恨', '喜欢', '讨厌', '惊讶', '害怕', '开心', '悲伤'];
        return words.filter(word => emotionalWords.includes(word));
    }
    
    predictEmotionFromWord(word) {
        // 简化的情感预测
        const predictions = {
            '爱': { type: 'happiness', intensity: 0.8 },
            '恨': { type: 'anger', intensity: 0.7 },
            '惊讶': { type: 'surprise', intensity: 0.9 },
            '害怕': { type: 'fear', intensity: 0.6 }
        };
        
        return predictions[word] || null;
    }
}

/**
 * 微表情检测器
 */
class MicroExpressionDetector {
    constructor() {
        this.detectionThreshold = 0.1;
        this.detectionWindow = 500; // 毫秒
    }
    
    detectMicroExpression(emotionChange) {
        // 检测微表情的逻辑
        if (emotionChange.duration < this.detectionWindow && 
            emotionChange.intensity > this.detectionThreshold) {
            return {
                type: 'micro_' + emotionChange.emotion,
                intensity: emotionChange.intensity,
                duration: emotionChange.duration
            };
        }
        return null;
    }
}

/**
 * 上下文分析器
 */
class ContextAnalyzer {
    analyze(text, context) {
        return {
            isSpeaking: context.isSpeaking || false,
            isQuestion: text.includes('？') || text.includes('?'),
            speechDuration: context.speechDuration || this.estimateSpeechDuration(text),
            conversationPhase: context.phase || 'middle',
            topicSentiment: this.analyzeTopic(text)
        };
    }
    
    estimateSpeechDuration(text) {
        // 估算说话时长：中文约每分钟300字
        return (text.length / 300) * 60 * 1000;
    }
    
    analyzeTopic(text) {
        // 简化的话题情感分析
        if (text.includes('产品') || text.includes('服务') || text.includes('公司')) {
            return 'business';
        }
        if (text.includes('问题') || text.includes('帮助')) {
            return 'support';
        }
        return 'general';
    }
}

/**
 * 文化适配器
 */
class CulturalAdapter {
    constructor() {
        this.culturalRules = {
            business_chinese: {
                emotionMultiplier: 0.7, // 中国商务文化中表情较为内敛
                allowedEmotions: ['professional_smile', 'attentive_listening', 'thinking'],
                restrictedEmotions: ['anger', 'disgust', 'fear']
            }
        };
    }
    
    adjust(emotionAnalysis, contextInfo) {
        const rules = this.culturalRules.business_chinese;
        
        // 应用文化调整
        if (rules.restrictedEmotions.includes(emotionAnalysis.primaryEmotion)) {
            return {
                ...emotionAnalysis,
                primaryEmotion: 'professional_smile',
                intensity: emotionAnalysis.intensity * 0.3
            };
        }
        
        return {
            ...emotionAnalysis,
            intensity: emotionAnalysis.intensity * rules.emotionMultiplier
        };
    }
}

/**
 * 性能优化器
 */
class PerformanceOptimizer {
    constructor() {
        this.optimizationLevel = 'medium';
    }
    
    optimize() {
        console.log('🔧 执行性能优化...');
        
        // 降低表情更新频率
        this.reduceUpdateFrequency();
        
        // 简化表情计算
        this.simplifyCalculations();
        
        // 清理内存
        this.cleanupMemory();
    }
    
    reduceUpdateFrequency() {
        // 实现更新频率控制
        console.log('📉 降低表情更新频率');
    }
    
    simplifyCalculations() {
        // 实现计算简化
        console.log('⚡ 简化表情计算');
    }
    
    cleanupMemory() {
        // 实现内存清理
        console.log('🧹 清理内存');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedFacialExpressionSystem;
} else {
    window.AdvancedFacialExpressionSystem = AdvancedFacialExpressionSystem;
}