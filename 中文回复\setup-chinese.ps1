# Claude Code 中文设置脚本 (PowerShell)
Write-Host "正在设置Claude Code中文环境..." -ForegroundColor Green

# 设置环境变量
$env:CLAUDE_LANGUAGE = "zh-CN"
$env:LANG = "zh_CN.UTF-8"
$env:LC_ALL = "zh_CN.UTF-8"
$env:CLAUDE_CODE_LANGUAGE = "chinese"
$env:CLAUDE_DEFAULT_LANGUAGE = "zh-CN"
$env:CLAUDE_RESPONSE_LANGUAGE = "chinese"

# 设置持久化环境变量
[Environment]::SetEnvironmentVariable("CLAUDE_LANGUAGE", "zh-CN", "User")
[Environment]::SetEnvironmentVariable("LANG", "zh_CN.UTF-8", "User")
[Environment]::SetEnvironmentVariable("LC_ALL", "zh_CN.UTF-8", "User")
[Environment]::SetEnvironmentVariable("CLAUDE_CODE_LANGUAGE", "chinese", "User")
[Environment]::SetEnvironmentVariable("CLAUDE_DEFAULT_LANGUAGE", "zh-CN", "User")
[Environment]::SetEnvironmentVariable("CLAUDE_RESPONSE_LANGUAGE", "chinese", "User")

# 创建配置目录
$configPaths = @(
    "$env:USERPROFILE\.config\claude-code",
    "$env:APPDATA\Claude Code",
    "$env:LOCALAPPDATA\Claude Code",
    "$env:USERPROFILE\.claude-code"
)

foreach ($path in $configPaths) {
    if (!(Test-Path $path)) {
        New-Item -ItemType Directory -Path $path -Force | Out-Null
        Write-Host "创建目录: $path" -ForegroundColor Yellow
    }
}

# 复制配置文件
$configFile = "claude-code-settings.json"
$targetFiles = @(
    "$env:USERPROFILE\.config\claude-code\settings.json",
    "$env:APPDATA\Claude Code\settings.json",
    "$env:LOCALAPPDATA\Claude Code\settings.json",
    "$env:USERPROFILE\.claude-code\settings.json",
    "$env:USERPROFILE\.config\claude-code\config.json"
)

if (Test-Path $configFile) {
    foreach ($target in $targetFiles) {
        try {
            Copy-Item $configFile $target -Force
            Write-Host "已复制配置到: $target" -ForegroundColor Green
        } catch {
            Write-Host "复制失败: $target" -ForegroundColor Red
        }
    }
} else {
    Write-Host "警告: 找不到配置文件 $configFile" -ForegroundColor Red
}

# 创建Claude Code启动脚本
$startupScript = @"
# Claude Code 中文启动脚本
`$env:CLAUDE_LANGUAGE = "zh-CN"
`$env:CLAUDE_CODE_LANGUAGE = "chinese"
`$env:CLAUDE_RESPONSE_LANGUAGE = "chinese"

Write-Host "Claude Code 中文环境已激活" -ForegroundColor Green
"@

$startupScript | Out-File -FilePath "$env:USERPROFILE\.claude-code\startup.ps1" -Encoding UTF8

Write-Host "`n设置完成！" -ForegroundColor Green
Write-Host "环境变量已设置，配置文件已复制到多个位置。" -ForegroundColor Green
Write-Host "请重新启动Claude Code以使设置生效。" -ForegroundColor Yellow

# 显示验证信息
Write-Host "`n验证环境变量:" -ForegroundColor Cyan
Write-Host "CLAUDE_LANGUAGE = $env:CLAUDE_LANGUAGE"
Write-Host "CLAUDE_CODE_LANGUAGE = $env:CLAUDE_CODE_LANGUAGE"
Write-Host "CLAUDE_RESPONSE_LANGUAGE = $env:CLAUDE_RESPONSE_LANGUAGE"

Read-Host "`n按任意键继续..."