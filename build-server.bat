@echo off
REM 3D Digital Human WebSocket Server Build Script
REM Windows Environment Automated Build

echo ===========================================
echo 3D Digital Human WebSocket Server Builder
echo ===========================================
echo.

REM Check for compiler
where gcc >nul 2>nul
if %errorlevel% neq 0 (
    echo [ERROR] GCC compiler not found
    echo Please install MinGW-w64 or MSYS2
    echo Download: https://www.msys2.org/
    pause
    exit /b 1
)

REM Check dependencies
echo [INFO] Checking dependencies...

REM Check libwebsockets
pkg-config --exists libwebsockets 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] libwebsockets library not found
    echo Install with vcpkg: vcpkg install libwebsockets
)

REM Check json-c
pkg-config --exists json-c 2>nul
if %errorlevel% neq 0 (
    echo [WARNING] json-c library not found
    echo Install with vcpkg: vcpkg install json-c
)

echo.

REM Set compilation options
set CC=gcc
set CFLAGS=-Wall -Wextra -std=c99 -O2 -g -D_WIN32_WINNT=0x0600
set LIBS=-lws2_32 -lwebsockets -ljson-c
set TARGET=digital_human_server.exe
set SOURCE=server.c

REM Display compilation info
echo [INFO] Build configuration:
echo   Compiler: %CC%
echo   Source: %SOURCE%
echo   Target: %TARGET%
echo   Flags: %CFLAGS%
echo   Libraries: %LIBS%
echo.

REM Start compilation
echo [INFO] Starting compilation...
%CC% %CFLAGS% -o %TARGET% %SOURCE% %LIBS%

if %errorlevel% equ 0 (
    echo.
    echo [SUCCESS] Compilation completed!
    echo Generated file: %TARGET%
    echo.
    
    REM Check file size
    for %%I in (%TARGET%) do echo File size: %%~zI bytes
    echo.
    
    REM Ask if user wants to run immediately
    set /p choice=Run server immediately? (y/n): 
    if /i "%choice%"=="y" (
        echo.
        echo [INFO] Starting Digital Human WebSocket server...
        echo Server address: ws://localhost:8080
        echo Press Ctrl+C to stop server
        echo.
        %TARGET% 8080
    )
) else (
    echo.
    echo [ERROR] Compilation failed!
    echo.
    echo Possible solutions:
    echo 1. Check if dependencies are correctly installed
    echo 2. Verify vcpkg toolchain configuration
    echo 3. Check source code syntax errors
    echo.
    
    REM Show detailed error information
    echo If using vcpkg, ensure you have run:
    echo   vcpkg install libwebsockets json-c
    echo   vcpkg integrate install
    echo.
    echo If using MSYS2, ensure you have installed:
    echo   pacman -S mingw-w64-x86_64-libwebsockets
    echo   pacman -S mingw-w64-x86_64-json-c
    echo.
)

echo.
echo Build script execution completed
pause