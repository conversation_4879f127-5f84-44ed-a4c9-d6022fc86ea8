/**
 * 真实数字人核心类 - 个人版
 * 基于EnterpriseDigitalHuman，简化接口
 */
class RealDigitalHuman {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = typeof containerId === 'string' ? 
            document.getElementById(containerId) : containerId;
        
        if (!this.container) {
            throw new Error(`容器元素未找到: ${containerId}`);
        }
        
        // 使用企业版核心，但简化配置
        this.core = new EnterpriseDigitalHuman(this.container, {
            width: this.container.clientWidth,
            height: this.container.clientHeight,
            modelPath: options.modelPath || './models/RiggedSimple.glb',
            enableAudio: true,
            enableControls: true,
            enableShadows: true,
            onProgress: options.onProgress || this.defaultProgressHandler.bind(this),
            onLoaded: options.onLoaded || this.defaultLoadedHandler.bind(this),
            onError: options.onError || this.defaultErrorHandler.bind(this),
            ...options
        });
        
        this.isInitialized = false;
    }
    
    defaultProgressHandler(progress) {
        console.log(`数字人加载进度: ${progress}%`);
        // 触发自定义事件
        this.dispatchEvent('progress', { progress });
    }
    
    defaultLoadedHandler() {
        this.isInitialized = true;
        console.log('数字人加载完成');
        this.dispatchEvent('loaded', {});
    }
    
    defaultErrorHandler(error) {
        console.error('数字人加载失败:', error);
        this.dispatchEvent('error', { error });
    }
    
    dispatchEvent(eventName, data) {
        const event = new CustomEvent(`digitalHuman.${eventName}`, {
            detail: data
        });
        this.container.dispatchEvent(event);
    }
    
    // 简化的API方法
    speak(text, options = {}) {
        if (this.core) {
            return this.core.speak(text, options);
        }
    }
    
    setEmotion(emotion, intensity = 1) {
        if (this.core) {
            return this.core.setEmotion(emotion, intensity);
        }
    }
    
    playAnimation(animationName) {
        if (this.core) {
            return this.core.playAnimation(animationName);
        }
    }
    
    stopAnimation() {
        if (this.core) {
            return this.core.stopAnimation();
        }
    }
    
    moveTo(x, z, speed = 5) {
        if (this.core) {
            return this.core.moveTo(x, z, speed);
        }
    }
    
    setPosition(x, y, z) {
        if (this.core) {
            return this.core.setPosition(x, y, z);
        }
    }
    
    getPosition() {
        if (this.core) {
            return this.core.getPosition();
        }
        return { x: 0, y: 0, z: 0 };
    }
    
    lookAt(x, y, z) {
        if (this.core) {
            return this.core.lookAt(x, y, z);
        }
    }
    
    setVolume(volume) {
        if (this.core) {
            return this.core.setVolume(volume);
        }
    }
    
    enableMicrophone() {
        if (this.core && this.core.voiceAdapter) {
            return this.core.voiceAdapter.enableMicrophone();
        }
    }
    
    disableMicrophone() {
        if (this.core && this.core.voiceAdapter) {
            return this.core.voiceAdapter.disableMicrophone();
        }
    }
    
    // 窗口大小调整
    onWindowResize() {
        if (this.core) {
            this.core.onWindowResize();
        }
    }
    
    // 销毁方法
    destroy() {
        if (this.core) {
            this.core.destroy();
        }
    }
    
    // 获取系统状态
    getStatus() {
        if (this.core) {
            return {
                isInitialized: this.isInitialized,
                isSpeaking: this.core.isSpeaking,
                isMoving: this.core.isMoving,
                currentAnimation: this.core.currentAnimation,
                position: this.core.getPosition()
            };
        }
        return {
            isInitialized: false,
            isSpeaking: false,
            isMoving: false,
            currentAnimation: null,
            position: { x: 0, y: 0, z: 0 }
        };
    }
}

// 全局导出
if (typeof window !== 'undefined') {
    window.RealDigitalHuman = RealDigitalHuman;
}