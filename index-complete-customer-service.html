<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逼真企业客服数字人 - 完整版</title>
    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #3b82f6;
            --accent-color: #ef4444;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --shadow: 0 4px 20px rgba(0,0,0,0.08);
            --border-radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: var(--text-primary);
            overflow: hidden;
            height: 100vh;
        }

        .enterprise-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 30px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 20px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
        }

        .header-controls {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .status-badge {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            font-size: 14px;
            font-weight: 600;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .main-container {
            position: absolute;
            top: 70px;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
        }

        .control-panel {
            width: 360px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid var(--border-color);
            box-shadow: var(--shadow);
            overflow-y: auto;
            z-index: 100;
        }

        .panel-section {
            padding: 25px;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .section-icon {
            width: 24px;
            height: 24px;
            background: var(--secondary-color);
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .control-input, .control-select, .control-textarea {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .control-input:focus, .control-select:focus, .control-textarea:focus {
            outline: none;
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            min-width: 120px;
        }

        .btn-primary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-secondary {
            background: var(--text-secondary);
            color: white;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .emotion-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .emotion-btn {
            padding: 12px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            background: white;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 12px;
            font-weight: 500;
        }

        .emotion-btn:hover {
            border-color: var(--secondary-color);
            background: rgba(59, 130, 246, 0.05);
        }

        .emotion-btn.active {
            border-color: var(--secondary-color);
            background: rgba(59, 130, 246, 0.1);
            color: var(--secondary-color);
        }

        .digital-human-viewport {
            flex: 1;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        #digital-human-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .viewport-overlay {
            position: absolute;
            top: 30px;
            right: 30px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow);
            min-width: 250px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .info-label {
            font-weight: 600;
            color: var(--text-secondary);
        }

        .info-value {
            font-weight: 700;
            color: var(--text-primary);
        }

        .chat-interface {
            position: absolute;
            bottom: 30px;
            left: 30px;
            right: 390px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: var(--border-radius);
            padding: 25px;
            box-shadow: var(--shadow);
        }

        .chat-input-group {
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        .chat-input {
            flex: 1;
            min-height: 50px;
            max-height: 150px;
            resize: vertical;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 15px;
            font-size: 16px;
            font-family: inherit;
        }

        .chat-controls {
            display: flex;
            gap: 10px;
        }

        .voice-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            background: var(--secondary-color);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 18px;
        }

        .voice-btn:hover {
            background: var(--primary-color);
            transform: scale(1.05);
        }

        .voice-btn.recording {
            background: var(--accent-color);
            animation: pulse 1s infinite;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 1000;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-bar {
            width: 300px;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 20px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .notification {
            position: fixed;
            top: 90px;
            right: 30px;
            padding: 15px 25px;
            border-radius: var(--border-radius);
            color: white;
            font-weight: 600;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.error {
            background: var(--accent-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }

        .emotion-status {
            background: rgba(59, 130, 246, 0.05);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 20px;
        }

        .status-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }

        .intensity-slider {
            width: 100%;
            margin: 10px 0;
        }

        @media (max-width: 1200px) {
            .control-panel {
                width: 320px;
            }
            
            .chat-interface {
                right: 350px;
            }
        }

        @media (max-width: 768px) {
            .control-panel {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                position: absolute;
                height: 100%;
                z-index: 200;
            }
            
            .control-panel.open {
                transform: translateX(0);
            }
            
            .chat-interface {
                right: 30px;
            }
        }
    </style>
</head>
<body>
    <!-- 企业级顶部导航 -->
    <header class="enterprise-header">
        <div class="logo-section">
            <div class="logo">🏢</div>
            <div class="logo-text">企业智能客服数字人</div>
        </div>
        
        <div class="header-controls">
            <div class="status-badge">
                <div class="status-dot"></div>
                <span id="system-status">系统就绪</span>
            </div>
            <button class="btn btn-secondary" id="settings-btn">系统设置</button>
            <button class="btn btn-primary" id="reset-btn">重置系统</button>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <!-- 控制面板 -->
        <aside class="control-panel" id="control-panel">
            <!-- 模型选择 -->
            <section class="panel-section">
                <h3 class="section-title">
                    <div class="section-icon">👤</div>
                    数字人模型
                </h3>
                
                <div class="control-group">
                    <label class="control-label">当前模型</label>
                    <select class="control-select" id="model-select">
                        <option value="business_female" selected>专业企业客服（李小雅）</option>
                        <option value="business_male">专业企业客服（男性）</option>
                        <option value="RiggedSimple">简化模型（测试用）</option>
                        <option value="online_backup" data-url="https://threejs.org/examples/models/gltf/Xbot.glb">在线备用模型</option>
                    </select>
                </div>
                
                <div class="btn-group">
                    <button class="btn btn-primary" id="load-model-btn">加载模型</button>
                    <button class="btn btn-secondary" id="optimize-btn">性能优化</button>
                </div>
            </section>

            <!-- 女性客服角色选择 -->
            <section class="panel-section">
                <h3 class="section-title">
                    <div class="section-icon">👩‍💼</div>
                    客服角色
                </h3>
                
                <div class="control-group">
                    <label class="control-label">角色类型</label>
                    <select class="control-select" id="character-type-select">
                        <option value="professional_friendly" selected>李小雅 - 专业友善</option>
                        <option value="young_energetic">张小萌 - 年轻活力</option>
                        <option value="senior_expert">王经理 - 资深专家</option>
                    </select>
                </div>
                
                <div class="character-info" id="character-info">
                    <div class="info-item">
                        <span class="info-label">姓名:</span>
                        <span class="info-value" id="character-name">李小雅</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">职位:</span>
                        <span class="info-value" id="character-title">高级客服专员</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">性格:</span>
                        <span class="info-value" id="character-personality">专业、友善、耐心</span>
                    </div>
                </div>
                
                <div class="btn-group">
                    <button class="btn btn-primary" id="switch-character-btn">切换角色</button>
                    <button class="btn btn-secondary" id="character-info-btn">角色详情</button>
                </div>
            </section>

            <!-- 客服状态 -->
            <section class="panel-section">
                <h3 class="section-title">
                    <div class="section-icon">💼</div>
                    客服状态
                </h3>
                
                <div class="emotion-status">
                    <div class="status-row">
                        <span>当前情感:</span>
                        <span id="current-emotion">专业待命</span>
                    </div>
                    <div class="status-row">
                        <span>客户情绪:</span>
                        <span id="customer-emotion">未检测</span>
                    </div>
                    <div class="status-row">
                        <span>响应延迟:</span>
                        <span id="response-latency">0ms</span>
                    </div>
                    <div class="status-row">
                        <span>服务质量:</span>
                        <span id="service-quality">优秀</span>
                    </div>
                </div>
            </section>

            <!-- 情感表情控制 -->
            <section class="panel-section">
                <h3 class="section-title">
                    <div class="section-icon">😊</div>
                    专业表情
                </h3>
                
                <div class="control-group">
                    <label class="control-label">客服专业表情</label>
                    <div class="emotion-grid">
                        <div class="emotion-btn" data-emotion="professional_ready">专业待命</div>
                        <div class="emotion-btn" data-emotion="warm_service_smile">温暖微笑</div>
                        <div class="emotion-btn" data-emotion="empathetic_concern">同理关切</div>
                        <div class="emotion-btn" data-emotion="patient_explaining">耐心解释</div>
                        <div class="emotion-btn" data-emotion="reassuring_confidence">安心自信</div>
                        <div class="emotion-btn" data-emotion="sincere_regret">真诚歉意</div>
                    </div>
                </div>
                
                <div class="control-group">
                    <label class="control-label">表情强度: <span id="intensity-value">60%</span></label>
                    <input type="range" class="intensity-slider" id="emotion-intensity" 
                           min="0" max="100" value="60" step="5">
                </div>
                
                <div class="btn-group">
                    <button class="btn btn-warning" id="reset-expression-btn">重置表情</button>
                    <button class="btn btn-secondary" id="auto-emotion-btn">智能情感</button>
                </div>
            </section>

            <!-- 语音控制 -->
            <section class="panel-section">
                <h3 class="section-title">
                    <div class="section-icon">🎤</div>
                    语音设置
                </h3>
                
                <div class="control-group">
                    <label class="control-label">语音语调</label>
                    <select class="control-select" id="voice-tone">
                        <option value="professional_friendly">专业友好</option>
                        <option value="warm_professional">温暖专业</option>
                        <option value="calm_steady">冷静稳重</option>
                        <option value="cheerful_professional">活泼专业</option>
                    </select>
                </div>
                
                <div class="control-group">
                    <label class="control-label">语速</label>
                    <input type="range" class="control-input" id="speech-rate" 
                           min="0.5" max="1.5" step="0.1" value="0.9">
                </div>
                
                <div class="control-group">
                    <label class="control-label">音调</label>
                    <input type="range" class="control-input" id="speech-pitch" 
                           min="0.8" max="1.2" step="0.1" value="1.0">
                </div>
            </section>

            <!-- 动作控制 -->
            <section class="panel-section">
                <h3 class="section-title">
                    <div class="section-icon">🤝</div>
                    服务动作
                </h3>
                
                <div class="control-group">
                    <label class="control-label">专业手势</label>
                    <select class="control-select" id="gesture-select">
                        <option value="none">无手势</option>
                        <option value="welcome_gesture">欢迎手势</option>
                        <option value="explaining_gesture">解释手势</option>
                        <option value="confirming_gesture">确认手势</option>
                        <option value="apologetic_gesture">歉意手势</option>
                    </select>
                </div>
                
                <div class="btn-group">
                    <button class="btn btn-primary" id="play-gesture-btn">播放手势</button>
                    <button class="btn btn-secondary" id="reset-posture-btn">重置姿态</button>
                </div>
            </section>

            <!-- 系统监控 -->
            <section class="panel-section">
                <h3 class="section-title">
                    <div class="section-icon">📊</div>
                    系统监控
                </h3>
                
                <div class="info-item">
                    <span class="info-label">模型状态</span>
                    <span class="info-value" id="model-status">已加载</span>
                </div>
                <div class="info-item">
                    <span class="info-label">渲染FPS</span>
                    <span class="info-value" id="fps-display">60</span>
                </div>
                <div class="info-item">
                    <span class="info-label">内存使用</span>
                    <span class="info-value" id="memory-usage">256MB</span>
                </div>
                <div class="info-item">
                    <span class="info-label">响应时间</span>
                    <span class="info-value" id="avg-response-time">150ms</span>
                </div>
                
                <div class="btn-group">
                    <button class="btn btn-success" id="performance-report-btn">性能报告</button>
                </div>
            </section>
        </aside>

        <!-- 3D渲染区域 -->
        <main class="digital-human-viewport">
            <div id="digital-human-container"></div>
            
            <!-- 加载动画 -->
            <div class="loading-overlay" id="loading-overlay">
                <div class="loading-spinner"></div>
                <div style="font-size: 18px; margin-bottom: 10px;">正在初始化企业客服数字人...</div>
                <div style="font-size: 14px; opacity: 0.8;">加载3D模型、情感系统和AI引擎</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="loading-progress"></div>
                </div>
                <div id="loading-text">0%</div>
            </div>
            
            <!-- 系统信息覆盖层 -->
            <div class="viewport-overlay">
                <div class="info-item">
                    <span class="info-label">数字人状态</span>
                    <span class="info-value" id="avatar-status">初始化中</span>
                </div>
                <div class="info-item">
                    <span class="info-label">当前表情</span>
                    <span class="info-value" id="current-expression">中性</span>
                </div>
                <div class="info-item">
                    <span class="info-label">服务模式</span>
                    <span class="info-value" id="service-mode">企业客服</span>
                </div>
                <div class="info-item">
                    <span class="info-label">智能等级</span>
                    <span class="info-value" id="ai-level">高级</span>
                </div>
            </div>
            
            <!-- 聊天界面 -->
            <div class="chat-interface">
                <div class="chat-input-group">
                    <textarea class="chat-input" id="customer-input" 
                              placeholder="请输入您的问题，我是您的专属AI客服助手..." 
                              rows="2"></textarea>
                    <div class="chat-controls">
                        <button class="voice-btn" id="voice-input-btn" title="语音输入">🎤</button>
                        <button class="btn btn-primary" id="send-message-btn">发送消息</button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 通知系统 -->
    <div class="notification" id="notification"></div>

    <!-- Three.js 和相关库 -->
    <script src="./libs/three.min.js"></script>
    <script src="./libs/GLTFLoader.js"></script>
    <script src="./libs/tween.min.js"></script>
    
    <!-- 数字人核心系统 -->
    <script src="./AdvancedFacialExpressionSystem.js"></script>
    <script src="./VoiceAdapter-simple.js"></script>
    <script src="./EnterpriseDigitalHuman.js"></script>
    
    <!-- PBR渲染增强系统 -->
    <script src="./PBREnhancementSystem.js"></script>
    <script src="./PerformanceOptimizer.js"></script>
    
    <!-- 女性客服角色系统 -->
    <script src="./FemaleCustomerServiceCharacter.js"></script>
    
    <!-- 企业客服增强系统 -->
    <script src="./EnterpriseCustomerService.js"></script>
    <script src="./RealisticAppearanceEnhancer.js"></script>
    <script src="./EnhancedEmotionSystem.js"></script>
    
    <script>
        // 全局应用实例
        let app = null;
        
        // 完整企业客服数字人应用
        class CompleteCustomerServiceApp {
            constructor() {
                // 核心系统
                this.digitalHuman = null;
                this.customerService = null;
                this.appearanceEnhancer = null;
                this.emotionSystem = null;
                this.performanceOptimizer = null;
                this.femaleCharacter = null;
                
                // 状态管理
                this.isInitialized = false;
                this.currentModel = 'business_female_professional';
                this.serviceMetrics = {
                    totalInteractions: 0,
                    avgSatisfaction: 0,
                    responseTime: 0
                };
                
                this.init();
            }
            
            async init() {
                console.log('🚀 启动完整企业客服数字人系统...');
                
                try {
                    // 1. 初始化3D数字人
                    await this.initializeDigitalHuman();
                    
                    // 2. 等待模型加载
                    await this.waitForModelLoad();
                    
                    // 3. 初始化增强系统
                    await this.initializeEnhancementSystems();
                    
                    // 4. 绑定事件
                    this.bindEvents();
                    
                    // 5. 启动系统监控
                    this.startSystemMonitoring();
                    
                    // 6. 完成初始化
                    this.completeInitialization();
                    
                } catch (error) {
                    console.error('系统初始化失败:', error);
                    this.handleInitializationError(error);
                }
            }
            
            async initializeDigitalHuman() {
                const container = document.getElementById('digital-human-container');
                
                this.digitalHuman = new EnterpriseDigitalHuman(container, {
                    width: container.clientWidth,
                    height: container.clientHeight,
                    modelPath: './models/business_female.glb',
                    enableAudio: true,
                    enableControls: true,
                    
                    onProgress: (progress) => {
                        this.updateLoadingProgress(progress, '加载3D模型中...');
                    },
                    
                    onLoaded: () => {
                        console.log('✅ 数字人模型加载完成');
                        this.updateSystemStatus('模型已加载');
                    },
                    
                    onError: (error) => {
                        console.error('❌ 数字人加载失败:', error);
                        this.showNotification('数字人加载失败: ' + error, 'error');
                    }
                });
            }
            
            async waitForModelLoad() {
                return new Promise((resolve) => {
                    const checkModel = () => {
                        if (this.digitalHuman && this.digitalHuman.model) {
                            resolve();
                        } else {
                            setTimeout(checkModel, 100);
                        }
                    };
                    checkModel();
                });
            }
            
            async initializeEnhancementSystems() {
                console.log('🔧 初始化增强系统...');
                
                // 1. 客服对话系统
                this.customerService = new EnterpriseCustomerService(this.digitalHuman);
                this.updateLoadingProgress(25, '初始化客服对话系统...');
                
                // 2. 外观增强系统
                this.appearanceEnhancer = new RealisticAppearanceEnhancer(this.digitalHuman);
                await this.appearanceEnhancer.onModelLoaded(this.digitalHuman.model);
                this.updateLoadingProgress(50, '增强外观真实感...');
                
                // 3. 情感识别系统
                this.emotionSystem = new EnhancedEmotionSystem(this.digitalHuman);
                this.updateLoadingProgress(65, '配置情感智能系统...');
                
                // 4. 女性客服角色系统
                this.initializeFemaleCharacter();
                this.updateLoadingProgress(75, '配置女性客服角色...');
                
                // 5. 性能优化系统
                this.initializePerformanceOptimizer();
                this.updateLoadingProgress(90, '优化渲染性能...');
                
                // 6. 系统集成
                await this.integrateAllSystems();
                this.updateLoadingProgress(100, '系统集成完成！');
            }
            
            /**
             * 初始化女性客服角色系统
             */
            initializeFemaleCharacter() {
                try {
                    if (typeof FemaleCustomerServiceCharacter !== 'undefined') {
                        console.log('👩‍💼 初始化女性客服角色系统...');
                        
                        this.femaleCharacter = new FemaleCustomerServiceCharacter(this.digitalHuman, {
                            characterType: 'professional_friendly',
                            appearanceStyle: 'business_elegant',
                            personalityProfile: 'warm_professional',
                            voiceStyle: 'gentle_confident'
                        });
                        
                        console.log('✅ 女性客服角色系统初始化完成');
                    } else {
                        console.warn('⚠️ 女性客服角色系统未加载');
                    }
                } catch (error) {
                    console.error('❌ 女性客服角色系统初始化失败:', error);
                }
            }
            
            /**
             * 初始化性能优化系统
             */
            initializePerformanceOptimizer() {
                try {
                    if (typeof PerformanceOptimizer !== 'undefined' && 
                        this.appearanceEnhancer && this.appearanceEnhancer.pbrSystem) {
                        
                        console.log('🚀 初始化性能优化系统...');
                        
                        this.performanceOptimizer = new PerformanceOptimizer(
                            this.digitalHuman, 
                            this.appearanceEnhancer.pbrSystem,
                            {
                                targetFPS: 30,
                                maxFPS: 60,
                                enableLOD: true,
                                enableFrustumCulling: true,
                                enableAdaptiveQuality: true,
                                debug: false
                            }
                        );
                        
                        console.log('✅ 性能优化系统初始化完成');
                    } else {
                        console.warn('⚠️ 性能优化系统跳过（依赖组件不可用）');
                    }
                } catch (error) {
                    console.error('❌ 性能优化系统初始化失败:', error);
                }
            }
            
            async integrateAllSystems() {
                // 将所有系统连接起来
                if (this.customerService && this.emotionSystem) {
                    // 客服系统使用情感系统
                    this.customerService.emotionAnalyzer = this.emotionSystem;
                }
                
                console.log('🔗 所有系统集成完成');
            }
            
            bindEvents() {
                // 发送消息
                document.getElementById('send-message-btn').addEventListener('click', () => {
                    this.handleCustomerMessage();
                });
                
                document.getElementById('customer-input').addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.handleCustomerMessage();
                    }
                });
                
                // 语音输入
                document.getElementById('voice-input-btn').addEventListener('click', () => {
                    this.toggleVoiceInput();
                });
                
                // 表情控制
                document.querySelectorAll('[data-emotion]').forEach(btn => {
                    btn.addEventListener('click', () => {
                        this.setCustomerServiceExpression(btn.dataset.emotion);
                        this.updateExpressionUI(btn);
                    });
                });
                
                // 表情强度
                document.getElementById('emotion-intensity').addEventListener('input', (e) => {
                    const intensity = e.target.value;
                    document.getElementById('intensity-value').textContent = intensity + '%';
                });
                
                // 手势控制
                document.getElementById('play-gesture-btn').addEventListener('click', () => {
                    const gesture = document.getElementById('gesture-select').value;
                    this.playServiceGesture(gesture);
                });
                
                // 重置功能
                document.getElementById('reset-expression-btn').addEventListener('click', () => {
                    this.resetToNeutralState();
                });
                
                document.getElementById('reset-btn').addEventListener('click', () => {
                    this.resetSystem();
                });
                
                // 智能情感开关
                document.getElementById('auto-emotion-btn').addEventListener('click', () => {
                    this.toggleAutoEmotion();
                });
                
                // 性能报告
                document.getElementById('performance-report-btn').addEventListener('click', () => {
                    this.showPerformanceReport();
                });
                
                // 角色切换
                document.getElementById('switch-character-btn').addEventListener('click', () => {
                    this.switchCharacter();
                });
                
                // 角色类型选择
                document.getElementById('character-type-select').addEventListener('change', (e) => {
                    this.updateCharacterInfo(e.target.value);
                });
                
                // 角色详情
                document.getElementById('character-info-btn').addEventListener('click', () => {
                    this.showCharacterDetails();
                });
            }
            
            async handleCustomerMessage() {
                const input = document.getElementById('customer-input');
                const message = input.value.trim();
                
                if (!message) return;
                
                const startTime = performance.now();
                
                try {
                    // 1. 情感分析
                    const emotion = await this.emotionSystem.analyzeCustomerEmotion(message, {
                        timestamp: Date.now(),
                        channel: 'text'
                    });
                    
                    // 2. 客服处理
                    const response = await this.customerService.processCustomerInput(message, {
                        emotion: emotion,
                        timestamp: Date.now()
                    });
                    
                    // 3. 更新UI
                    this.updateCustomerEmotionDisplay(emotion);
                    this.updateResponseMetrics(performance.now() - startTime);
                    
                    // 4. 清空输入
                    input.value = '';
                    
                    this.showNotification('消息处理完成', 'success');
                    
                } catch (error) {
                    console.error('消息处理错误:', error);
                    this.showNotification('处理消息时出错', 'error');
                }
            }
            
            setCustomerServiceExpression(expressionType) {
                if (this.emotionSystem) {
                    const intensity = document.getElementById('emotion-intensity').value / 100;
                    
                    // 通过情感系统设置表情
                    this.emotionSystem.generateServiceResponse({
                        type: expressionType,
                        intensity: intensity,
                        confidence: 0.9
                    });
                    
                    // 更新显示
                    document.getElementById('current-expression').textContent = this.getExpressionName(expressionType);
                    document.getElementById('current-emotion').textContent = this.getExpressionName(expressionType);
                }
            }
            
            playServiceGesture(gestureType) {
                if (gestureType === 'none') return;
                
                const gestureMap = {
                    'welcome_gesture': 'wave_right',
                    'explaining_gesture': 'presentation',
                    'confirming_gesture': 'thumbs_up',
                    'apologetic_gesture': 'wave_left'
                };
                
                const animation = gestureMap[gestureType];
                if (animation && this.digitalHuman) {
                    if (animation === 'presentation') {
                        this.digitalHuman.playAnimation(animation);
                    } else {
                        this.digitalHuman.playHandGesture(animation);
                    }
                    
                    this.showNotification(`播放手势: ${gestureType}`, 'success');
                }
            }
            
            resetToNeutralState() {
                if (this.emotionSystem) {
                    this.emotionSystem.resetToProfessionalState();
                }
                
                // 重置UI
                document.querySelectorAll('[data-emotion]').forEach(btn => {
                    btn.classList.remove('active');
                });
                
                document.getElementById('current-expression').textContent = '专业待命';
                document.getElementById('current-emotion').textContent = '专业待命';
                
                this.showNotification('已重置到专业状态', 'success');
            }
            
            updateExpressionUI(activeBtn) {
                document.querySelectorAll('[data-emotion]').forEach(btn => {
                    btn.classList.remove('active');
                });
                activeBtn.classList.add('active');
            }
            
            updateCustomerEmotionDisplay(emotion) {
                document.getElementById('customer-emotion').textContent = this.getEmotionName(emotion.type);
                
                // 根据客户情绪更新服务质量显示
                const quality = this.calculateServiceQuality(emotion);
                document.getElementById('service-quality').textContent = quality;
            }
            
            updateResponseMetrics(responseTime) {
                // 更新平均响应时间
                this.serviceMetrics.responseTime = 
                    (this.serviceMetrics.responseTime * 0.9) + (responseTime * 0.1);
                
                document.getElementById('response-latency').textContent = 
                    Math.round(this.serviceMetrics.responseTime) + 'ms';
                
                document.getElementById('avg-response-time').textContent = 
                    Math.round(this.serviceMetrics.responseTime) + 'ms';
                
                this.serviceMetrics.totalInteractions++;
            }
            
            calculateServiceQuality(emotion) {
                // 根据客户情绪计算服务质量
                const positiveEmotions = ['satisfied', 'happy', 'grateful'];
                const negativeEmotions = ['angry', 'frustrated', 'disappointed'];
                
                if (positiveEmotions.includes(emotion.type)) {
                    return '优秀';
                } else if (negativeEmotions.includes(emotion.type)) {
                    return '需改进';
                } else {
                    return '良好';
                }
            }
            
            startSystemMonitoring() {
                setInterval(() => {
                    this.updateSystemMetrics();
                }, 1000);
                
                console.log('📊 系统监控已启动');
            }
            
            updateSystemMetrics() {
                // 更新FPS
                if (this.digitalHuman) {
                    const fps = this.digitalHuman.getFPS ? this.digitalHuman.getFPS() : 60;
                    document.getElementById('fps-display').textContent = Math.round(fps);
                }
                
                // 更新内存使用 (估算)
                const memoryUsage = Math.round(performance.memory ? 
                    performance.memory.usedJSHeapSize / 1024 / 1024 : 256);
                document.getElementById('memory-usage').textContent = memoryUsage + 'MB';
                
                // 更新系统状态
                if (this.isInitialized) {
                    document.getElementById('avatar-status').textContent = '运行中';
                    document.getElementById('model-status').textContent = '正常';
                }
            }
            
            completeInitialization() {
                this.hideLoading();
                this.isInitialized = true;
                
                // 启动欢迎流程
                this.startWelcomeSequence();
                
                console.log('🎉 企业客服数字人系统初始化完成！');
                this.showNotification('🎉 企业客服数字人已就绪！', 'success');
            }
            
            async startWelcomeSequence() {
                if (this.customerService) {
                    // 播放欢迎语
                    const greeting = this.customerService.startNewConversation({
                        isReturning: false,
                        preferredLanguage: 'zh-CN'
                    });
                    
                    console.log('👋 欢迎序列已启动');
                }
            }
            
            // 工具方法
            updateLoadingProgress(progress, text = '') {
                document.getElementById('loading-progress').style.width = progress + '%';
                document.getElementById('loading-text').textContent = Math.round(progress) + '%';
                
                if (text) {
                    document.querySelector('.loading-overlay div').textContent = text;
                }
            }
            
            hideLoading() {
                document.getElementById('loading-overlay').style.display = 'none';
            }
            
            updateSystemStatus(status) {
                document.getElementById('system-status').textContent = status;
            }
            
            showNotification(message, type = 'success') {
                const notification = document.getElementById('notification');
                notification.textContent = message;
                notification.className = `notification ${type}`;
                notification.classList.add('show');
                
                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }
            
            getExpressionName(expression) {
                const names = {
                    'professional_ready': '专业待命',
                    'warm_service_smile': '温暖微笑',
                    'empathetic_concern': '同理关切',
                    'patient_explaining': '耐心解释',
                    'reassuring_confidence': '安心自信',
                    'sincere_regret': '真诚歉意'
                };
                return names[expression] || expression;
            }
            
            getEmotionName(emotion) {
                const names = {
                    'satisfied': '满意',
                    'happy': '开心',
                    'grateful': '感激',
                    'frustrated': '沮丧',
                    'angry': '愤怒',
                    'confused': '困惑',
                    'disappointed': '失望',
                    'worried': '担心',
                    'neutral': '中性',
                    'curious': '好奇'
                };
                return names[emotion] || emotion;
            }
            
            handleInitializationError(error) {
                this.hideLoading();
                this.showNotification('系统初始化失败: ' + error.message, 'error');
                
                // 显示错误恢复选项
                this.showErrorRecoveryOptions(error);
            }
            
            showErrorRecoveryOptions(error) {
                // 实现错误恢复界面
                console.log('显示错误恢复选项:', error);
            }
            
            // 高级功能
            toggleVoiceInput() {
                // 语音输入功能
                console.log('切换语音输入');
            }
            
            toggleAutoEmotion() {
                // 切换自动情感模式
                console.log('切换自动情感模式');
            }
            
            showPerformanceReport() {
                // 显示性能报告
                let report = {
                    totalInteractions: this.serviceMetrics.totalInteractions,
                    avgResponseTime: Math.round(this.serviceMetrics.responseTime),
                    currentFPS: this.digitalHuman ? this.digitalHuman.getFPS() : 60,
                    memoryUsage: Math.round(performance.memory ? 
                        performance.memory.usedJSHeapSize / 1024 / 1024 : 256)
                };
                
                // 如果有性能优化器，获取详细报告
                if (this.performanceOptimizer) {
                    const detailedReport = this.performanceOptimizer.getPerformanceReport();
                    report = { ...report, ...detailedReport };
                    
                    console.log('📊 详细性能报告:', report);
                    this.showNotification(
                        `FPS: ${report.metrics.fps}, 质量: ${report.metrics.currentQuality}, 适应次数: ${report.metrics.adaptationCount}`, 
                        'success'
                    );
                } else {
                    console.log('📊 基础性能报告:', report);
                    this.showNotification(`总交互: ${report.totalInteractions}, 响应时间: ${report.avgResponseTime}ms`, 'success');
                }
            }
            
            // 角色管理方法
            switchCharacter() {
                const selectedType = document.getElementById('character-type-select').value;
                
                if (this.femaleCharacter) {
                    const newCharacter = this.femaleCharacter.switchCharacter(selectedType);
                    this.updateCharacterDisplay(newCharacter);
                    this.showNotification(`已切换到角色: ${newCharacter.name}`, 'success');
                } else {
                    this.showNotification('角色系统未初始化', 'error');
                }
            }
            
            updateCharacterInfo(characterType) {
                if (this.femaleCharacter) {
                    const profiles = this.femaleCharacter.characterProfiles;
                    const character = profiles[characterType];
                    
                    if (character) {
                        document.getElementById('character-name').textContent = character.name;
                        document.getElementById('character-title').textContent = character.title;
                        document.getElementById('character-personality').textContent = character.personality;
                    }
                }
            }
            
            updateCharacterDisplay(character) {
                document.getElementById('character-name').textContent = character.name;
                document.getElementById('character-title').textContent = character.title;
                document.getElementById('character-personality').textContent = character.personality;
            }
            
            showCharacterDetails() {
                if (this.femaleCharacter) {
                    const info = this.femaleCharacter.getCharacterInfo();
                    const current = info.current;
                    
                    const details = `
角色信息：
• 姓名：${current.name}
• 职位：${current.title}
• 性格：${current.personality}
• 发型：${current.appearance.hairStyle}
• 服装：${current.clothing.style}
• 语音：${current.voice.tone}

可用表情：${info.expressions.join(', ')}
可用手势：${info.gestures.join(', ')}
                    `;
                    
                    alert(details);
                } else {
                    this.showNotification('角色系统未初始化', 'error');
                }
            }
            
            resetSystem() {
                // 重置整个系统
                if (confirm('确定要重置整个系统吗？')) {
                    location.reload();
                }
            }
        }
        
        // 页面加载完成后启动应用
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 启动完整企业客服数字人应用...');
            
            // 稍微延迟以确保所有资源加载完成
            setTimeout(() => {
                app = new CompleteCustomerServiceApp();
                window.app = app; // 全局访问
            }, 500);
        });
        
        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            if (app) {
                if (app.femaleCharacter) app.femaleCharacter.destroy();
                if (app.performanceOptimizer) app.performanceOptimizer.destroy();
                if (app.digitalHuman) app.digitalHuman.destroy();
                if (app.emotionSystem) app.emotionSystem.destroy();
                if (app.appearanceEnhancer) app.appearanceEnhancer.destroy();
            }
        });
    </script>
</body>
</html>