/**
 * 3D数字人WebSocket服务器
 * 企业级C语言实现
 * 支持多客户端连接、消息路由、数据持久化
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <time.h>
#include <pthread.h>
#include <errno.h>
#include <sys/time.h>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
    #define close closesocket
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
#endif

#include <libwebsockets.h>
#include <json-c/json.h>

// 配置常量
#define MAX_CLIENTS 100
#define MAX_MESSAGE_SIZE 4096
#define DEFAULT_PORT 8080
#define HEARTBEAT_INTERVAL 30
#define MAX_SESSIONS 50

// 消息类型定义
typedef enum {
    MSG_TYPE_USER_MESSAGE = 1,
    MSG_TYPE_SYSTEM_STATUS = 2,
    MSG_TYPE_MODEL_CONTROL = 3,
    MSG_TYPE_ANIMATION_CONTROL = 4,
    MSG_TYPE_VOICE_CONTROL = 5,
    MSG_TYPE_POSITION_CONTROL = 6,
    MSG_TYPE_HEARTBEAT = 7,
    MSG_TYPE_ERROR = 8
} message_type_t;

// 客户端会话结构
typedef struct client_session {
    struct lws *wsi;
    char session_id[64];
    char user_agent[256];
    char client_ip[64];
    time_t connect_time;
    time_t last_heartbeat;
    int is_authenticated;
    int message_count;
    struct client_session *next;
} client_session_t;

// 数字人状态结构
typedef struct digital_human_state {
    float position_x, position_y, position_z;
    float rotation_x, rotation_y, rotation_z;
    char current_animation[64];
    char current_emotion[32];
    int is_speaking;
    int is_moving;
    char last_message[512];
    time_t last_update;
} digital_human_state_t;

// 服务器上下文
typedef struct server_context {
    struct lws_context *lws_context;
    client_session_t *clients;
    digital_human_state_t human_state;
    int client_count;
    int port;
    int running;
    pthread_mutex_t clients_mutex;
    pthread_mutex_t state_mutex;
    FILE *log_file;
} server_context_t;

// 全局服务器实例
static server_context_t g_server = {0};

// 函数声明
static int callback_digital_human(struct lws *wsi, enum lws_callback_reasons reason, 
                                  void *user, void *in, size_t len);
static client_session_t* create_client_session(struct lws *wsi);
static void destroy_client_session(client_session_t *client);
static client_session_t* find_client_session(struct lws *wsi);
static void remove_client_session(struct lws *wsi);
static int process_message(client_session_t *client, const char *message);
static int send_message_to_client(client_session_t *client, const char *message);
static int broadcast_message(const char *message);
static char* create_response_message(const char *type, const char *data, const char *status);
static void handle_user_message(client_session_t *client, json_object *msg_obj);
static void handle_model_control(client_session_t *client, json_object *msg_obj);
static void handle_animation_control(client_session_t *client, json_object *msg_obj);
static void handle_voice_control(client_session_t *client, json_object *msg_obj);
static void handle_position_control(client_session_t *client, json_object *msg_obj);
static void update_digital_human_state(const char *key, const char *value);
static char* get_digital_human_status();
static void log_message(const char *level, const char *format, ...);
static void cleanup_server();
static void signal_handler(int signum);
static void* heartbeat_thread(void *arg);
static char* generate_session_id();

// WebSocket协议定义
static struct lws_protocols protocols[] = {
    {
        "digital-human-protocol",
        callback_digital_human,
        sizeof(client_session_t),
        MAX_MESSAGE_SIZE,
    },
    { NULL, NULL, 0, 0 }
};

/**
 * WebSocket回调函数
 */
static int callback_digital_human(struct lws *wsi, enum lws_callback_reasons reason, 
                                  void *user, void *in, size_t len) {
    client_session_t *client = NULL;
    
    switch (reason) {
        case LWS_CALLBACK_ESTABLISHED:
            log_message("INFO", "New WebSocket connection established");
            client = create_client_session(wsi);
            if (client) {
                pthread_mutex_lock(&g_server.clients_mutex);
                g_server.client_count++;
                pthread_mutex_unlock(&g_server.clients_mutex);
                
                // 发送欢迎消息
                char *welcome_msg = create_response_message("welcome", 
                    "{\"session_id\":\"%s\", \"server_time\":%ld}", "success");
                send_message_to_client(client, welcome_msg);
                free(welcome_msg);
                
                // 发送当前数字人状态
                char *status_msg = get_digital_human_status();
                send_message_to_client(client, status_msg);
                free(status_msg);
            }
            break;
            
        case LWS_CALLBACK_RECEIVE:
            client = find_client_session(wsi);
            if (client && in && len > 0) {
                char message[MAX_MESSAGE_SIZE];
                strncpy(message, (char*)in, len);
                message[len] = '\0';
                
                log_message("DEBUG", "Received message from client %s: %s", 
                           client->session_id, message);
                
                process_message(client, message);
                client->message_count++;
            }
            break;
            
        case LWS_CALLBACK_CLOSED:
            log_message("INFO", "WebSocket connection closed");
            remove_client_session(wsi);
            pthread_mutex_lock(&g_server.clients_mutex);
            g_server.client_count--;
            pthread_mutex_unlock(&g_server.clients_mutex);
            break;
            
        case LWS_CALLBACK_SERVER_WRITEABLE:
            // 处理待发送消息队列（如果需要）
            break;
            
        default:
            break;
    }
    
    return 0;
}

/**
 * 创建客户端会话
 */
static client_session_t* create_client_session(struct lws *wsi) {
    client_session_t *client = malloc(sizeof(client_session_t));
    if (!client) {
        log_message("ERROR", "Failed to allocate memory for client session");
        return NULL;
    }
    
    memset(client, 0, sizeof(client_session_t));
    client->wsi = wsi;
    client->connect_time = time(NULL);
    client->last_heartbeat = client->connect_time;
    
    // 生成会话ID
    strcpy(client->session_id, generate_session_id());
    
    // 获取客户端IP
    char client_name[128];
    char client_ip[128];
    lws_get_peer_addresses(wsi, lws_get_socket_fd(wsi), client_name, sizeof(client_name),
                          client_ip, sizeof(client_ip));
    strncpy(client->client_ip, client_ip, sizeof(client->client_ip) - 1);
    
    // 添加到客户端链表
    pthread_mutex_lock(&g_server.clients_mutex);
    client->next = g_server.clients;
    g_server.clients = client;
    pthread_mutex_unlock(&g_server.clients_mutex);
    
    log_message("INFO", "Created client session %s from IP %s", 
               client->session_id, client->client_ip);
    
    return client;
}

/**
 * 查找客户端会话
 */
static client_session_t* find_client_session(struct lws *wsi) {
    pthread_mutex_lock(&g_server.clients_mutex);
    client_session_t *current = g_server.clients;
    while (current) {
        if (current->wsi == wsi) {
            pthread_mutex_unlock(&g_server.clients_mutex);
            return current;
        }
        current = current->next;
    }
    pthread_mutex_unlock(&g_server.clients_mutex);
    return NULL;
}

/**
 * 移除客户端会话
 */
static void remove_client_session(struct lws *wsi) {
    pthread_mutex_lock(&g_server.clients_mutex);
    
    client_session_t **current = &g_server.clients;
    while (*current) {
        if ((*current)->wsi == wsi) {
            client_session_t *to_remove = *current;
            *current = (*current)->next;
            
            log_message("INFO", "Removed client session %s", to_remove->session_id);
            free(to_remove);
            break;
        }
        current = &(*current)->next;
    }
    
    pthread_mutex_unlock(&g_server.clients_mutex);
}

/**
 * 处理接收到的消息
 */
static int process_message(client_session_t *client, const char *message) {
    json_object *root = json_tokener_parse(message);
    if (!root) {
        log_message("ERROR", "Failed to parse JSON message from client %s", client->session_id);
        return -1;
    }
    
    json_object *type_obj;
    if (!json_object_object_get_ex(root, "type", &type_obj)) {
        log_message("ERROR", "Message missing 'type' field from client %s", client->session_id);
        json_object_put(root);
        return -1;
    }
    
    const char *type = json_object_get_string(type_obj);
    log_message("DEBUG", "Processing message type '%s' from client %s", type, client->session_id);
    
    // 更新心跳时间
    client->last_heartbeat = time(NULL);
    
    // 根据消息类型分发处理
    if (strcmp(type, "userMessage") == 0) {
        handle_user_message(client, root);
    } else if (strcmp(type, "modelControl") == 0) {
        handle_model_control(client, root);
    } else if (strcmp(type, "animationControl") == 0) {
        handle_animation_control(client, root);
    } else if (strcmp(type, "voiceControl") == 0) {
        handle_voice_control(client, root);
    } else if (strcmp(type, "positionControl") == 0) {
        handle_position_control(client, root);
    } else if (strcmp(type, "heartbeat") == 0) {
        // 心跳消息已在上面处理
        char *response = create_response_message("heartbeat", "{\"server_time\":%ld}", "success");
        send_message_to_client(client, response);
        free(response);
    } else {
        log_message("WARNING", "Unknown message type '%s' from client %s", type, client->session_id);
    }
    
    json_object_put(root);
    return 0;
}

/**
 * 处理用户消息
 */
static void handle_user_message(client_session_t *client, json_object *msg_obj) {
    json_object *content_obj;
    if (!json_object_object_get_ex(msg_obj, "content", &content_obj)) {
        return;
    }
    
    const char *content = json_object_get_string(content_obj);
    log_message("INFO", "User message from %s: %s", client->session_id, content);
    
    // 更新数字人状态
    pthread_mutex_lock(&g_server.state_mutex);
    strncpy(g_server.human_state.last_message, content, sizeof(g_server.human_state.last_message) - 1);
    g_server.human_state.is_speaking = 1;
    g_server.human_state.last_update = time(NULL);
    pthread_mutex_unlock(&g_server.state_mutex);
    
    // 广播给所有客户端
    char broadcast_msg[MAX_MESSAGE_SIZE];
    snprintf(broadcast_msg, sizeof(broadcast_msg),
             "{\"type\":\"userMessage\",\"data\":{\"content\":\"%s\",\"session_id\":\"%s\"},\"timestamp\":%ld}",
             content, client->session_id, time(NULL));
    
    broadcast_message(broadcast_msg);
    
    // 模拟AI响应（这里可以集成实际的AI服务）
    sleep(1); // 模拟处理时间
    
    char ai_response[MAX_MESSAGE_SIZE];
    snprintf(ai_response, sizeof(ai_response),
             "{\"type\":\"aiResponse\",\"data\":{\"content\":\"收到您的消息：%s\"},\"timestamp\":%ld}",
             content, time(NULL));
    
    broadcast_message(ai_response);
    
    // 更新状态为非说话状态
    pthread_mutex_lock(&g_server.state_mutex);
    g_server.human_state.is_speaking = 0;
    pthread_mutex_unlock(&g_server.state_mutex);
}

/**
 * 处理模型控制
 */
static void handle_model_control(client_session_t *client, json_object *msg_obj) {
    json_object *data_obj;
    if (!json_object_object_get_ex(msg_obj, "data", &data_obj)) {
        return;
    }
    
    json_object *action_obj;
    if (!json_object_object_get_ex(data_obj, "action", &action_obj)) {
        return;
    }
    
    const char *action = json_object_get_string(action_obj);
    log_message("INFO", "Model control action from %s: %s", client->session_id, action);
    
    // 处理模型切换、加载等操作
    char response[MAX_MESSAGE_SIZE];
    snprintf(response, sizeof(response),
             "{\"type\":\"modelControl\",\"data\":{\"action\":\"%s\",\"status\":\"success\"},\"timestamp\":%ld}",
             action, time(NULL));
    
    broadcast_message(response);
}

/**
 * 处理动画控制
 */
static void handle_animation_control(client_session_t *client, json_object *msg_obj) {
    json_object *data_obj;
    if (!json_object_object_get_ex(msg_obj, "data", &data_obj)) {
        return;
    }
    
    json_object *animation_obj;
    if (!json_object_object_get_ex(data_obj, "animation", &animation_obj)) {
        return;
    }
    
    const char *animation = json_object_get_string(animation_obj);
    log_message("INFO", "Animation control from %s: %s", client->session_id, animation);
    
    // 更新数字人动画状态
    pthread_mutex_lock(&g_server.state_mutex);
    strncpy(g_server.human_state.current_animation, animation, sizeof(g_server.human_state.current_animation) - 1);
    g_server.human_state.last_update = time(NULL);
    pthread_mutex_unlock(&g_server.state_mutex);
    
    char response[MAX_MESSAGE_SIZE];
    snprintf(response, sizeof(response),
             "{\"type\":\"animationControl\",\"data\":{\"animation\":\"%s\",\"status\":\"playing\"},\"timestamp\":%ld}",
             animation, time(NULL));
    
    broadcast_message(response);
}

/**
 * 处理语音控制
 */
static void handle_voice_control(client_session_t *client, json_object *msg_obj) {
    json_object *data_obj;
    if (!json_object_object_get_ex(msg_obj, "data", &data_obj)) {
        return;
    }
    
    json_object *text_obj;
    if (!json_object_object_get_ex(data_obj, "text", &text_obj)) {
        return;
    }
    
    const char *text = json_object_get_string(text_obj);
    log_message("INFO", "Voice control from %s: %s", client->session_id, text);
    
    // 更新说话状态
    pthread_mutex_lock(&g_server.state_mutex);
    g_server.human_state.is_speaking = 1;
    strncpy(g_server.human_state.last_message, text, sizeof(g_server.human_state.last_message) - 1);
    g_server.human_state.last_update = time(NULL);
    pthread_mutex_unlock(&g_server.state_mutex);
    
    char response[MAX_MESSAGE_SIZE];
    snprintf(response, sizeof(response),
             "{\"type\":\"voiceControl\",\"data\":{\"text\":\"%s\",\"status\":\"speaking\"},\"timestamp\":%ld}",
             text, time(NULL));
    
    broadcast_message(response);
}

/**
 * 处理位置控制
 */
static void handle_position_control(client_session_t *client, json_object *msg_obj) {
    json_object *data_obj;
    if (!json_object_object_get_ex(msg_obj, "data", &data_obj)) {
        return;
    }
    
    json_object *x_obj, *y_obj, *z_obj;
    if (!json_object_object_get_ex(data_obj, "x", &x_obj) ||
        !json_object_object_get_ex(data_obj, "y", &y_obj) ||
        !json_object_object_get_ex(data_obj, "z", &z_obj)) {
        return;
    }
    
    double x = json_object_get_double(x_obj);
    double y = json_object_get_double(y_obj);
    double z = json_object_get_double(z_obj);
    
    log_message("INFO", "Position control from %s: (%.2f, %.2f, %.2f)", 
               client->session_id, x, y, z);
    
    // 更新数字人位置
    pthread_mutex_lock(&g_server.state_mutex);
    g_server.human_state.position_x = (float)x;
    g_server.human_state.position_y = (float)y;
    g_server.human_state.position_z = (float)z;
    g_server.human_state.is_moving = 1;
    g_server.human_state.last_update = time(NULL);
    pthread_mutex_unlock(&g_server.state_mutex);
    
    char response[MAX_MESSAGE_SIZE];
    snprintf(response, sizeof(response),
             "{\"type\":\"positionControl\",\"data\":{\"x\":%.2f,\"y\":%.2f,\"z\":%.2f,\"status\":\"moving\"},\"timestamp\":%ld}",
             x, y, z, time(NULL));
    
    broadcast_message(response);
    
    // 模拟移动完成
    sleep(2);
    pthread_mutex_lock(&g_server.state_mutex);
    g_server.human_state.is_moving = 0;
    pthread_mutex_unlock(&g_server.state_mutex);
    
    snprintf(response, sizeof(response),
             "{\"type\":\"positionControl\",\"data\":{\"x\":%.2f,\"y\":%.2f,\"z\":%.2f,\"status\":\"arrived\"},\"timestamp\":%ld}",
             x, y, z, time(NULL));
    
    broadcast_message(response);
}

/**
 * 发送消息给指定客户端
 */
static int send_message_to_client(client_session_t *client, const char *message) {
    if (!client || !client->wsi || !message) {
        return -1;
    }
    
    size_t msg_len = strlen(message);
    unsigned char *buf = malloc(LWS_PRE + msg_len);
    if (!buf) {
        log_message("ERROR", "Failed to allocate message buffer");
        return -1;
    }
    
    memcpy(&buf[LWS_PRE], message, msg_len);
    
    int result = lws_write(client->wsi, &buf[LWS_PRE], msg_len, LWS_WRITE_TEXT);
    
    free(buf);
    
    if (result < 0) {
        log_message("ERROR", "Failed to send message to client %s", client->session_id);
        return -1;
    }
    
    return 0;
}

/**
 * 广播消息给所有客户端
 */
static int broadcast_message(const char *message) {
    pthread_mutex_lock(&g_server.clients_mutex);
    
    client_session_t *current = g_server.clients;
    int sent_count = 0;
    
    while (current) {
        if (send_message_to_client(current, message) == 0) {
            sent_count++;
        }
        current = current->next;
    }
    
    pthread_mutex_unlock(&g_server.clients_mutex);
    
    log_message("DEBUG", "Broadcast message to %d clients: %s", sent_count, message);
    return sent_count;
}

/**
 * 创建响应消息
 */
static char* create_response_message(const char *type, const char *data, const char *status) {
    char *message = malloc(MAX_MESSAGE_SIZE);
    if (!message) {
        return NULL;
    }
    
    snprintf(message, MAX_MESSAGE_SIZE,
             "{\"type\":\"%s\",\"data\":%s,\"status\":\"%s\",\"timestamp\":%ld}",
             type, data, status, time(NULL));
    
    return message;
}

/**
 * 获取数字人状态
 */
static char* get_digital_human_status() {
    char *status = malloc(MAX_MESSAGE_SIZE);
    if (!status) {
        return NULL;
    }
    
    pthread_mutex_lock(&g_server.state_mutex);
    
    snprintf(status, MAX_MESSAGE_SIZE,
             "{\"type\":\"systemStatus\",\"data\":{"
             "\"position\":{\"x\":%.2f,\"y\":%.2f,\"z\":%.2f},"
             "\"rotation\":{\"x\":%.2f,\"y\":%.2f,\"z\":%.2f},"
             "\"animation\":\"%s\","
             "\"emotion\":\"%s\","
             "\"is_speaking\":%s,"
             "\"is_moving\":%s,"
             "\"last_message\":\"%s\","
             "\"client_count\":%d"
             "},\"timestamp\":%ld}",
             g_server.human_state.position_x,
             g_server.human_state.position_y,
             g_server.human_state.position_z,
             g_server.human_state.rotation_x,
             g_server.human_state.rotation_y,
             g_server.human_state.rotation_z,
             g_server.human_state.current_animation,
             g_server.human_state.current_emotion,
             g_server.human_state.is_speaking ? "true" : "false",
             g_server.human_state.is_moving ? "true" : "false",
             g_server.human_state.last_message,
             g_server.client_count,
             time(NULL));
    
    pthread_mutex_unlock(&g_server.state_mutex);
    
    return status;
}

/**
 * 生成会话ID
 */
static char* generate_session_id() {
    static char session_id[64];
    struct timeval tv;
    gettimeofday(&tv, NULL);
    
    snprintf(session_id, sizeof(session_id), "DHU_%ld_%ld_%d", 
             tv.tv_sec, tv.tv_usec, rand() % 10000);
    
    return session_id;
}

/**
 * 日志记录
 */
static void log_message(const char *level, const char *format, ...) {
    time_t now;
    struct tm *tm_info;
    char timestamp[64];
    
    time(&now);
    tm_info = localtime(&now);
    strftime(timestamp, sizeof(timestamp), "%Y-%m-%d %H:%M:%S", tm_info);
    
    printf("[%s] [%s] ", timestamp, level);
    
    va_list args;
    va_start(args, format);
    vprintf(format, args);
    va_end(args);
    
    printf("\n");
    fflush(stdout);
    
    // 如果有日志文件，也写入文件
    if (g_server.log_file) {
        fprintf(g_server.log_file, "[%s] [%s] ", timestamp, level);
        va_start(args, format);
        vfprintf(g_server.log_file, format, args);
        va_end(args);
        fprintf(g_server.log_file, "\n");
        fflush(g_server.log_file);
    }
}

/**
 * 心跳线程
 */
static void* heartbeat_thread(void *arg) {
    while (g_server.running) {
        sleep(HEARTBEAT_INTERVAL);
        
        time_t current_time = time(NULL);
        
        // 检查客户端心跳
        pthread_mutex_lock(&g_server.clients_mutex);
        client_session_t *current = g_server.clients;
        while (current) {
            if (current_time - current->last_heartbeat > HEARTBEAT_INTERVAL * 2) {
                log_message("WARNING", "Client %s heartbeat timeout", current->session_id);
                // 这里可以选择断开连接
            }
            current = current->next;
        }
        pthread_mutex_unlock(&g_server.clients_mutex);
        
        // 发送系统状态更新
        char *status_msg = get_digital_human_status();
        if (status_msg) {
            broadcast_message(status_msg);
            free(status_msg);
        }
    }
    
    return NULL;
}

/**
 * 信号处理函数
 */
static void signal_handler(int signum) {
    log_message("INFO", "Received signal %d, shutting down server...", signum);
    g_server.running = 0;
}

/**
 * 清理服务器资源
 */
static void cleanup_server() {
    log_message("INFO", "Cleaning up server resources...");
    
    // 设置停止标志
    g_server.running = 0;
    
    // 清理客户端连接
    pthread_mutex_lock(&g_server.clients_mutex);
    while (g_server.clients) {
        client_session_t *to_remove = g_server.clients;
        g_server.clients = g_server.clients->next;
        free(to_remove);
    }
    pthread_mutex_unlock(&g_server.clients_mutex);
    
    // 销毁互斥锁
    pthread_mutex_destroy(&g_server.clients_mutex);
    pthread_mutex_destroy(&g_server.state_mutex);
    
    // 关闭日志文件
    if (g_server.log_file && g_server.log_file != stdout) {
        fclose(g_server.log_file);
    }
    
    // 清理libwebsockets上下文
    if (g_server.lws_context) {
        lws_context_destroy(g_server.lws_context);
    }
    
    log_message("INFO", "Server cleanup completed");
}

/**
 * 主函数
 */
int main(int argc, char **argv) {
    struct lws_context_creation_info info;
    pthread_t heartbeat_tid;
    
    // 初始化随机数生成器
    srand(time(NULL));
    
    // 解析命令行参数
    int port = DEFAULT_PORT;
    if (argc > 1) {
        port = atoi(argv[1]);
        if (port <= 0 || port > 65535) {
            fprintf(stderr, "Invalid port number: %s\n", argv[1]);
            return 1;
        }
    }
    
    // 初始化服务器
    memset(&g_server, 0, sizeof(g_server));
    g_server.port = port;
    g_server.running = 1;
    
    // 初始化数字人状态
    strcpy(g_server.human_state.current_animation, "idle");
    strcpy(g_server.human_state.current_emotion, "neutral");
    
    // 初始化互斥锁
    if (pthread_mutex_init(&g_server.clients_mutex, NULL) != 0 ||
        pthread_mutex_init(&g_server.state_mutex, NULL) != 0) {
        fprintf(stderr, "Failed to initialize mutexes\n");
        return 1;
    }
    
    // 打开日志文件
    g_server.log_file = fopen("digital_human_server.log", "a");
    if (!g_server.log_file) {
        g_server.log_file = stdout;
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 配置libwebsockets
    memset(&info, 0, sizeof(info));
    info.port = port;
    info.protocols = protocols;
    info.gid = -1;
    info.uid = -1;
    info.options = LWS_SERVER_OPTION_VALIDATE_UTF8;
    
    // 创建WebSocket上下文
    g_server.lws_context = lws_create_context(&info);
    if (!g_server.lws_context) {
        log_message("ERROR", "Failed to create libwebsockets context");
        cleanup_server();
        return 1;
    }
    
    log_message("INFO", "Digital Human WebSocket Server started on port %d", port);
    log_message("INFO", "Server features: Multi-client, Heartbeat, State Management");
    
    // 启动心跳线程
    if (pthread_create(&heartbeat_tid, NULL, heartbeat_thread, NULL) != 0) {
        log_message("ERROR", "Failed to create heartbeat thread");
        cleanup_server();
        return 1;
    }
    
    // 主事件循环
    while (g_server.running) {
        if (lws_service(g_server.lws_context, 50) < 0) {
            log_message("ERROR", "WebSocket service error");
            break;
        }
    }
    
    // 等待心跳线程结束
    pthread_join(heartbeat_tid, NULL);
    
    // 清理资源
    cleanup_server();
    
    log_message("INFO", "Digital Human Server shutdown completed");
    
    return 0;
}