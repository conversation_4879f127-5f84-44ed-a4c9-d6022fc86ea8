@echo off
chcp 65001 > nul
echo 正在修复Tween.js库文件...
echo.

cd libs

echo [修复] 删除损坏的tween.min.js文件...
if exist "tween.min.js" del "tween.min.js"

echo [下载] 尝试从备用源下载Tween.js...
curl -L -o tween.min.js "https://unpkg.com/@tweenjs/tween.js@18.6.4/dist/tween.min.js"
if %errorlevel% neq 0 (
    echo [备用] 尝试从jsdelivr下载...
    curl -L -o tween.min.js "https://cdn.jsdelivr.net/npm/@tweenjs/tween.js@18.6.4/dist/tween.min.js"
    if %errorlevel% neq 0 (
        echo [备用] 尝试从cdnjs下载...
        curl -L -o tween.min.js "https://cdnjs.cloudflare.com/ajax/libs/tween.js/18.6.4/tween.min.js"
        if %errorlevel% neq 0 (
            echo [错误] 所有下载源都失败了，创建本地版本...
            goto :create_local
        )
    )
)

echo [验证] 检查文件内容...
findstr /C:"TWEEN" tween.min.js > nul
if %errorlevel% neq 0 (
    echo [错误] 下载的文件不是有效的Tween.js库，创建本地版本...
    goto :create_local
)

echo ✅ Tween.js修复完成！
goto :end

:create_local
echo [创建] 创建简化版本的Tween.js...
echo // 简化版Tween.js - 为数字人系统提供基本动画功能 > tween.min.js
echo var TWEEN = { >> tween.min.js
echo   Tween: function(obj) { >> tween.min.js
echo     this.object = obj; >> tween.min.js
echo     this.start = function() { return this; }; >> tween.min.js
echo     this.to = function(target, duration) { >> tween.min.js
echo       this.target = target; >> tween.min.js
echo       this.duration = duration || 1000; >> tween.min.js
echo       return this; >> tween.min.js
echo     }; >> tween.min.js
echo     this.easing = function(fn) { return this; }; >> tween.min.js
echo     this.onUpdate = function(fn) { this.updateFn = fn; return this; }; >> tween.min.js
echo     this.onComplete = function(fn) { this.completeFn = fn; return this; }; >> tween.min.js
echo     return this; >> tween.min.js
echo   }, >> tween.min.js
echo   Easing: { >> tween.min.js
echo     Quadratic: { Out: function(t) { return -t * (t - 2); } } >> tween.min.js
echo   }, >> tween.min.js
echo   update: function() {} >> tween.min.js
echo }; >> tween.min.js
echo ✅ 简化版Tween.js创建完成！

:end
cd ..
echo.
echo 现在可以运行程序了！
pause