@echo off
chcp 65001 >nul
:: GCC Compiler Installation Guide Script
:: Helps users install GCC and dependencies for Digital Human backend

echo ======================================
echo      GCC Compiler Installation Guide
echo ======================================
echo.

echo This script will guide you through installing GCC compiler
echo and required dependencies for the Digital Human backend.
echo.

:: Check current system
echo [Info] Checking current system...

:: Check if GCC already exists
where gcc >nul 2>&1
if not errorlevel 1 (
    echo [Info] GCC compiler found!
    gcc --version
    echo.
    echo GCC is already installed. You can run the full system.
    echo Use: start-all-fixed.bat
    pause
    exit /b 0
)

echo [Info] GCC compiler not found. Installation required.
echo.

:: Show installation options
echo ======================================
echo         Installation Options
echo ======================================
echo.
echo Option 1: MSYS2 (Recommended)
echo   - Complete development environment
echo   - Easy package management
echo   - Size: ~1GB
echo.
echo Option 2: MinGW-w64 Standalone
echo   - Minimal installation
echo   - Manual dependency management
echo   - Size: ~200MB
echo.
echo Option 3: Visual Studio Build Tools
echo   - Microsoft C++ compiler (alternative)
echo   - Requires code modification
echo   - Size: ~3GB
echo.

set /p choice="Choose installation option (1/2/3) or Q to quit: "

if /i "%choice%"=="q" (
    echo Installation cancelled.
    pause
    exit /b 0
)

if "%choice%"=="1" goto :install_msys2
if "%choice%"=="2" goto :install_mingw
if "%choice%"=="3" goto :install_vs
echo Invalid choice. Please run the script again.
pause
exit /b 1

:install_msys2
echo.
echo ======================================
echo         MSYS2 Installation
echo ======================================
echo.
echo Step 1: Download MSYS2
echo   Visit: https://www.msys2.org/
echo   Download: msys2-x86_64-latest.exe
echo.
echo Step 2: Install MSYS2
echo   Run the installer and follow instructions
echo   Default location: C:\msys64
echo.
echo Step 3: Update package database
echo   Open MSYS2 terminal and run:
echo   pacman -Syu
echo.
echo Step 4: Install development tools
echo   pacman -S mingw-w64-x86_64-toolchain
echo   pacman -S mingw-w64-x86_64-libwebsockets
echo   pacman -S mingw-w64-x86_64-json-c
echo.
echo Step 5: Add to PATH
echo   Add C:\msys64\mingw64\bin to system PATH
echo.
set /p continue="Press Enter to open MSYS2 download page..."
start "" "https://www.msys2.org/"
goto :end

:install_mingw
echo.
echo ======================================
echo        MinGW-w64 Installation
echo ======================================
echo.
echo Step 1: Download MinGW-w64
echo   Visit: https://www.mingw-w64.org/downloads/
echo   Recommended: MSYS2 (which includes MinGW-w64)
echo   Alternative: winlibs.com standalone builds
echo.
echo Step 2: Extract and install
echo   Extract to C:\mingw64 (or preferred location)
echo.
echo Step 3: Add to PATH
echo   Add C:\mingw64\bin to system PATH
echo.
echo Step 4: Install vcpkg for libraries
echo   git clone https://github.com/Microsoft/vcpkg.git
echo   cd vcpkg
echo   .\bootstrap-vcpkg.bat
echo   .\vcpkg install libwebsockets json-c
echo.
set /p continue="Press Enter to open MinGW-w64 download page..."
start "" "https://www.mingw-w64.org/downloads/"
goto :end

:install_vs
echo.
echo ======================================
echo    Visual Studio Build Tools
echo ======================================
echo.
echo Step 1: Download Visual Studio Build Tools
echo   Visit: https://visualstudio.microsoft.com/downloads/
echo   Download: Build Tools for Visual Studio
echo.
echo Step 2: Install C++ build tools
echo   Select "C++ build tools" workload
echo   Include Windows 10/11 SDK
echo.
echo Step 3: Install vcpkg
echo   git clone https://github.com/Microsoft/vcpkg.git
echo   cd vcpkg
echo   .\bootstrap-vcpkg.bat
echo   .\vcpkg install libwebsockets json-c
echo.
echo Step 4: Modify build script
echo   Use 'cl' instead of 'gcc' in compilation
echo.
set /p continue="Press Enter to open Visual Studio downloads..."
start "" "https://visualstudio.microsoft.com/downloads/"
goto :end

:end
echo.
echo ======================================
echo         Next Steps
echo ======================================
echo.
echo After installation:
echo 1. Restart command prompt/terminal
echo 2. Verify installation: gcc --version
echo 3. Run: start-all-fixed.bat
echo.
echo For immediate frontend-only testing:
echo   start-frontend-only.bat
echo.
echo Troubleshooting:
echo - Ensure PATH environment variable is updated
echo - Restart Windows if PATH changes don't take effect
echo - Check firewall settings for server ports
echo.
pause