@echo off
REM 3D Digital Human System - One-Click Startup Script
REM Auto-start frontend and backend services

echo ===========================================
echo     3D Digital Human System - Startup
echo ===========================================
echo.

REM Check if backend server exists
if not exist "digital_human_server.exe" (
    echo [WARNING] Backend server not compiled, compiling now...
    call build-server.bat
    if %errorlevel% neq 0 (
        echo [ERROR] Server compilation failed, please check environment
        pause
        exit /b 1
    )
)

REM Check model files
if not exist "models\RiggedSimple.glb" (
    echo [INFO] Missing 3D model detected, downloading...
    python download-models.py --all
    if %errorlevel% neq 0 (
        echo [WARNING] Model download failed, will use online models
    )
)

echo [INFO] Starting system components...
echo.

REM Start backend WebSocket server
echo [1/2] Starting WebSocket server (port 8080)...
start "Digital Human Backend Server" cmd /k "digital_human_server.exe 8080"

REM Wait for server startup
echo [WAIT] Server initializing...
timeout /t 3 /nobreak >nul

REM Start frontend HTTP server
echo [2/2] Starting frontend server (port 8000)...

REM Try Python first
python --version >nul 2>&1
if %errorlevel% equ 0 (
    echo [INFO] Using Python HTTP server...
    start "Digital Human Frontend Server" cmd /k "python -m http.server 8000"
) else (
    REM If no Python, try Node.js
    node --version >nul 2>&1
    if %errorlevel% equ 0 (
        echo [INFO] Using Node.js HTTP server...
        start "Digital Human Frontend Server" cmd /k "npx http-server -p 8000"
    ) else (
        echo [WARNING] Python or Node.js not found, manual HTTP server setup required
        echo You can:
        echo 1. Install Python and run: python -m http.server 8000
        echo 2. Install Node.js and run: npx http-server -p 8000
        echo 3. Open index-enterprise.html directly in browser
    )
)

REM Wait for complete server startup
echo [WAIT] Frontend server initializing...
timeout /t 5 /nobreak >nul

echo.
echo ===========================================
echo           系统启动完成！
echo ===========================================
echo.
echo 📌 访问地址:
echo   企业版界面: http://localhost:8000/index-enterprise.html
echo   (注意：标准版界面已移除，专注企业级功能)
echo.
echo 🔧 系统组件:
echo   WebSocket服务器: ws://localhost:8080
echo   前端服务器: http://localhost:8000
echo.
echo 📖 使用说明:
echo   1. 打开浏览器访问上述地址
echo   2. 等待3D模型加载完成
echo   3. 在侧边栏选择数字人模型
echo   4. 开始语音或文字交互
echo.
echo ⚠️  注意事项:
echo   - 首次使用需要下载3D模型
echo   - 建议使用Chrome或Edge浏览器
echo   - 语音功能需要麦克风权限
echo.

REM 询问是否立即打开浏览器
set /p choice=是否立即在浏览器中打开企业版界面？(Y/n): 
if /i "%choice%" neq "n" (
    echo [信息] 正在打开浏览器...
    echo [新功能] 高级面部表情系统已集成
    echo [新功能] 公司语音系统适配器已配置
    timeout /t 2 /nobreak >nul
    start http://localhost:8000/index-enterprise.html
)

echo.
echo ✅ 系统已完全启动！按任意键关闭此窗口。
echo    要停止系统，请关闭服务器窗口。
pause >nul