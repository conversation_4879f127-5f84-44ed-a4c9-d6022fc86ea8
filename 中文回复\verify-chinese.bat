@echo off
chcp 65001 >nul
echo 正在验证Claude Code中文配置...
echo.

echo 检查环境变量:
echo CLAUDE_LANGUAGE = %CLAUDE_LANGUAGE%
echo CLAUDE_CODE_LANGUAGE = %CLAUDE_CODE_LANGUAGE%
echo CLAUDE_RESPONSE_LANGUAGE = %CLAUDE_RESPONSE_LANGUAGE%
echo LANG = %LANG%
echo LC_ALL = %LC_ALL%
echo.

echo 检查配置文件:
set "found=0"

if exist "%USERPROFILE%\.config\claude-code\settings.json" (
    echo [√] 找到: %USERPROFILE%\.config\claude-code\settings.json
    set "found=1"
)

if exist "%APPDATA%\Claude Code\settings.json" (
    echo [√] 找到: %APPDATA%\Claude Code\settings.json
    set "found=1"
)

if exist "%LOCALAPPDATA%\Claude Code\settings.json" (
    echo [√] 找到: %LOCALAPPDATA%\Claude Code\settings.json
    set "found=1"
)

if exist "%USERPROFILE%\.claude-code\settings.json" (
    echo [√] 找到: %USERPROFILE%\.claude-code\settings.json
    set "found=1"
)

if "%found%"=="0" (
    echo [×] 未找到任何配置文件
    echo 请重新运行 setup-chinese.bat
) else (
    echo.
    echo [√] 中文配置验证完成
)

echo.
pause