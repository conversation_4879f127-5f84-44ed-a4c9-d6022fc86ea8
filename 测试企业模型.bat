@echo off
chcp 65001 >nul
echo.
echo ========================================
echo    企业数字人模型测试启动器
echo ========================================
echo.

echo 🔍 检查模型文件...
if not exist "models\" (
    echo ❌ models目录不存在！
    pause
    exit /b 1
)

echo ✅ 发现以下企业数字人模型:
echo.
for %%f in (models\*.glb) do (
    echo 📁 %%~nxf
)

echo.
echo 🚀 启动模型测试页面...
echo.

REM 检查是否有测试页面
if exist "test-model-loading-debug.html" (
    echo 🌐 启动调试测试页面...
    start "" "test-model-loading-debug.html"
) else if exist "index-complete-customer-service.html" (
    echo 🌐 启动客服系统测试页面...
    start "" "index-complete-customer-service.html"
) else if exist "debug-enterprise.html" (
    echo 🌐 启动企业调试页面...
    start "" "debug-enterprise.html"
) else (
    echo ⚠️ 未找到测试页面，启动简单HTTP服务器...
    
    REM 尝试启动Python HTTP服务器
    python -c "import http.server; import socketserver; socketserver.TCPServer(('', 8000), http.server.SimpleHTTPRequestHandler).serve_forever()" 2>nul
    if errorlevel 1 (
        echo ❌ 无法启动HTTP服务器
        echo 💡 请手动打开HTML文件测试模型
    ) else (
        echo 🌐 HTTP服务器已启动: http://localhost:8000
        start "" "http://localhost:8000"
    )
)

echo.
echo 📋 推荐测试的企业模型:
echo.
echo 🏢 客服数字人:
echo    - enterprise_female_rigged.glb (女性客服)
echo    - enterprise_male_simple.glb (男性客服)
echo    - cesium_man_professional.glb (专业男性)
echo.
echo 🎭 表情动画:
echo    - fox_expression_demo.glb (表情演示)
echo    - morph_animation_demo.glb (变形动画)
echo.
echo 🧠 专业头像:
echo    - brain_stem_avatar.glb (高级头部模型)
echo.
echo 💡 使用提示:
echo    1. 在浏览器中打开开发者工具查看加载日志
echo    2. 检查模型是否正确显示和动画
echo    3. 测试不同的光照和材质效果
echo    4. 验证性能表现和内存使用
echo.

pause
