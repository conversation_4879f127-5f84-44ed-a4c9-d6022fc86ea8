/**
 * 简化版语音适配器
 * 确保系统能正常运行的最小版本
 */
class VoiceAdapter {
    constructor(options = {}) {
        this.options = options;
        this.isInitialized = false;
        this.isProcessing = false;
        console.log('VoiceAdapter 简化版已创建');
        this.init();
    }
    
    async init() {
        try {
            this.isInitialized = true;
            console.log('VoiceAdapter 简化版初始化成功');
        } catch (error) {
            console.error('VoiceAdapter 初始化失败:', error);
        }
    }
    
    async processVoice(text, options = {}) {
        console.log('处理语音:', text);
        
        try {
            if ('speechSynthesis' in window) {
                const utterance = new SpeechSynthesisUtterance(text);
                utterance.lang = 'zh-CN';
                utterance.rate = options.rate || 1;
                utterance.pitch = options.pitch || 1;
                
                const playPromise = new Promise((resolve) => {
                    utterance.onend = () => resolve();
                    utterance.onerror = () => resolve();
                });
                
                window.speechSynthesis.speak(utterance);
                
                return {
                    source: 'web_api',
                    duration: text.length * 150,
                    playPromise: playPromise
                };
            } else {
                return {
                    source: 'none',
                    duration: 0,
                    playPromise: Promise.resolve()
                };
            }
        } catch (error) {
            console.error('语音处理失败:', error);
            return {
                source: 'error',
                duration: 0,
                playPromise: Promise.resolve()
            };
        }
    }
    
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            mode: 'simple',
            webAPIAvailable: 'speechSynthesis' in window
        };
    }
}

// 确保全局导出
if (typeof window !== 'undefined') {
    window.VoiceAdapter = VoiceAdapter;
    console.log('VoiceAdapter 已全局导出');
}

console.log('VoiceAdapter-simple.js 加载完成');