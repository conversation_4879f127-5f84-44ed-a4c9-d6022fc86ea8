# Claude Code 中文回复设置指南

本指南将帮助您设置Claude Code全局使用中文回复。

## 方法1：使用自动设置脚本（推荐）

1. 运行 `setup-chinese.bat` 脚本：
   ```
   双击运行 setup-chinese.bat
   ```

2. 等待脚本执行完成

3. 重新启动Claude Code

## 方法2：手动设置

### 1. 配置文件设置

将 `claude-code-settings.json` 复制到以下位置之一：

**Windows:**
- `%USERPROFILE%\.config\claude-code\settings.json`
- `%APPDATA%\Claude Code\settings.json`

**macOS/Linux:**
- `~/.config/claude-code/settings.json`

### 2. 环境变量设置

设置以下环境变量：
```bash
CLAUDE_LANGUAGE=zh-CN
LANG=zh_CN.UTF-8
LC_ALL=zh_CN.UTF-8
CLAUDE_CODE_LANGUAGE=chinese
```

## 配置文件内容说明

```json
{
  "language": "zh-CN",                    // 主要语言设置
  "locale": "zh-CN",                      // 地区设置
  "system_prompt": "请始终使用中文回复...", // 系统提示
  "response_language": "chinese",         // 回复语言
  "default_language": "zh-CN",           // 默认语言
  "ui_language": "zh-CN",               // 界面语言
  "conversation_language": "zh-CN"       // 对话语言
}
```

## 验证设置

1. 重新启动Claude Code
2. 输入任何问题，检查是否使用中文回复
3. 如果仍然是英文，请检查配置文件路径是否正确

## 故障排除

如果设置不生效：

1. 确认配置文件路径正确
2. 检查文件权限
3. 重新启动Claude Code
4. 清除缓存后重试

## 临时方法

如果以上方法都不起作用，您可以在每次对话开始时输入：
```
请使用中文回复我的所有问题
```