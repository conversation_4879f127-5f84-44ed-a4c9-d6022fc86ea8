# 3D数字人企业版系统 v2.1

*最新更新：2025-01-19 | 版本：2.1*

## 🚀 系统概述

本系统是专为企业级应用设计的3D全身真人数字人交互系统，集成了最新的面部表情技术、语音适配器和企业级功能。支持GLB/FBX模型、实时表情响应、多语言交互和公司语音系统集成。

### ✨ v2.1 新功能亮点

- **🎭 高级面部表情系统** - 基于FACS科学标准，支持17+动作单元和微表情
- **🗣️ 公司语音系统适配器** - 无缝集成公司语音API，支持降级到Web API
- **💼 企业专业模式** - 商务场景优化，文化敏感性适配
- **⚡ 实时响应** - 低延迟（<150ms）表情和语音处理
- **🤖 AI情感分析** - 智能文本情感识别和表情生成

---

## 🚀 快速开始

### 一键启动

```bash
# 方式1：使用一键启动脚本（推荐）
start-system.bat

# 方式2：手动启动
# 1. 启动后端服务器
./digital_human_server.exe 8080

# 2. 启动前端服务
python -m http.server 8000

# 3. 在浏览器中访问
http://localhost:8000/index-enterprise.html
```

### 系统要求

| 组件 | 要求 | 说明 |
|------|------|---------|
| **浏览器** | Chrome 90+, Firefox 88+, Safari 14+ | 需要WebGL 2.0支持 |
| **操作系统** | Windows 10/11, macOS 10.15+, Linux | 跨平台支持 |
| **内存** | 4GB RAM（推荐8GB+） | 支持3D模型和实时处理 |
| **网络** | 稳定的互联网连接 | 模型下载和API调用 |

---

## 🎯 核心功能

### 1. 高级面部表情系统

#### FACS动作单元支持
- **17+标准动作单元** - 基于Paul Ekman科学标准
- **微表情检测** - 1/25秒级别的细微变化
- **实时响应** - 低于150ms的处理延迟
- **智能混合** - 多表情自然过渡

#### 企业专业表情库
| 表情类型 | 适用场景 | 特点 |
|---------|---------|------|
| 职业微笑 | 客户接待、商务问候 | 温和专业，文化适配 |
| 专注倾听 | 会议讨论、客户咨询 | 认真投入，信任建立 |
| 自信表达 | 产品介绍、方案演示 | 自信清晰，说服力强 |
| 深度思考 | 问题分析、决策讨论 | 沉稳理性，专业形象 |
| 友好愉悦 | 轻松交流、成功庆祝 | 亲和友善，团队协作 |

### 2. 公司语音系统适配器

#### 多模式支持
- **公司集成模式** - 优先使用公司语音API
- **Web API降级** - 自动降级到浏览器API
- **混合模式** - 智能选择最优方案

#### 语音处理功能
- **文字转语音(TTS)** - 支持多种音色和语速
- **语音识别(ASR)** - 实时语音转文字
- **口型同步** - 精确的唇形匹配
- **情感语调** - 根据文本内容调整语调

### 3. 企业级交互界面

#### 界面组件
- **模型选择器** - 多种数字人模型
- **表情控制面板** - 实时表情调节
- **语音交互区域** - 文字/语音输入
- **FACS高级控制** - 精细表情调节
- **系统状态监控** - 性能和连接状态

---

## 💻 安装配置

### 项目结构

```
数字人4/
├── 📁 数字人/                     # 核心代码目录
│   ├── index-enterprise.html      # 企业版主页面
│   ├── EnterpriseDigitalHuman.js  # 数字人核心类
│   ├── AdvancedFacialExpressionSystem.js  # 高级表情系统
│   └── VoiceAdapter.js            # 语音适配器
├── 📁 models/                     # 3D模型文件
│   ├── RiggedSimple.glb          # 默认数字人模型
│   └── (其他GLB/FBX模型)
├── 📁 中文回复/                   # 中文环境配置
│   └── claude-code-settings.json # Claude Code配置
├── 📄 CLAUDE.md                  # 开发指引文档
├── 📄 高级表情系统使用指南.md        # 表情系统指南
├── 📄 公司提供资料.txt              # 公司技术要求
└── 📄 start-system.bat           # 一键启动脚本
```

---

## 📖 使用指南

### 基础操作

#### 1. 启动系统
```bash
# 使用一键启动脚本
start-system.bat

# 系统会自动：
# - 编译后端服务器（如需要）
# - 下载3D模型（如缺失）
# - 启动WebSocket服务（端口8080）
# - 启动HTTP服务（端口8000）
# - 打开浏览器页面
```

#### 2. 表情控制
```javascript
// 设置企业专业表情
app.digitalHuman.setEmotion('professional_smile', 0.8);

// 播放微表情
app.digitalHuman.facialExpressionSystem.playMicroExpression('doubt', {
    intensity: 0.3,
    duration: 200
});

// 基于文本自动生成表情
await app.digitalHuman.facialExpressionSystem.analyzeAndExpressFromText(
    "很高兴为您介绍我们的新产品",
    { isSpeaking: true, context: { scenario: 'presentation' } }
);
```

#### 3. 语音交互
```javascript
// 文字转语音
app.digitalHuman.speak("您好，欢迎来到我们公司！", {
    emotion: 'professional_smile',
    context: { phase: 'greeting', scenario: 'reception' }
});

// 语音识别
const result = await app.digitalHuman.voiceAdapter.recognizeSpeech({
    lang: 'zh-CN',
    continuous: false
});
console.log('识别结果:', result.text);
```

---

## 🔧 API文档

### EnterpriseDigitalHuman 核心API

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| `speak(text, options)` | text: 字符串, options: 配置对象 | Promise | 语音播放 |
| `setEmotion(emotion, intensity, options)` | emotion: 表情名, intensity: 强度, options: 选项 | void | 设置表情 |
| `moveTo(x, z, duration)` | x: X坐标, z: Z坐标, duration: 时长 | void | 移动位置 |
| `playAnimation(name, loop)` | name: 动画名, loop: 是否循环 | void | 播放动画 |
| `getPosition()` | 无 | Object | 获取当前位置 |
| `getFPS()` | 无 | Number | 获取帧率 |

### AdvancedFacialExpressionSystem API

| 方法 | 说明 | 示例 |
|------|------|------|
| `expressEmotion(emotion, intensity, options)` | 设置基础表情 | `expressEmotion('happiness', 0.8)` |
| `playMicroExpression(type, options)` | 播放微表情 | `playMicroExpression('doubt', {duration: 200})` |
| `analyzeAndExpressFromText(text, context)` | 文本情感分析 | `analyzeAndExpressFromText('很高兴见到您')` |
| `resetToNeutral(duration)` | 重置中性表情 | `resetToNeutral(500)` |
| `setConfig(config)` | 配置系统参数 | `setConfig({businessMode: true})` |

---

## 📊 技术规范

### 性能基准

| 指标 | 目标值 | 说明 |
|------|--------|------|
| **帧率** | ≥30fps | 流畅的3D渲染体验 |
| **内存使用** | ≤512MB | 支持中低端设备 |
| **加载时间** | ≤3秒 | 首次模型加载 |
| **响应延迟** | ≤150ms | 表情和语音响应 |
| **WebSocket延迟** | ≤100ms | 实时通信要求 |

### 浏览器兼容性

| 浏览器 | 最低版本 | WebGL支持 | 推荐度 |
|--------|----------|----------|--------|
| Chrome | 90+ | WebGL 2.0 | ⭐⭐⭐⭐⭐ |
| Firefox | 88+ | WebGL 2.0 | ⭐⭐⭐⭐ |
| Safari | 14+ | WebGL 2.0 | ⭐⭐⭐ |
| Edge | 90+ | WebGL 2.0 | ⭐⭐⭐⭐ |

---

## 🐛 故障排除

### 常见问题

#### 1. 模型无法加载
**症状**: 页面显示加载失败或模型不显示

**解决方案**:
```bash
# 检查模型文件是否存在
ls models/RiggedSimple.glb

# 重新下载模型
python download-models.py --all
```

#### 2. 表情无变化
**症状**: 点击表情按钮无反应

**诊断步骤**:
```javascript
// 检查变形目标
console.log('可用变形目标:', app.digitalHuman.facialExpressionSystem.morphTargets.size);

// 检查系统状态
console.log('表情系统状态:', app.digitalHuman.facialExpressionSystem.getSystemStatus());

// 启用调试模式
localStorage.setItem('facialExpression_debug', 'true');
```

#### 3. 语音功能异常
**症状**: 语音播放失败或识别不工作

**解决方案**:
```javascript
// 检查语音适配器状态
const voiceStatus = app.digitalHuman.voiceAdapter.getStatus();
console.log('语音系统状态:', voiceStatus);

// 测试Web API降级
app.digitalHuman.voiceAdapter.options.mode = 'web_api';
```

---

## 📞 技术支持

### 开发资源
- **详细指南**: 参考 `高级表情系统使用指南.md`
- **开发文档**: 查看 `CLAUDE.md` 开发指引
- **API参考**: 源码注释详细说明
- **示例代码**: 参考 `index-enterprise.html` 实现

### 系统监控
- **状态检查**: 界面右上角信息面板
- **性能指标**: FPS和延迟监控
- **错误日志**: 浏览器开发者工具控制台

---

## 🗺️ 发展路线图

### 已完成功能 ✅
- [x] 高级面部表情系统（FACS + AI）
- [x] 公司语音系统适配器
- [x] 企业级用户界面
- [x] GLB/FBX模型支持
- [x] 实时语音交互
- [x] 微表情检测
- [x] 性能优化和监控

### 计划功能 🚧

#### Q2 2025
- [ ] 实时面部捕捉集成
- [ ] 高级AI情感引擎
- [ ] 语音情感同步技术
- [ ] 3D表情预设库扩展

#### Q3 2025
- [ ] VR/AR平台支持
- [ ] 多人协作功能
- [ ] 云端模型库
- [ ] 高级动画编辑器

---

*文档版本：v2.1 | 最后更新：2025-01-19 | 持续更新中*