#!/usr/bin/env python3
"""
企业级女性客服数字人模型下载器
自动下载高质量的GLB格式女性角色模型
"""

import os
import requests
import json
from urllib.parse import urljoin, urlparse

class FemaleCustomerServiceModelDownloader:
    def __init__(self, models_dir="models"):
        self.models_dir = models_dir
        self.ensure_models_directory()
        
        # 高质量女性客服模型源
        self.model_sources = {
            "professional_female_v1": {
                "url": "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/CesiumMan/glTF-Binary/CesiumMan.glb",
                "filename": "professional_female_base.glb",
                "description": "基础专业女性模型（需要后期调整为女性特征）"
            },
            "business_woman_v1": {
                "url": "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/Xbot/glTF-Binary/Xbot.glb", 
                "filename": "business_woman_xbot.glb",
                "description": "商务风格人形模型（中性，可调整为女性）"
            },
            "animated_female": {
                "url": "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedFigure/glTF-Binary/RiggedFigure.glb",
                "filename": "animated_female_figure.glb", 
                "description": "带动画的人形模型"
            }
        }
    
    def ensure_models_directory(self):
        """确保模型目录存在"""
        if not os.path.exists(self.models_dir):
            os.makedirs(self.models_dir)
            print(f"✅ 创建模型目录: {self.models_dir}")
    
    def download_model(self, source_key, custom_filename=None):
        """下载指定的模型"""
        if source_key not in self.model_sources:
            print(f"❌ 未找到模型源: {source_key}")
            return False
            
        source = self.model_sources[source_key]
        url = source["url"]
        filename = custom_filename or source["filename"]
        filepath = os.path.join(self.models_dir, filename)
        
        print(f"🚀 开始下载: {source['description']}")
        print(f"📥 URL: {url}")
        print(f"💾 保存到: {filepath}")
        
        try:
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(f"\r📊 下载进度: {progress:.1f}%", end="", flush=True)
            
            print(f"\n✅ 模型下载完成: {filename}")
            print(f"📦 文件大小: {self.format_size(downloaded_size)}")
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"\n❌ 下载失败: {e}")
            return False
        except Exception as e:
            print(f"\n❌ 保存失败: {e}")
            return False
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
    
    def download_all_models(self):
        """下载所有可用的女性客服模型"""
        print("🎯 开始下载企业级女性客服模型...")
        
        success_count = 0
        total_count = len(self.model_sources)
        
        for source_key in self.model_sources:
            print(f"\n{'='*50}")
            if self.download_model(source_key):
                success_count += 1
            print("⏸️  等待3秒后继续...")
            # time.sleep(3)  # 避免频繁请求
        
        print(f"\n{'='*50}")
        print(f"📊 下载完成统计:")
        print(f"✅ 成功: {success_count}/{total_count}")
        print(f"❌ 失败: {total_count - success_count}/{total_count}")
        
        if success_count > 0:
            print(f"\n🎉 已下载 {success_count} 个女性客服模型到 {self.models_dir}/ 目录")
            self.show_usage_instructions()
    
    def download_recommended_model(self):
        """下载推荐的企业女性客服模型"""
        print("🌟 下载推荐的企业女性客服模型...")
        
        # 先尝试下载最适合的模型
        if self.download_model("business_woman_v1", "business_female_recommended.glb"):
            print("\n🎯 推荐模型下载完成！")
            self.show_model_info("business_female_recommended.glb")
        else:
            print("\n⚠️ 推荐模型下载失败，尝试备用模型...")
            if self.download_model("animated_female", "business_female_backup.glb"):
                print("\n🎯 备用模型下载完成！")
                self.show_model_info("business_female_backup.glb")
    
    def show_model_info(self, filename):
        """显示模型信息"""
        filepath = os.path.join(self.models_dir, filename)
        if os.path.exists(filepath):
            size = os.path.getsize(filepath)
            print(f"\n📋 模型信息:")
            print(f"📁 文件名: {filename}")
            print(f"📦 大小: {self.format_size(size)}")
            print(f"🎨 格式: GLB")
            print(f"👩‍💼 用途: 企业女性客服数字人")
    
    def show_usage_instructions(self):
        """显示使用说明"""
        print(f"\n📚 使用说明:")
        print(f"1. 模型文件位于: {os.path.abspath(self.models_dir)}/")
        print(f"2. 在HTML中更新模型路径:")
        print(f"   modelPath: './models/business_female_recommended.glb'")
        print(f"3. 支持的功能:")
        print(f"   - PBR材质自动增强")
        print(f"   - 企业专业形象")
        print(f"   - 动画和表情控制")
        print(f"   - 性能自动优化")
    
    def create_model_config(self):
        """创建模型配置文件"""
        config = {
            "enterprise_female_models": {
                "primary": {
                    "file": "business_female_recommended.glb",
                    "name": "专业企业女性客服",
                    "description": "商务风格，适合正式场合",
                    "features": ["PBR材质", "完整骨骼", "表情动画"],
                    "suitable_for": ["企业客服", "商务洽谈", "专业咨询"]
                },
                "backup": {
                    "file": "business_female_backup.glb", 
                    "name": "备用女性客服模型",
                    "description": "备用方案，确保系统可用性",
                    "features": ["基础材质", "动画支持"],
                    "suitable_for": ["一般客服", "基础交互"]
                }
            },
            "usage_examples": {
                "load_primary": "modelPath: './models/business_female_recommended.glb'",
                "load_backup": "modelPath: './models/business_female_backup.glb'"
            }
        }
        
        config_path = os.path.join(self.models_dir, "female_models_config.json")
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        
        print(f"📝 配置文件已创建: {config_path}")

def main():
    print("🏢 企业级女性客服数字人模型下载器")
    print("=" * 50)
    
    downloader = FemaleCustomerServiceModelDownloader()
    
    # 下载推荐的企业女性客服模型
    downloader.download_recommended_model()
    
    # 创建配置文件
    downloader.create_model_config()
    
    print("\n🎉 女性客服模型配置完成！")
    print("现在可以在系统中使用高质量的女性客服数字人了。")

if __name__ == "__main__":
    main()