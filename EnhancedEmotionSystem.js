/**
 * 增强情感识别与表情响应系统
 * 专门为企业客服数字人设计的情感智能系统
 */

class EnhancedEmotionSystem {
    constructor(digitalHuman) {
        this.digitalHuman = digitalHuman;
        this.emotionDetector = new RealTimeEmotionDetector();
        this.expressionMapper = new CustomerServiceExpressionMapper();
        this.responseGenerator = new EmotionalResponseGenerator();
        this.contextMemory = new EmotionContextMemory();
        
        // 企业客服情感配置
        this.serviceConfig = {
            empathyLevel: 0.8,          // 同理心水平
            professionalismLevel: 0.9,   // 专业度水平
            responseDelay: 200,          // 情感响应延迟(ms)
            expressionIntensity: 0.6,    // 表情强度(企业适中)
            culturalSensitivity: 'chinese_business' // 文化敏感度
        };
        
        // 情感状态管理
        this.currentEmotion = {
            customer: { type: 'neutral', intensity: 0.5, confidence: 0.0 },
            agent: { type: 'professional_ready', intensity: 0.7, confidence: 1.0 }
        };
        
        // 情感历史
        this.emotionHistory = [];
        this.isActive = true;
        
        this.init();
    }
    
    init() {
        this.setupEmotionMappings();
        this.initializeCustomerServiceExpressions();
        this.startEmotionMonitoring();
        console.log('🧠 增强情感识别系统已启动');
    }
    
    /**
     * 设置企业客服情感映射
     */
    setupEmotionMappings() {
        // 客户情感 -> 客服响应映射
        this.emotionResponseMap = {
            // 客户积极情感
            'satisfied': {
                expression: 'warm_service_smile',
                response: 'positive_acknowledgment',
                gesture: 'confirming_nod',
                voiceTone: 'warm_professional'
            },
            
            'happy': {
                expression: 'genuine_smile',
                response: 'shared_positivity',
                gesture: 'open_gesture',
                voiceTone: 'cheerful_professional'
            },
            
            'grateful': {
                expression: 'humble_smile',
                response: 'gracious_acceptance',
                gesture: 'slight_bow',
                voiceTone: 'modest_warm'
            },
            
            // 客户消极情感
            'frustrated': {
                expression: 'empathetic_concern',
                response: 'understanding_support',
                gesture: 'calming_gesture',
                voiceTone: 'soothing_professional'
            },
            
            'angry': {
                expression: 'calm_attentive',
                response: 'de_escalation',
                gesture: 'open_palms',
                voiceTone: 'calm_steady'
            },
            
            'confused': {
                expression: 'patient_explaining',
                response: 'clarification_offer',
                gesture: 'explanatory_gesture',
                voiceTone: 'clear_patient'
            },
            
            'disappointed': {
                expression: 'sincere_regret',
                response: 'solution_focused',
                gesture: 'reassuring_gesture',
                voiceTone: 'sincere_helpful'
            },
            
            'worried': {
                expression: 'reassuring_confidence',
                response: 'confidence_building',
                gesture: 'stable_presence',
                voiceTone: 'confident_reassuring'
            },
            
            // 中性情感
            'neutral': {
                expression: 'professional_ready',
                response: 'attentive_listening',
                gesture: 'attentive_posture',
                voiceTone: 'professional_friendly'
            },
            
            'curious': {
                expression: 'engaged_interest',
                response: 'information_sharing',
                gesture: 'informative_gesture',
                voiceTone: 'engaging_informative'
            }
        };
    }
    
    /**
     * 初始化客服专用表情
     */
    initializeCustomerServiceExpressions() {
        this.serviceExpressions = {
            // 专业微笑系列
            'professional_ready': {
                description: '专业待命状态',
                auSettings: [
                    { au: 'AU12', intensity: 0.4 }, // 轻微嘴角上扬
                    { au: 'AU6', intensity: 0.3 },  // 轻微面颊上提
                    { au: 'AU1', intensity: 0.1 }   // 微微内眉上扬(专注)
                ]
            },
            
            'warm_service_smile': {
                description: '温暖服务微笑',
                auSettings: [
                    { au: 'AU12', intensity: 0.7 }, // 明显嘴角上扬
                    { au: 'AU6', intensity: 0.6 },  // 面颊上提
                    { au: 'AU25', intensity: 0.2 }  // 轻微张唇
                ]
            },
            
            'empathetic_concern': {
                description: '同理心关切',
                auSettings: [
                    { au: 'AU1', intensity: 0.5 },  // 内眉上扬
                    { au: 'AU4', intensity: 0.3 },  // 轻微眉头皱起
                    { au: 'AU12', intensity: 0.3 }  // 温和微笑
                ]
            },
            
            'calm_attentive': {
                description: '冷静专注',
                auSettings: [
                    { au: 'AU1', intensity: 0.2 },  // 轻微内眉上扬
                    { au: 'AU5', intensity: 0.3 },  // 上眼睑轻微提升
                    { au: 'AU23', intensity: 0.2 }  // 唇部轻微收紧(专注)
                ]
            },
            
            'patient_explaining': {
                description: '耐心解释状态',
                auSettings: [
                    { au: 'AU2', intensity: 0.4 },  // 外眉上扬
                    { au: 'AU12', intensity: 0.4 }, // 友善微笑
                    { au: 'AU25', intensity: 0.4 }, // 说话时张唇
                    { au: 'AU26', intensity: 0.2 }  // 轻微张嘴
                ]
            },
            
            'reassuring_confidence': {
                description: '安心自信',
                auSettings: [
                    { au: 'AU12', intensity: 0.6 }, // 自信微笑
                    { au: 'AU2', intensity: 0.3 },  // 轻微外眉上扬
                    { au: 'AU6', intensity: 0.4 }   // 面颊上提
                ]
            },
            
            'sincere_regret': {
                description: '真诚歉意',
                auSettings: [
                    { au: 'AU1', intensity: 0.6 },  // 内眉上扬
                    { au: 'AU4', intensity: 0.4 },  // 眉头轻皱
                    { au: 'AU15', intensity: 0.3 }, // 轻微嘴角下压
                    { au: 'AU12', intensity: 0.2 }  // 微弱安慰性微笑
                ]
            }
        };
    }
    
    /**
     * 实时分析客户情感
     */
    async analyzeCustomerEmotion(input, context = {}) {
        try {
            // 1. 文本情感分析
            const textEmotion = await this.analyzeTextEmotion(input);
            
            // 2. 上下文情感分析
            const contextEmotion = this.analyzeContextualEmotion(context);
            
            // 3. 历史情感趋势分析
            const trendEmotion = this.analyzeTrendEmotion();
            
            // 4. 综合情感评估
            const finalEmotion = this.synthesizeEmotions([
                { emotion: textEmotion, weight: 0.6 },
                { emotion: contextEmotion, weight: 0.3 },
                { emotion: trendEmotion, weight: 0.1 }
            ]);
            
            // 5. 更新情感历史
            this.updateEmotionHistory(finalEmotion);
            
            // 6. 生成客服响应
            await this.generateServiceResponse(finalEmotion);
            
            return finalEmotion;
            
        } catch (error) {
            console.error('情感分析错误:', error);
            return this.getDefaultNeutralEmotion();
        }
    }
    
    /**
     * 文本情感分析
     */
    async analyzeTextEmotion(text) {
        // 企业客服专用情感关键词
        const emotionKeywords = {
            satisfied: ['满意', '不错', '好的', '可以', '行', '谢谢', '感谢'],
            happy: ['高兴', '开心', '太好了', '完美', '棒', '赞'],
            grateful: ['谢谢', '感谢', '感激', '麻烦了', '辛苦了'],
            
            frustrated: ['烦', '麻烦', '复杂', '太难了', '搞不懂', '为什么'],
            angry: ['生气', '愤怒', '气死了', '太过分', '什么意思', '投诉'],
            confused: ['不懂', '不明白', '什么意思', '怎么', '为什么', '搞不清'],
            disappointed: ['失望', '不行', '不好', '差', '糟糕', '不满意'],
            worried: ['担心', '害怕', '不安', '忧虑', '不放心'],
            
            neutral: ['了解', '知道', '明白', '好吧', '这样'],
            curious: ['想了解', '想知道', '请问', '能否', '可以介绍']
        };
        
        let dominantEmotion = 'neutral';
        let maxScore = 0;
        let totalMatches = 0;
        
        // 计算各情感得分
        Object.entries(emotionKeywords).forEach(([emotion, keywords]) => {
            const matches = keywords.filter(keyword => text.includes(keyword)).length;
            totalMatches += matches;
            
            if (matches > maxScore) {
                maxScore = matches;
                dominantEmotion = emotion;
            }
        });
        
        // 计算置信度和强度
        const confidence = totalMatches > 0 ? Math.min(maxScore / totalMatches, 1.0) : 0.3;
        const intensity = this.calculateEmotionIntensity(text, dominantEmotion);
        
        return {
            type: dominantEmotion,
            intensity: intensity,
            confidence: confidence,
            source: 'text_analysis'
        };
    }
    
    /**
     * 计算情感强度
     */
    calculateEmotionIntensity(text, emotionType) {
        // 基础强度
        let baseIntensity = 0.5;
        
        // 感叹号增强情感
        const exclamationCount = (text.match(/[！!]/g) || []).length;
        baseIntensity += exclamationCount * 0.1;
        
        // 重复字符增强情感 (如：太太太好了)
        const repetitionPattern = /(.)\1{2,}/g;
        const repetitions = (text.match(repetitionPattern) || []).length;
        baseIntensity += repetitions * 0.15;
        
        // 情感修饰词
        const intensifiers = ['非常', '特别', '极其', '超级', '太', '很', '真的'];
        const intensifierCount = intensifiers.filter(word => text.includes(word)).length;
        baseIntensity += intensifierCount * 0.1;
        
        // 企业客服环境下的强度调节
        if (['angry', 'frustrated'].includes(emotionType)) {
            baseIntensity = Math.min(baseIntensity * 0.8, 0.8); // 企业环境下降低负面情感强度
        }
        
        return Math.min(Math.max(baseIntensity, 0.1), 1.0);
    }
    
    /**
     * 生成客服响应
     */
    async generateServiceResponse(customerEmotion) {
        const responseConfig = this.emotionResponseMap[customerEmotion.type];
        if (!responseConfig) return;
        
        // 1. 设置面部表情
        await this.setServiceExpression(responseConfig.expression, customerEmotion);
        
        // 2. 设置身体语言
        await this.setServiceGesture(responseConfig.gesture);
        
        // 3. 调整语音语调
        this.adjustVoiceTone(responseConfig.voiceTone);
        
        // 4. 更新数字人状态
        this.updateAgentEmotionalState(responseConfig.response, customerEmotion);
        
        console.log(`💼 客服响应: ${customerEmotion.type} -> ${responseConfig.expression}`);
    }
    
    /**
     * 设置客服表情
     */
    async setServiceExpression(expressionType, customerEmotion) {
        const expression = this.serviceExpressions[expressionType];
        if (!expression || !this.digitalHuman) return;
        
        // 根据客户情感强度调整表情强度
        const adjustedIntensity = this.calculateResponseIntensity(customerEmotion);
        
        // 应用表情
        if (this.digitalHuman.setEmotion) {
            this.digitalHuman.setEmotion(expressionType, adjustedIntensity, {
                duration: 1200,
                context: 'customer_service_response',
                customerEmotion: customerEmotion.type
            });
        }
        
        // 直接控制面部动作单元 (如果支持高级表情系统)
        if (this.digitalHuman.facialExpressionSystem) {
            await this.applyDetailedExpression(expression, adjustedIntensity);
        }
    }
    
    /**
     * 应用详细表情
     */
    async applyDetailedExpression(expression, intensity) {
        if (!this.digitalHuman.facialExpressionSystem) return;
        
        const facialSystem = this.digitalHuman.facialExpressionSystem;
        
        // 逐个应用动作单元
        for (const auSetting of expression.auSettings) {
            const adjustedIntensity = auSetting.intensity * intensity;
            
            if (facialSystem.facsController) {
                facialSystem.facsController.activateActionUnit(
                    auSetting.au, 
                    adjustedIntensity
                );
            }
            
            // 小延迟以创建自然的表情变化
            await new Promise(resolve => setTimeout(resolve, 50));
        }
    }
    
    /**
     * 设置客服手势
     */
    async setServiceGesture(gestureType) {
        if (!this.digitalHuman || !gestureType) return;
        
        const serviceGestures = {
            'confirming_nod': () => this.performNodGesture(),
            'open_gesture': () => this.performOpenGesture(),
            'calming_gesture': () => this.performCalmingGesture(),
            'explanatory_gesture': () => this.performExplanatoryGesture(),
            'reassuring_gesture': () => this.performReassuringGesture(),
            'attentive_posture': () => this.performAttentivePosture()
        };
        
        const gestureFunction = serviceGestures[gestureType];
        if (gestureFunction) {
            await gestureFunction();
        }
    }
    
    /**
     * 执行点头手势
     */
    async performNodGesture() {
        // 通过动画系统实现点头
        if (this.digitalHuman.playAnimation) {
            this.digitalHuman.playAnimation('greeting'); // 使用现有的问候动画
        }
    }
    
    /**
     * 执行开放手势
     */
    async performOpenGesture() {
        if (this.digitalHuman.playHandGesture) {
            this.digitalHuman.playHandGesture('wave_right');
        }
    }
    
    /**
     * 执行安抚手势
     */
    async performCalmingGesture() {
        if (this.digitalHuman.playHandGesture) {
            this.digitalHuman.playHandGesture('hold_object'); // 使用温和的手势
        }
    }
    
    /**
     * 执行解释手势
     */
    async performExplanatoryGesture() {
        if (this.digitalHuman.playAnimation) {
            this.digitalHuman.playAnimation('presentation');
        }
    }
    
    /**
     * 计算响应强度
     */
    calculateResponseIntensity(customerEmotion) {
        let baseIntensity = this.serviceConfig.expressionIntensity;
        
        // 根据客户情感强度调整
        const customerIntensityFactor = customerEmotion.intensity * 0.3;
        
        // 根据情感类型调整
        const emotionFactors = {
            angry: 0.7,      // 愤怒客户需要冷静回应
            frustrated: 0.8, // 沮丧客户需要温和回应
            satisfied: 1.2,  // 满意客户可以更积极回应
            happy: 1.1,      // 开心客户适度回应
            worried: 0.9     // 担心客户需要稳重回应
        };
        
        const emotionFactor = emotionFactors[customerEmotion.type] || 1.0;
        
        const finalIntensity = (baseIntensity + customerIntensityFactor) * emotionFactor;
        
        return Math.min(Math.max(finalIntensity, 0.3), 0.9); // 企业环境下保持适度
    }
    
    /**
     * 调整语音语调
     */
    adjustVoiceTone(toneType) {
        const voiceSettings = {
            'warm_professional': { rate: 0.9, pitch: 1.1 },
            'cheerful_professional': { rate: 1.0, pitch: 1.2 },
            'soothing_professional': { rate: 0.8, pitch: 0.9 },
            'calm_steady': { rate: 0.85, pitch: 1.0 },
            'clear_patient': { rate: 0.8, pitch: 1.0 },
            'confident_reassuring': { rate: 0.9, pitch: 1.0 },
            'professional_friendly': { rate: 0.95, pitch: 1.05 }
        };
        
        const settings = voiceSettings[toneType] || voiceSettings['professional_friendly'];
        
        // 存储语音设置供后续使用
        this.currentVoiceSettings = settings;
    }
    
    /**
     * 更新客服情感状态
     */
    updateAgentEmotionalState(responseType, customerEmotion) {
        this.currentEmotion.customer = customerEmotion;
        this.currentEmotion.agent = {
            type: responseType,
            intensity: this.calculateResponseIntensity(customerEmotion),
            confidence: 0.9
        };
    }
    
    /**
     * 综合多源情感数据
     */
    synthesizeEmotions(emotionSources) {
        let weightedEmotion = {
            type: 'neutral',
            intensity: 0.5,
            confidence: 0.0
        };
        
        let totalWeight = 0;
        let emotionScores = {};
        
        // 计算加权情感得分
        emotionSources.forEach(source => {
            const emotion = source.emotion;
            const weight = source.weight;
            
            if (!emotionScores[emotion.type]) {
                emotionScores[emotion.type] = 0;
            }
            
            emotionScores[emotion.type] += emotion.intensity * emotion.confidence * weight;
            totalWeight += weight;
        });
        
        // 找出最高得分的情感
        let maxScore = 0;
        Object.entries(emotionScores).forEach(([emotionType, score]) => {
            if (score > maxScore) {
                maxScore = score;
                weightedEmotion.type = emotionType;
            }
        });
        
        // 计算综合强度和置信度
        weightedEmotion.intensity = Math.min(maxScore / totalWeight, 1.0);
        weightedEmotion.confidence = Math.min(maxScore / (totalWeight * 0.8), 1.0);
        
        return weightedEmotion;
    }
    
    /**
     * 更新情感历史
     */
    updateEmotionHistory(emotion) {
        this.emotionHistory.push({
            timestamp: Date.now(),
            emotion: emotion,
            context: 'customer_interaction'
        });
        
        // 保持历史记录在合理范围内
        if (this.emotionHistory.length > 20) {
            this.emotionHistory = this.emotionHistory.slice(-15);
        }
    }
    
    /**
     * 分析情感趋势
     */
    analyzeTrendEmotion() {
        if (this.emotionHistory.length < 2) {
            return this.getDefaultNeutralEmotion();
        }
        
        const recentEmotions = this.emotionHistory.slice(-3);
        const trendTypes = recentEmotions.map(record => record.emotion.type);
        
        // 检测情感趋势
        if (trendTypes.every(type => ['angry', 'frustrated', 'disappointed'].includes(type))) {
            return {
                type: 'escalating_negative',
                intensity: 0.8,
                confidence: 0.7,
                source: 'trend_analysis'
            };
        }
        
        if (trendTypes.every(type => ['satisfied', 'happy', 'grateful'].includes(type))) {
            return {
                type: 'positive_trend',
                intensity: 0.7,
                confidence: 0.6,
                source: 'trend_analysis'
            };
        }
        
        return {
            type: 'stable_interaction',
            intensity: 0.5,
            confidence: 0.4,
            source: 'trend_analysis'
        };
    }
    
    /**
     * 获取默认中性情感
     */
    getDefaultNeutralEmotion() {
        return {
            type: 'neutral',
            intensity: 0.5,
            confidence: 0.5,
            source: 'default'
        };
    }
    
    /**
     * 开始情感监控
     */
    startEmotionMonitoring() {
        // 定期检查情感状态
        setInterval(() => {
            if (this.isActive) {
                this.performEmotionMaintenance();
            }
        }, 5000);
    }
    
    /**
     * 情感维护
     */
    performEmotionMaintenance() {
        // 如果长时间没有交互，回到专业待命状态
        const lastInteraction = this.emotionHistory[this.emotionHistory.length - 1];
        const timeSinceLastInteraction = lastInteraction ? 
            Date.now() - lastInteraction.timestamp : Infinity;
        
        if (timeSinceLastInteraction > 30000) { // 30秒无交互
            this.resetToProfessionalState();
        }
    }
    
    /**
     * 重置到专业状态
     */
    resetToProfessionalState() {
        if (this.digitalHuman && this.digitalHuman.setEmotion) {
            this.digitalHuman.setEmotion('professional_ready', 0.6, {
                duration: 2000,
                context: 'maintenance_reset'
            });
        }
        
        this.currentEmotion.agent = {
            type: 'professional_ready',
            intensity: 0.6,
            confidence: 1.0
        };
    }
    
    /**
     * 获取当前情感状态
     */
    getCurrentEmotionState() {
        return {
            customer: this.currentEmotion.customer,
            agent: this.currentEmotion.agent,
            history: this.emotionHistory.slice(-5),
            config: this.serviceConfig
        };
    }
    
    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        this.serviceConfig = { ...this.serviceConfig, ...newConfig };
        console.log('🔧 情感系统配置已更新:', newConfig);
    }
    
    /**
     * 销毁情感系统
     */
    destroy() {
        this.isActive = false;
        this.emotionHistory = [];
        this.currentEmotion = null;
        console.log('🔄 情感识别系统已销毁');
    }
}

/**
 * 实时情感检测器
 */
class RealTimeEmotionDetector {
    constructor() {
        this.isActive = false;
    }
    
    startDetection() {
        this.isActive = true;
        console.log('🔍 实时情感检测已启动');
    }
    
    stopDetection() {
        this.isActive = false;
    }
    
    detectFromAudio(audioData) {
        // 音频情感检测预留接口
        return { type: 'neutral', confidence: 0.3 };
    }
    
    detectFromFace(imageData) {
        // 面部表情检测预留接口
        return { type: 'neutral', confidence: 0.3 };
    }
}

/**
 * 客服表情映射器
 */
class CustomerServiceExpressionMapper {
    constructor() {
        this.mappingRules = new Map();
        this.culturalAdaptations = new Map();
    }
    
    mapToServiceExpression(customerEmotion, context) {
        // 将客户情感映射为合适的客服表情
        const baseMapping = this.mappingRules.get(customerEmotion.type);
        const culturalAdjustment = this.culturalAdaptations.get(context.culture);
        
        return this.combineExpressions(baseMapping, culturalAdjustment);
    }
    
    combineExpressions(base, adjustment) {
        // 组合基础表情和文化调整
        return base; // 简化实现
    }
}

/**
 * 情感响应生成器
 */
class EmotionalResponseGenerator {
    generateResponse(emotion, context) {
        // 根据情感和上下文生成合适的响应
        return {
            verbal: this.generateVerbalResponse(emotion),
            nonVerbal: this.generateNonVerbalResponse(emotion),
            timing: this.calculateResponseTiming(emotion)
        };
    }
    
    generateVerbalResponse(emotion) {
        // 生成语言响应
        return "我理解您的感受";
    }
    
    generateNonVerbalResponse(emotion) {
        // 生成非语言响应
        return { gesture: 'nod', expression: 'empathetic' };
    }
    
    calculateResponseTiming(emotion) {
        // 计算响应时机
        return { delay: 200, duration: 1500 };
    }
}

/**
 * 情感上下文记忆
 */
class EmotionContextMemory {
    constructor() {
        this.shortTermMemory = [];
        this.longTermPatterns = new Map();
    }
    
    storeEmotion(emotion, context) {
        this.shortTermMemory.push({ emotion, context, timestamp: Date.now() });
        this.updateLongTermPatterns(emotion, context);
    }
    
    updateLongTermPatterns(emotion, context) {
        // 更新长期情感模式
        const pattern = `${emotion.type}_${context.phase}`;
        const count = this.longTermPatterns.get(pattern) || 0;
        this.longTermPatterns.set(pattern, count + 1);
    }
    
    getEmotionContext(emotion) {
        // 获取情感上下文
        return {
            recentPatterns: this.getRecentPatterns(),
            similarSituations: this.findSimilarSituations(emotion)
        };
    }
    
    getRecentPatterns() {
        return this.shortTermMemory.slice(-5);
    }
    
    findSimilarSituations(emotion) {
        return this.shortTermMemory.filter(record => 
            record.emotion.type === emotion.type);
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnhancedEmotionSystem;
} else {
    window.EnhancedEmotionSystem = EnhancedEmotionSystem;
}