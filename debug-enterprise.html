<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字人系统调试版</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .debug-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            width: 300px;
            background: white;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .debug-panel h3 {
            margin-top: 0;
            color: #333;
        }
        
        .log-entry {
            padding: 5px;
            margin: 2px 0;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .log-info { background: #e7f3ff; color: #0066cc; }
        .log-success { background: #e7f7e7; color: #009900; }
        .log-error { background: #ffe7e7; color: #cc0000; }
        .log-warning { background: #fff3cd; color: #856404; }
        
        #digital-human-container {
            width: 600px;
            height: 400px;
            border: 2px solid #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 20px 0;
        }
        
        .loading-info {
            text-align: center;
            padding: 20px;
            background: rgba(255,255,255,0.9);
            margin: 20px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>数字人系统调试版</h1>
    
    <div class="debug-panel">
        <h3>调试信息</h3>
        <div id="debug-log"></div>
        <button onclick="clearLog()">清空日志</button>
    </div>
    
    <div class="loading-info">
        <h3>加载状态</h3>
        <div id="loading-status">准备中...</div>
        <div id="loading-progress">0%</div>
    </div>
    
    <div id="digital-human-container"></div>
    
    <div>
        <button onclick="testSystem()">测试系统</button>
        <button onclick="reloadModel()">重新加载模型</button>
    </div>

    <!-- Three.js 库 -->
    <script src="./libs/three.min.js"></script>
    <script src="./libs/GLTFLoader.js"></script>
    <script src="./libs/tween.min.js"></script>
    
    <!-- 系统组件 -->
    <script src="./AdvancedFacialExpressionSystem.js"></script>
    <script src="./VoiceAdapter-simple.js"></script>
    <script src="./EnterpriseDigitalHuman.js"></script>
    
    <script>
        let digitalHuman = null;
        
        // 调试日志函数
        function addLog(message, type = 'info') {
            const logDiv = document.getElementById('debug-log');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logDiv.appendChild(entry);
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function clearLog() {
            document.getElementById('debug-log').innerHTML = '';
        }
        
        function updateLoadingStatus(status) {
            document.getElementById('loading-status').textContent = status;
        }
        
        function updateLoadingProgress(progress) {
            document.getElementById('loading-progress').textContent = Math.round(progress) + '%';
        }
        
        // 测试系统函数
        function testSystem() {
            addLog('开始系统测试', 'info');
            
            // 检查 Three.js
            if (typeof THREE === 'undefined') {
                addLog('❌ THREE.js 未加载', 'error');
                return;
            } else {
                addLog('✅ THREE.js 已加载', 'success');
            }
            
            // 检查 GLTFLoader
            if (typeof THREE.GLTFLoader === 'undefined') {
                addLog('❌ GLTFLoader 未加载', 'error');
                return;
            } else {
                addLog('✅ GLTFLoader 已加载', 'success');
            }
            
            // 检查其他组件
            const components = [
                'AdvancedFacialExpressionSystem',
                'VoiceAdapter', 
                'EnterpriseDigitalHuman'
            ];
            
            components.forEach(comp => {
                if (typeof window[comp] === 'undefined') {
                    addLog(`❌ ${comp} 未加载`, 'error');
                } else {
                    addLog(`✅ ${comp} 已加载`, 'success');
                }
            });
            
            addLog('系统测试完成', 'info');
        }
        
        function initDigitalHuman() {
            try {
                addLog('开始初始化数字人系统', 'info');
                updateLoadingStatus('初始化中...');
                
                const container = document.getElementById('digital-human-container');
                
                digitalHuman = new EnterpriseDigitalHuman(container, {
                    width: container.clientWidth,
                    height: container.clientHeight,
                    modelPath: './models/RiggedSimple.glb',
                    onProgress: (progress) => {
                        addLog(`加载进度: ${Math.round(progress)}%`, 'info');
                        updateLoadingProgress(progress);
                    },
                    onLoaded: () => {
                        addLog('✅ 数字人加载完成', 'success');
                        updateLoadingStatus('加载完成');
                    },
                    onError: (error) => {
                        addLog(`❌ 加载失败: ${error}`, 'error');
                        updateLoadingStatus('加载失败: ' + error);
                    }
                });
                
                addLog('数字人实例创建成功', 'success');
                
            } catch (error) {
                addLog(`❌ 初始化失败: ${error.message}`, 'error');
                updateLoadingStatus('初始化失败');
            }
        }
        
        function reloadModel() {
            if (digitalHuman) {
                addLog('重新加载模型', 'info');
                digitalHuman.loadModel('./models/RiggedSimple.glb');
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            addLog('页面加载完成', 'info');
            
            // 延迟一秒确保所有脚本加载完成
            setTimeout(() => {
                testSystem();
                initDigitalHuman();
            }, 1000);
        });
        
        // 错误捕获
        window.addEventListener('error', (event) => {
            addLog(`全局错误: ${event.error.message}`, 'error');
        });
        
        // 未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            addLog(`Promise错误: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>