/**
 * 逼真外观增强器
 * 提升3D数字人的视觉真实感和专业形象
 */

class RealisticAppearanceEnhancer {
    constructor(digitalHuman) {
        this.digitalHuman = digitalHuman;
        this.model = null;
        this.materials = new Map();
        this.lighting = new Map();
        this.postProcessing = null;
        
        // PBR增强系统
        this.pbrSystem = null;
        
        // 企业客服形象配置
        this.appearanceConfig = {
            skinTone: 'natural',
            businessAttire: true,
            professionalMakeup: true,
            hairStyle: 'professional',
            accessories: ['subtle_earrings'],
            posture: 'confident_professional'
        };
        
        // 材质增强配置
        this.materialConfig = {
            skin: {
                subsurfaceScattering: true,
                normalMaps: true,
                specularMaps: true,
                roughnessVariation: true
            },
            hair: {
                anisotropy: true,
                transparency: true,
                volumetrics: false // 性能考虑
            },
            clothing: {
                fabricTextures: true,
                wrinkleDetails: true,
                businessAppropriate: true
            },
            eyes: {
                wetness: true,
                reflection: true,
                pupilDilation: true
            }
        };
        
        this.init();
    }
    
    async init() {
        console.log('🎨 初始化逼真外观增强器...');
        
        // 初始化PBR增强系统
        await this.initializePBRSystem();
        
        this.setupMaterialEnhancements();
        this.configureLighting();
        this.setupBusinessAppearance();
    }
    
    /**
     * 初始化PBR增强系统
     */
    async initializePBRSystem() {
        try {
            if (typeof PBREnhancementSystem !== 'undefined') {
                console.log('🌟 初始化PBR增强系统...');
                
                this.pbrSystem = new PBREnhancementSystem(this.digitalHuman, {
                    qualityLevel: 'high',
                    enableIBL: true,
                    enableShadows: true,
                    enablePostProcessing: true,
                    enableAdaptiveQuality: true,
                    debug: false
                });
                
                // 将PBR系统设置到数字人中
                if (this.digitalHuman.setPBRSystem) {
                    this.digitalHuman.setPBRSystem(this.pbrSystem);
                }
                
                console.log('✅ PBR增强系统初始化完成');
            } else {
                console.warn('⚠️ PBREnhancementSystem未加载，使用传统材质系统');
            }
        } catch (error) {
            console.error('❌ PBR系统初始化失败:', error);
            console.warn('⚠️ 回退到传统材质系统');
        }
    }
    
    /**
     * 当模型加载完成后应用增强效果
     */
    async onModelLoaded(model) {
        this.model = model;
        
        // 如果有PBR系统，优先使用PBR增强
        if (this.pbrSystem) {
            console.log('🌟 使用PBR系统增强模型...');
            this.pbrSystem.enhanceModelMaterials(model);
        }
        
        // 应用传统增强效果（作为补充）
        this.applyRealisticEnhancements();
        
        console.log('✨ 逼真外观增强完成');
    }
    
    /**
     * 应用逼真增强效果
     */
    applyRealisticEnhancements() {
        if (!this.model) return;
        
        // 如果没有PBR系统，使用传统材质增强
        if (!this.pbrSystem) {
            this.enhanceSkinMaterials();
            this.enhanceHairMaterials();
            this.enhanceClothingMaterials();
            this.enhanceEyeMaterials();
        } else {
            console.log('🌟 PBR系统已处理材质，跳过传统材质增强');
        }
        
        // 应用商务造型（无论是否有PBR系统都需要）
        this.applyBusinessStyling();
        this.optimizeForPerformance();
    }
    
    /**
     * 增强皮肤材质
     */
    enhanceSkinMaterials() {
        this.model.traverse((child) => {
            if (child.isMesh && this.isSkinMaterial(child.material)) {
                const material = child.material;
                
                // 创建增强的皮肤材质
                const enhancedMaterial = new THREE.MeshStandardMaterial({
                    map: material.map,
                    normalMap: this.createSkinNormalMap(),
                    roughnessMap: this.createSkinRoughnessMap(),
                    metalnessMap: this.createSkinMetalnessMap(),
                    
                    // 皮肤特性
                    roughness: 0.6,
                    metalness: 0.0,
                    
                    // 次表面散射模拟
                    transparent: true,
                    opacity: 0.98,
                    
                    // 专业客服妆容
                    color: this.getProfessionalSkinTone()
                });
                
                // 添加细节纹理
                this.addSkinDetails(enhancedMaterial);
                
                child.material = enhancedMaterial;
                this.materials.set('skin', enhancedMaterial);
            }
        });
    }
    
    /**
     * 增强头发材质
     */
    enhanceHairMaterials() {
        this.model.traverse((child) => {
            if (child.isMesh && this.isHairMaterial(child.material)) {
                const material = child.material;
                
                const enhancedMaterial = new THREE.MeshStandardMaterial({
                    map: material.map,
                    alphaMap: this.createHairAlphaMap(),
                    normalMap: this.createHairNormalMap(),
                    
                    // 专业发型特性
                    color: this.getProfessionalHairColor(),
                    roughness: 0.8,
                    metalness: 0.1,
                    
                    // 透明度处理
                    transparent: true,
                    alphaTest: 0.5,
                    side: THREE.DoubleSide
                });
                
                this.addHairSheen(enhancedMaterial);
                
                child.material = enhancedMaterial;
                this.materials.set('hair', enhancedMaterial);
            }
        });
    }
    
    /**
     * 增强服装材质
     */
    enhanceClothingMaterials() {
        this.model.traverse((child) => {
            if (child.isMesh && this.isClothingMaterial(child.material)) {
                const material = child.material;
                const clothingType = this.identifyClothingType(child);
                
                const enhancedMaterial = this.createBusinessClothingMaterial(clothingType);
                
                child.material = enhancedMaterial;
                this.materials.set(`clothing_${clothingType}`, enhancedMaterial);
            }
        });
    }
    
    /**
     * 增强眼部材质
     */
    enhanceEyeMaterials() {
        this.model.traverse((child) => {
            if (child.isMesh && this.isEyeMaterial(child.material)) {
                const material = child.material;
                
                const enhancedMaterial = new THREE.MeshStandardMaterial({
                    map: material.map,
                    normalMap: this.createEyeNormalMap(),
                    envMap: this.createEyeReflectionMap(),
                    
                    // 眼部特性
                    roughness: 0.1,
                    metalness: 0.0,
                    transparent: true,
                    opacity: 0.95,
                    
                    // 专业眼妆
                    color: this.getProfessionalEyeColor()
                });
                
                this.addEyeWetness(enhancedMaterial);
                
                child.material = enhancedMaterial;
                this.materials.set('eyes', enhancedMaterial);
            }
        });
    }
    
    /**
     * 创建商务服装材质
     */
    createBusinessClothingMaterial(clothingType) {
        const businessMaterials = {
            blazer: {
                color: new THREE.Color(0x2c3e50), // 深蓝色西装
                roughness: 0.7,
                metalness: 0.1,
                normalScale: 0.5
            },
            shirt: {
                color: new THREE.Color(0xffffff), // 白色衬衫
                roughness: 0.3,
                metalness: 0.0,
                normalScale: 0.3
            },
            skirt: {
                color: new THREE.Color(0x34495e), // 深灰色裙子
                roughness: 0.6,
                metalness: 0.05,
                normalScale: 0.4
            }
        };
        
        const config = businessMaterials[clothingType] || businessMaterials.shirt;
        
        return new THREE.MeshStandardMaterial({
            color: config.color,
            roughness: config.roughness,
            metalness: config.metalness,
            normalMap: this.createFabricNormalMap(clothingType),
            roughnessMap: this.createFabricRoughnessMap(clothingType),
            normalScale: new THREE.Vector2(config.normalScale, config.normalScale)
        });
    }
    
    /**
     * 应用商务造型
     */
    applyBusinessStyling() {
        // 调整姿态为专业坐姿或站姿
        this.adjustProfessionalPosture();
        
        // 应用专业妆容
        this.applyProfessionalMakeup();
        
        // 添加适度的配饰
        this.addProfessionalAccessories();
        
        // 设置专业表情
        this.setDefaultProfessionalExpression();
    }
    
    /**
     * 调整专业姿态
     */
    adjustProfessionalPosture() {
        if (!this.model) return;
        
        // 查找骨骼系统
        this.model.traverse((child) => {
            if (child.isSkinnedMesh && child.skeleton) {
                const skeleton = child.skeleton;
                
                // 调整脊椎姿态 - 挺直
                this.adjustSpinePosture(skeleton);
                
                // 调整肩膀位置 - 自然下垂
                this.adjustShoulderPosture(skeleton);
                
                // 调整手部位置 - 自然放置
                this.adjustHandPosture(skeleton);
                
                // 调整头部角度 - 微微前倾，显示专注
                this.adjustHeadPosture(skeleton);
            }
        });
    }
    
    /**
     * 应用专业妆容
     */
    applyProfessionalMakeup() {
        // 通过材质调整实现专业妆容效果
        const skinMaterial = this.materials.get('skin');
        if (skinMaterial) {
            // 自然底妆色调
            skinMaterial.color.setHex(0xfdbcb4);
            
            // 添加轻微的高光效果
            skinMaterial.roughness = 0.5;
            skinMaterial.metalness = 0.02;
        }
        
        // 眼部妆容
        const eyeMaterial = this.materials.get('eyes');
        if (eyeMaterial) {
            // 自然眼影效果
            eyeMaterial.color.setHex(0x8b6f47);
        }
    }
    
    /**
     * 添加专业配饰
     */
    addProfessionalAccessories() {
        // 这里可以添加简约的耳环、手表等专业配饰
        // 由于性能考虑，使用简单几何体
        
        if (this.appearanceConfig.accessories.includes('subtle_earrings')) {
            this.addSubtleEarrings();
        }
    }
    
    /**
     * 添加精致耳环
     */
    addSubtleEarrings() {
        const earringGeometry = new THREE.SphereGeometry(0.3, 8, 8);
        const earringMaterial = new THREE.MeshStandardMaterial({
            color: 0xc0c0c0, // 银色
            metalness: 0.9,
            roughness: 0.1
        });
        
        // 左耳环
        const leftEarring = new THREE.Mesh(earringGeometry, earringMaterial);
        // 右耳环
        const rightEarring = new THREE.Mesh(earringGeometry, earringMaterial);
        
        // 需要根据模型的具体结构来定位耳环位置
        // 这里提供一个通用的方法
        this.positionAccessories(leftEarring, rightEarring);
    }
    
    /**
     * 定位配饰
     */
    positionAccessories(leftEarring, rightEarring) {
        // 查找头部骨骼来定位耳环
        this.model.traverse((child) => {
            if (child.isSkinnedMesh && child.skeleton) {
                const bones = child.skeleton.bones;
                const headBone = bones.find(bone => 
                    bone.name.toLowerCase().includes('head'));
                
                if (headBone) {
                    // 相对于头部骨骼定位耳环
                    leftEarring.position.set(-1.2, 0.5, 0);
                    rightEarring.position.set(1.2, 0.5, 0);
                    
                    headBone.add(leftEarring);
                    headBone.add(rightEarring);
                }
            }
        });
    }
    
    /**
     * 设置默认专业表情
     */
    setDefaultProfessionalExpression() {
        if (this.digitalHuman && this.digitalHuman.setEmotion) {
            // 设置为专业微笑
            this.digitalHuman.setEmotion('professional_smile', 0.6, {
                duration: 2000,
                context: 'default_professional'
            });
        }
    }
    
    /**
     * 创建皮肤法线贴图
     */
    createSkinNormalMap() {
        // 创建一个简单的皮肤纹理法线贴图
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');
        
        // 生成皮肤纹理噪声
        const imageData = ctx.createImageData(512, 512);
        for (let i = 0; i < imageData.data.length; i += 4) {
            // 生成细微的皮肤纹理
            const noise = (Math.random() - 0.5) * 20;
            imageData.data[i] = 128 + noise;     // R
            imageData.data[i + 1] = 128 + noise; // G
            imageData.data[i + 2] = 255;         // B (Z向量)
            imageData.data[i + 3] = 255;         // A
        }
        
        ctx.putImageData(imageData, 0, 0);
        
        const texture = new THREE.CanvasTexture(canvas);
        texture.wrapS = THREE.RepeatWrapping;
        texture.wrapT = THREE.RepeatWrapping;
        
        return texture;
    }
    
    /**
     * 创建织物法线贴图
     */
    createFabricNormalMap(fabricType) {
        const canvas = document.createElement('canvas');
        canvas.width = 256;
        canvas.height = 256;
        const ctx = canvas.getContext('2d');
        
        // 根据织物类型生成不同的纹理
        const patterns = {
            blazer: () => this.generateSuitFabricPattern(ctx),
            shirt: () => this.generateShirtFabricPattern(ctx),
            skirt: () => this.generateSkirtFabricPattern(ctx)
        };
        
        const pattern = patterns[fabricType] || patterns.shirt;
        pattern();
        
        return new THREE.CanvasTexture(canvas);
    }
    
    /**
     * 生成西装面料图案
     */
    generateSuitFabricPattern(ctx) {
        ctx.fillStyle = '#808080';
        ctx.fillRect(0, 0, 256, 256);
        
        // 添加细微的纹理线条
        ctx.strokeStyle = '#787878';
        ctx.lineWidth = 1;
        
        for (let i = 0; i < 256; i += 4) {
            ctx.beginPath();
            ctx.moveTo(i, 0);
            ctx.lineTo(i, 256);
            ctx.stroke();
        }
    }
    
    /**
     * 材质识别方法
     */
    isSkinMaterial(material) {
        if (!material || !material.name) return false;
        const skinKeywords = ['skin', 'face', 'head', 'body', '皮肤', '脸部'];
        return skinKeywords.some(keyword => 
            material.name.toLowerCase().includes(keyword));
    }
    
    isHairMaterial(material) {
        if (!material || !material.name) return false;
        const hairKeywords = ['hair', 'scalp', '头发', '毛发'];
        return hairKeywords.some(keyword => 
            material.name.toLowerCase().includes(keyword));
    }
    
    isClothingMaterial(material) {
        if (!material || !material.name) return false;
        const clothingKeywords = ['clothing', 'shirt', 'dress', 'suit', 'fabric', '衣服', '服装'];
        return clothingKeywords.some(keyword => 
            material.name.toLowerCase().includes(keyword));
    }
    
    isEyeMaterial(material) {
        if (!material || !material.name) return false;
        const eyeKeywords = ['eye', 'pupil', 'iris', '眼睛', '瞳孔'];
        return eyeKeywords.some(keyword => 
            material.name.toLowerCase().includes(keyword));
    }
    
    /**
     * 识别服装类型
     */
    identifyClothingType(mesh) {
        const name = mesh.name ? mesh.name.toLowerCase() : '';
        
        if (name.includes('blazer') || name.includes('jacket')) return 'blazer';
        if (name.includes('shirt') || name.includes('blouse')) return 'shirt';
        if (name.includes('skirt') || name.includes('dress')) return 'skirt';
        if (name.includes('pants') || name.includes('trousers')) return 'pants';
        
        return 'shirt'; // 默认
    }
    
    /**
     * 获取专业肤色
     */
    getProfessionalSkinTone() {
        // 返回自然健康的肤色
        return new THREE.Color(0xfdbcb4);
    }
    
    /**
     * 获取专业发色
     */
    getProfessionalHairColor() {
        // 返回专业的深棕色
        return new THREE.Color(0x4a3728);
    }
    
    /**
     * 获取专业眼色
     */
    getProfessionalEyeColor() {
        // 返回深棕色眼睛
        return new THREE.Color(0x3e2723);
    }
    
    /**
     * 性能优化
     */
    optimizeForPerformance() {
        // 减少不必要的材质计算
        this.materials.forEach((material) => {
            // 禁用不必要的特性以提升性能
            material.envMapIntensity = 0.3; // 降低环境反射强度
        });
        
        // 优化纹理分辨率
        this.optimizeTextures();
        
        console.log('🚀 外观材质性能优化完成');
    }
    
    /**
     * 优化纹理
     */
    optimizeTextures() {
        this.materials.forEach((material) => {
            if (material.map) {
                material.map.generateMipmaps = true;
                material.map.minFilter = THREE.LinearMipmapLinearFilter;
            }
            if (material.normalMap) {
                material.normalMap.generateMipmaps = true;
            }
        });
    }
    
    /**
     * 实时更新专业形象
     */
    updateProfessionalAppearance(config) {
        this.appearanceConfig = { ...this.appearanceConfig, ...config };
        
        // 重新应用外观设置
        if (this.model) {
            this.applyRealisticEnhancements();
        }
    }
    
    /**
     * 骨骼姿态调整方法
     */
    adjustSpinePosture(skeleton) {
        const spineBones = skeleton.bones.filter(bone => 
            bone.name.toLowerCase().includes('spine'));
        
        spineBones.forEach((bone, index) => {
            // 轻微向前倾斜，显示专注状态
            bone.rotation.x = -0.05 * (index + 1);
        });
    }
    
    adjustShoulderPosture(skeleton) {
        const shoulderBones = skeleton.bones.filter(bone => 
            bone.name.toLowerCase().includes('shoulder'));
        
        shoulderBones.forEach(bone => {
            // 自然下垂，避免紧张感
            bone.rotation.y = 0;
            bone.rotation.z = 0.1;
        });
    }
    
    adjustHandPosture(skeleton) {
        const handBones = skeleton.bones.filter(bone => 
            bone.name.toLowerCase().includes('hand'));
        
        handBones.forEach(bone => {
            // 自然放置，显示专业形象
            bone.rotation.x = 0.2;
            bone.rotation.y = 0.1;
        });
    }
    
    adjustHeadPosture(skeleton) {
        const headBone = skeleton.bones.find(bone => 
            bone.name.toLowerCase().includes('head'));
        
        if (headBone) {
            // 微微前倾，显示关注客户
            headBone.rotation.x = -0.1;
        }
    }
    
    /**
     * 获取当前外观状态
     */
    getAppearanceStatus() {
        return {
            config: this.appearanceConfig,
            materialsLoaded: this.materials.size,
            enhancementsApplied: this.model !== null,
            performanceOptimized: true
        };
    }
    
    /**
     * 销毁增强器
     */
    destroy() {
        // 销毁PBR系统
        if (this.pbrSystem) {
            this.pbrSystem.destroy();
            this.pbrSystem = null;
        }
        
        this.materials.clear();
        this.lighting.clear();
        this.model = null;
        console.log('🔄 外观增强器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = RealisticAppearanceEnhancer;
} else {
    window.RealisticAppearanceEnhancer = RealisticAppearanceEnhancer;
}