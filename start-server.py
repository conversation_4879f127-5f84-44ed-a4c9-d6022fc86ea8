#!/usr/bin/env python3
"""
企业数字人本地HTTP服务器启动器
解决CORS问题，确保模型文件正常加载
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import time
import threading
from urllib.parse import urlparse

class DigitalHumanHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器，添加CORS头部"""
    
    def end_headers(self):
        # 添加CORS头部，允许跨域请求
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        
        # 添加缓存控制，确保模型文件正确加载
        if self.path.endswith('.glb') or self.path.endswith('.gltf'):
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Content-Type', 'model/gltf-binary')
        elif self.path.endswith('.js'):
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Content-Type', 'application/javascript')
        elif self.path.endswith('.html'):
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Content-Type', 'text/html; charset=utf-8')
        
        super().end_headers()
    
    def do_OPTIONS(self):
        """处理OPTIONS预检请求"""
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
        message = format % args
        
        # 高亮重要的请求
        if '.glb' in message or '.gltf' in message:
            print(f"🎨 [{timestamp}] 模型文件: {message}")
        elif '.js' in message:
            print(f"📜 [{timestamp}] JS文件: {message}")
        elif '.html' in message:
            print(f"🌐 [{timestamp}] HTML页面: {message}")
        else:
            print(f"📁 [{timestamp}] {message}")

def find_available_port(start_port=8000, max_attempts=50):
    """查找可用的端口"""
    import socket
    
    for port in range(start_port, start_port + max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                return port
        except OSError:
            continue
    
    raise RuntimeError(f"无法找到可用端口 (尝试范围: {start_port}-{start_port + max_attempts})")

def check_model_files():
    """检查模型文件是否存在"""
    model_files = [
        'models/business_female_professional.glb',
        'models/business_female.glb',
        'models/business_male.glb'
    ]
    
    missing_files = []
    existing_files = []
    
    for model_file in model_files:
        if os.path.exists(model_file):
            size = os.path.getsize(model_file)
            existing_files.append((model_file, size))
        else:
            missing_files.append(model_file)
    
    print("📋 模型文件检查:")
    for file_path, size in existing_files:
        size_mb = size / (1024 * 1024)
        print(f"  ✅ {file_path} ({size_mb:.1f} MB)")
    
    for file_path in missing_files:
        print(f"  ❌ {file_path} (文件不存在)")
    
    return len(missing_files) == 0

def check_dependencies():
    """检查必要的文件是否存在"""
    required_files = [
        'index-complete-customer-service.html',
        'libs/three.min.js',
        'libs/GLTFLoader.js',
        'EnterpriseDigitalHuman.js',
        'PBREnhancementSystem.js',
        'FemaleCustomerServiceCharacter.js'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✅ 所有必要文件已存在")
    return True

def open_browser_delayed(url, delay=2):
    """延迟打开浏览器"""
    def open_browser():
        time.sleep(delay)
        print(f"\n🌐 正在打开浏览器: {url}")
        try:
            webbrowser.open(url)
        except Exception as e:
            print(f"⚠️ 无法自动打开浏览器: {e}")
            print(f"请手动访问: {url}")
    
    thread = threading.Thread(target=open_browser)
    thread.daemon = True
    thread.start()

def main():
    print("🏢 企业数字人本地服务器启动器")
    print("=" * 50)
    
    # 切换到脚本所在目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 检查依赖文件
    if not check_dependencies():
        print("\n❌ 请确保所有必要文件存在后重试")
        input("按回车键退出...")
        sys.exit(1)
    
    # 检查模型文件
    if not check_model_files():
        print("\n⚠️ 部分模型文件缺失，但系统仍可运行")
    
    # 查找可用端口
    try:
        port = find_available_port()
        print(f"🚀 找到可用端口: {port}")
    except RuntimeError as e:
        print(f"❌ {e}")
        input("按回车键退出...")
        sys.exit(1)
    
    # 启动HTTP服务器
    try:
        with socketserver.TCPServer(("", port), DigitalHumanHTTPRequestHandler) as httpd:
            server_url = f"http://localhost:{port}"
            page_url = f"{server_url}/index-complete-customer-service.html"
            
            print(f"\n🎉 企业数字人服务器已启动!")
            print(f"📍 服务器地址: {server_url}")
            print(f"🌐 系统页面: {page_url}")
            print(f"📊 状态监控: {server_url}/")
            print("\n" + "=" * 50)
            print("📋 可用功能:")
            print("  • 企业级PBR渲染")
            print("  • 女性客服角色系统")
            print("  • 智能性能优化")
            print("  • 实时表情和手势")
            print("  • 多语言支持")
            print("\n💡 提示:")
            print("  - 按 Ctrl+C 停止服务器")
            print("  - 确保防火墙允许本地连接")
            print("  - 推荐使用Chrome或Edge浏览器")
            print("=" * 50)
            
            # 延迟打开浏览器
            open_browser_delayed(page_url)
            
            print(f"\n🔄 服务器运行中，等待连接...")
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务器...")
        print("✅ 服务器已停止")
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        input("按回车键退出...")
        sys.exit(1)

if __name__ == "__main__":
    main()