# 🚀 3D数字人项目服务器启动指南

## ⚠️ 重要提示

**由于浏览器的安全限制，直接双击HTML文件打开会导致CORS错误，无法加载3D模型文件。**
**必须通过HTTP服务器运行项目才能正常工作。**

## 🎯 快速启动

### 方法一：使用批处理脚本（推荐）
```bash
# 双击运行
start-server.bat
```

### 方法二：使用PowerShell脚本
```powershell
# 右键 -> 使用PowerShell运行
.\start-server.ps1
```

### 方法三：手动启动Python服务器
```bash
# 在项目目录下运行
python -m http.server 8000
```

### 方法四：使用Node.js
```bash
# 如果已安装Node.js
npx http-server -p 8000 --cors
```

## 📱 访问地址

服务器启动后，在浏览器中访问：

- **主应用**: http://localhost:8000/index-enterprise.html
- **测试页面**: http://localhost:8000/test-model-loading.html

## 🔧 故障排除

### 1. ProgressEvent 错误
- **原因**: 文件访问权限问题，通常是因为直接打开HTML文件
- **解决**: 必须使用HTTP服务器运行

### 2. Python命令不存在
- **解决**: 安装Python 3.x: https://python.org
- **确保**: 安装时勾选"Add to PATH"选项

### 3. 端口被占用
```bash
# 更换端口号
python -m http.server 8080
```

### 4. 防火墙阻止
- 允许Python通过Windows防火墙
- 或临时关闭防火墙进行测试

## 🛠️ 开发工具选择

### VS Code Live Server（推荐）
1. 安装 "Live Server" 扩展
2. 右键HTML文件 → "Open with Live Server"
3. 自动处理CORS问题

### WebStorm/PhpStorm
- 内置HTTP服务器，右键HTML文件运行

### Chrome开发者模式
```bash
# 禁用安全限制启动Chrome（仅用于开发）
chrome.exe --disable-web-security --user-data-dir="C:\temp\chrome_dev"
```

## 📋 测试检查清单

启动服务器后，建议按顺序进行以下测试：

1. ✅ 访问测试页面: http://localhost:8000/test-model-loading.html
2. ✅ 点击"测试文件访问" - 确认所有模型文件可以访问
3. ✅ 尝试加载不同的3D模型
4. ✅ 检查控制台是否有错误信息
5. ✅ 访问主应用测试完整功能

## 🎨 项目文件结构

```
数字人4/
├── index-enterprise.html          # 主应用
├── test-model-loading.html        # 模型加载测试页面
├── start-server.bat              # Windows批处理启动脚本
├── start-server.ps1              # PowerShell启动脚本
├── models/                       # 3D模型文件夹
│   ├── business_female.glb
│   ├── business_male.glb
│   ├── RiggedSimple.glb
│   └── RiggedFigure.glb
├── libs/                        # 第三方库
│   ├── three.min.js
│   ├── GLTFLoader.js
│   └── tween.min.js
└── *.js                        # 项目核心文件
```

## 🔗 相关链接

- [Python下载](https://python.org)
- [Node.js下载](https://nodejs.org)  
- [VS Code下载](https://code.visualstudio.com)
- [THREE.js文档](https://threejs.org/docs)

---

💡 **提示**: 确保所有模型文件都在 `models/` 目录下，并且文件名与代码中的路径匹配。