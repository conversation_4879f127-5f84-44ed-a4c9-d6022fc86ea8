@echo off
chcp 65001
echo.
echo ================================================
echo       企业级数字人模型快速下载工具
echo ================================================
echo.

cd /d "%~dp0"

:: 检查models目录
if not exist "models" (
    echo 📁 创建 models 目录...
    mkdir models
)

echo 🚀 开始下载企业级数字人模型...
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未找到Python，尝试使用curl下载...
    goto :use_curl
)

:: 使用Python下载器
echo 💡 检测到Python，使用智能下载器...
python 企业数字人模型下载器.py
goto :end

:use_curl
echo 🌐 使用curl下载基础模型...

:: 检查curl是否可用
curl --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ curl不可用，请手动下载模型
    echo.
    echo 📋 手动下载链接:
    echo 1. 女性商务模型: https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedFigure/glTF-Binary/RiggedFigure.glb
    echo 2. 男性商务模型: https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedSimple/glTF-Binary/RiggedSimple.glb
    echo.
    echo 💡 下载后重命名并放入 models/ 目录:
    echo    RiggedFigure.glb → business_female.glb
    echo    RiggedSimple.glb → business_male.glb
    goto :end
)

:: 下载女性商务模型
echo 📥 下载女性商务模型...
curl -L -o "models/business_female.glb" "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedFigure/glTF-Binary/RiggedFigure.glb"

if %errorlevel% equ 0 (
    echo ✅ 女性商务模型下载成功
) else (
    echo ❌ 女性商务模型下载失败
)

:: 下载男性商务模型
echo 📥 下载男性商务模型...
curl -L -o "models/business_male.glb" "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedSimple/glTF-Binary/RiggedSimple.glb"

if %errorlevel% equ 0 (
    echo ✅ 男性商务模型下载成功
) else (
    echo ❌ 男性商务模型下载失败
)

:: 下载兼容性模型 (备用)
echo 📥 下载兼容性备用模型...
curl -L -o "models/RiggedSimple.glb" "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedSimple/glTF-Binary/RiggedSimple.glb"

if %errorlevel% equ 0 (
    echo ✅ 备用模型下载成功
) else (
    echo ❌ 备用模型下载失败
)

:end
echo.
echo ================================================
echo                下载完成！
echo ================================================
echo.

:: 检查下载结果
echo 📊 检查下载结果...
echo.

if exist "models\business_female.glb" (
    for %%I in ("models\business_female.glb") do (
        if %%~zI GTR 1024 (
            echo ✅ business_female.glb - 大小: %%~zI 字节
        ) else (
            echo ❌ business_female.glb - 文件损坏或不完整
        )
    )
) else (
    echo ❌ business_female.glb - 文件不存在
)

if exist "models\business_male.glb" (
    for %%I in ("models\business_male.glb") do (
        if %%~zI GTR 1024 (
            echo ✅ business_male.glb - 大小: %%~zI 字节
        ) else (
            echo ❌ business_male.glb - 文件损坏或不完整
        )
    )
) else (
    echo ❌ business_male.glb - 文件不存在
)

if exist "models\RiggedSimple.glb" (
    for %%I in ("models\RiggedSimple.glb") do (
        if %%~zI GTR 1024 (
            echo ✅ RiggedSimple.glb - 大小: %%~zI 字节 (备用)
        ) else (
            echo ❌ RiggedSimple.glb - 文件损坏或不完整
        )
    )
) else (
    echo ❌ RiggedSimple.glb - 文件不存在
)

echo.
echo 🎯 接下来的步骤:
echo 1. 启动HTTP服务器: start-http-server.bat
echo 2. 打开企业客服系统: index-complete-customer-service-fixed.html
echo 3. 或运行诊断工具: test-model-loading-debug.html
echo.
echo 📚 更多模型资源请查看: 模型下载指南-2024增强版.md
echo.

:: 询问是否启动HTTP服务器
set /p choice="是否现在启动HTTP服务器？(y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 🚀 启动HTTP服务器...
    start start-http-server.bat
)

echo.
echo 👋 感谢使用企业级数字人模型下载工具！
pause