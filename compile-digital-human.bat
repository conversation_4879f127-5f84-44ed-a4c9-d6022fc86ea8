@echo off
chcp 65001 >nul
:: Compile Digital Human Server using MSYS2 GCC
:: 使用MSYS2环境编译数字人服务器

echo ======================================
echo      编译数字人后端服务器
echo ======================================
echo.

echo [1/3] 设置MSYS2环境...
:: 添加MSYS2路径到当前会话的PATH
set "PATH=C:\msys64\ucrt64\bin;C:\msys64\usr\bin;%PATH%"

echo 检查GCC版本...
gcc --version
echo.

echo [2/3] 检查源文件...
if exist "数字人\server.c" (
    echo ✓ 找到源文件: 数字人\server.c
) else (
    echo ✗ 未找到源文件: 数字人\server.c
    echo 请确保源文件存在
    pause
    exit /b 1
)
echo.

echo [3/3] 开始编译...
echo 编译命令: gcc -o digital_human 数字人\server.c -lws2_32
echo.

gcc -o digital_human "数字人\server.c" -lws2_32

if %ERRORLEVEL% == 0 (
    echo.
    echo ======================================
    echo         编译成功！
    echo ======================================
    echo.
    echo 生成的可执行文件: digital_human.exe
    echo.
    echo 启动服务器:
    echo   .\digital_human.exe --port 8080
    echo.
) else (
    echo.
    echo ======================================
    echo         编译失败！
    echo ======================================
    echo.
    echo 请检查：
    echo 1. 源代码是否有语法错误
    echo 2. 是否缺少必要的头文件
    echo 3. 是否需要额外的库文件
    echo.
)

pause