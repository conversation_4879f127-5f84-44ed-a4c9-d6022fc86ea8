# 3D数字人客服系统实施方案（OA系统深度集成版）

---

## 一、项目目标

在公司OA系统聊天窗口中集成**可自由移动、高精度、语音驱动的3D全身真人数字人客服**，实现以下核心能力：

- **全身3D真人模型**（LHM或Mixamo生成，支持表情与动作，必须为3D全身真人，含头、躯干、四肢、手指）
- **语音交互**（Fay ASR/TTS，MimicTalk驱动口型）
- **屏幕内自由移动**（键盘/鼠标控制，边界/碰撞检测）
- **场景适配**（虚拟办公室、会议室等）
- **轻量化兼容**（适配低配电脑，支持精简模式）

---

## 二、OA系统深度集成建议

### 1. 聊天窗口嵌入与层级适配

- **嵌入方式**：iframe自适应OA聊天窗口（建议400×600px，可拖拽）
- **层级控制**：数字人渲染层置于聊天内容上方，`pointer-events: none`，仅数字人本体可响应点击
- **窗口联动**：最小化/最大化时，数字人切换待机/唤醒动画

### 2. OA功能对接与通信协议

- **语音对接**：复用OA ASR/TTS接口，语音流通过`postMessage`同步至数字人
    - 语音输入：`{ type: "audioStart", stream: audioStream }`，驱动唇形
    - 文字输出：`{ type: "textSpeak", content: "..." }`，驱动说话动画
- **交互联动**：监听OA消息事件，用户输入时数字人自动转向输入框（Three.js相机调整）

---

## 三、3D数字人移动与交互实现建议

### 1. 屏幕内移动与边界/碰撞

- **活动区域**：OA窗口底部1/3为行走区，顶部2/3为交互区，确保3D数字人可在窗口内自由移动。
- **自动移动**：收到消息时，数字人平滑走到窗口中央（`Vector3.lerp()`，速度0.1m/s），提升交互感。
- **手动控制**：管理员可开启移动开关，用户点击任意位置，数字人射线检测目标点并行走，模拟真实走动。
- **碰撞检测**：对输入框、按钮等UI元素添加`Box3`碰撞体，防止数字人穿透UI，保证真实感。

#### 实现建议
- 使用Three.js加载全身FBX/GLB模型，模型需为**3D全身真人**（含头、躯干、四肢、手指），骨骼20-30根，三角面数≤5万，兼顾真实感与性能。
- 行走、待机、点头、挥手等动画建议用Blender或Mixamo制作，导出GLB动画集。
- 利用Three.js的`Raycaster`实现点击目标点检测，`Box3`实现与UI元素的碰撞检测。
- 自动/手动移动均通过`Vector3.lerp()`实现平滑过渡，动画与位置同步。

#### 示例代码（核心片段）
```javascript
// 加载全身3D真人模型
const loader = new THREE.GLTFLoader();
loader.load('fullbody_real_human.glb', gltf => {
  digitalHuman = gltf.scene;
  scene.add(digitalHuman);
});

// 键盘控制
let velocity = new THREE.Vector3();
window.addEventListener("keydown", e => {
  switch (e.key) {
    case "ArrowUp": velocity.z = -0.1; break;
    case "ArrowDown": velocity.z = 0.1; break;
    case "ArrowLeft": velocity.x = -0.1; break;
    case "ArrowRight": velocity.x = 0.1; break;
  }
});
// 渲染循环中
if (digitalHuman) {
  if (inBounds(digitalHuman.position.clone().add(velocity))) {
    digitalHuman.position.add(velocity);
  }
}
```
> **建议**：实现`inBounds`函数，判断新位置是否超出活动区或与UI碰撞。全身模型建议采用真人照片驱动的LHM或Mixamo模型，提升真实感。

### 2. 全身模型与动画

- **模型规范**：FBX/GLB格式，三角面数≤5万，骨骼20-30根，**必须为3D全身真人模型**，支持表情与动作驱动。
- **动画集**：待机（晃动/眨眼）、行走、点头、挥手、淡出等，动作流畅自然。
- **LOD策略**：低配设备自动切换低模（隐藏细节），保证兼容性和流畅度。

---

## 四、语音与表情动作联动建议

- **ASR返回文本时**：同步语速/停顿数据，数字人调整唇形帧率与点头
- **TTS播放时**：驱动说话动画（前倾、手势），语音结束切换等待动画

---

## 五、部署与运维建议

- **依赖隔离**：数字人SDK≤5MB，通过OA npm私有库引入，Three.js版本独立
- **权限控制**：复用OA JWT认证，仅登录用户可见
- **性能监控**：OA后台集成帧率、加载时长、内存占用监控，超限自动降级

---

## 六、最低硬件要求

- **CPU**：Intel i5及以上
- **内存**：8GB RAM
- **显卡**：集成显卡（如Intel UHD 630）或更高
- **浏览器**：Chrome 95+ 或 Edge 100+

---

## 七、OA集成测试清单

| 测试项 | 验证方法 |
|--------|----------|
| 数字人嵌入 | OA聊天窗口显示数字人 |
| 移动碰撞 | 行走时不穿过输入框/按钮 |
| 语音唇形同步 | 语音输入/输出时唇形动画同步 |
| 动作联动 | 发送消息数字人转向输入框 |
| 窗口缩放动画 | 最小化/最大化动画切换 |
| 权限控制 | 未登录用户不可见 |
| 性能监控 | 后台显示帧率/内存 |
| LOD切换 | 低配电脑自动降级 |
| 动画切换 | 聊天不同阶段动画正确 |
| 语音异常恢复 | 断网/中断后恢复待机 |

---

## 八、主流开源数字人项目推荐

- **OneShotOneTalk**：[https://xiangjun-xj.github.io/OneShotOneTalk/](https://xiangjun-xj.github.io/OneShotOneTalk/)
- **MimicTalk**：[https://github.com/yerfor/MimicTalk](https://github.com/yerfor/MimicTalk)
- **HeyGem**：[https://github.com/GuijiAI/HeyGem.ai](https://github.com/GuijiAI/HeyGem.ai)
- **DUIX**：[https://github.com/GuijiAI/DUIX](https://github.com/GuijiAI/DUIX)
- **Fay**：[https://github.com/fay-team/fay](https://github.com/fay-team/fay)
- **Hallo**：[https://github.com/Baidu/Hallo](https://github.com/Baidu/Hallo)
- **Sonic**：[https://github.com/jixiaozhong/Sonic](https://github.com/jixiaozhong/Sonic)
- **Goku**：[https://saiyan-world.github.io/goku/](https://saiyan-world.github.io/goku/)

---

## 九、实现建议与注意事项

1. **必须采用3D全身真人模型**，含头、躯干、四肢、手指，提升真实感和交互体验。
2. **优先保证OA集成体验**，所有交互、动画、性能均以OA主业务流畅为前提。
3. **移动与碰撞检测为核心难点**，建议先实现基础移动，再逐步完善碰撞与动画联动。
4. **表情与语音联动建议采用MimicTalk等现成方案，减少自研成本。**
5. **所有SDK和依赖需严格版本隔离，避免与OA主系统冲突。**
6. **如需代码示例（如postMessage通信、Three.js边界检测），可随时补充。**

---

如需进一步细化某部分代码或具体实现方案，请随时提出