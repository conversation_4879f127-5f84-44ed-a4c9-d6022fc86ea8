# 3D数字人WebSocket服务器 Makefile
# 支持Windows和Linux编译

# 编译器设置
CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g
LDFLAGS = 

# 目标文件
TARGET = digital_human_server
SOURCE = server.c

# 操作系统检测
UNAME_S := $(shell uname -s 2>/dev/null || echo "Windows")

# Windows设置
ifeq ($(OS),Windows_NT)
	TARGET := $(TARGET).exe
	LIBS = -lws2_32 -lwebsockets -ljson-c
	CFLAGS += -D_WIN32_WINNT=0x0600
else
	# Linux设置
	ifeq ($(UNAME_S),Linux)
		LIBS = -lwebsockets -ljson-c -lpthread
		CFLAGS += -D_GNU_SOURCE
	endif
	# macOS设置
	ifeq ($(UNAME_S),Darwin)
		LIBS = -lwebsockets -ljson-c -lpthread
		CFLAGS += -D_DARWIN_C_SOURCE
	endif
endif

# 默认目标
all: $(TARGET)

# 编译主程序
$(TARGET): $(SOURCE)
	@echo "编译数字人WebSocket服务器..."
	@echo "操作系统: $(UNAME_S)"
	@echo "编译选项: $(CFLAGS)"
	@echo "链接库: $(LIBS)"
	$(CC) $(CFLAGS) -o $(TARGET) $(SOURCE) $(LIBS) $(LDFLAGS)
	@echo "编译完成: $(TARGET)"

# 安装依赖（仅Linux）
install-deps:
	@echo "安装依赖库..."
ifeq ($(UNAME_S),Linux)
	sudo apt-get update
	sudo apt-get install -y libwebsockets-dev libjson-c-dev build-essential
	@echo "Linux依赖安装完成"
else
	@echo "Windows请使用vcpkg安装依赖："
	@echo "vcpkg install libwebsockets json-c"
endif

# 运行服务器
run: $(TARGET)
	@echo "启动数字人服务器（端口8080）..."
	./$(TARGET) 8080

# 运行服务器（自定义端口）
run-port: $(TARGET)
	@echo "启动数字人服务器（端口$(PORT)）..."
	./$(TARGET) $(PORT)

# 清理编译文件
clean:
	@echo "清理编译文件..."
	rm -f $(TARGET)
	rm -f *.o
	rm -f *.log
	@echo "清理完成"

# 调试编译
debug: CFLAGS += -DDEBUG -g3 -O0
debug: $(TARGET)
	@echo "调试版本编译完成"

# 发布编译
release: CFLAGS += -DNDEBUG -O3
release: $(TARGET)
	@echo "发布版本编译完成"

# 检查代码风格
check:
	@echo "检查代码风格..."
	cppcheck --enable=all --std=c99 $(SOURCE)

# 内存泄漏检查（需要valgrind）
memcheck: $(TARGET)
	@echo "内存泄漏检查..."
	valgrind --leak-check=full --show-leak-kinds=all ./$(TARGET) 8080

# 帮助信息
help:
	@echo "可用的make目标："
	@echo "  all          - 编译服务器（默认）"
	@echo "  install-deps - 安装依赖库"
	@echo "  run          - 运行服务器（端口8080）"
	@echo "  run-port     - 运行服务器（自定义端口，使用PORT=xxxx）"
	@echo "  clean        - 清理编译文件"
	@echo "  debug        - 编译调试版本"
	@echo "  release      - 编译发布版本"
	@echo "  check        - 代码风格检查"
	@echo "  memcheck     - 内存泄漏检查"
	@echo "  help         - 显示此帮助信息"
	@echo ""
	@echo "示例："
	@echo "  make                    # 编译"
	@echo "  make run                # 运行（端口8080）"
	@echo "  make run-port PORT=9090 # 运行（端口9090）"
	@echo "  make debug              # 调试编译"

# 声明伪目标
.PHONY: all install-deps run run-port clean debug release check memcheck help