# CLAUDE.md

本文件为Claude Code（claude.ai/code）在本仓库协作时的开发指引。

## 项目概述

本项目为数字人（3D全身真人数字人）系统，主要技术栈如下：
- 前端：HTML + CSS + JavaScript（Three.js，支持3D全身模型渲染与交互）
- 后端：C语言（负责业务逻辑与WebSocket通信）
- 运行环境：Web
- 通信协议：WebSocket

## 项目结构

当前代码库结构如下：

```
数字人4/
│   ├── claude-code-settings.json
│   ├── setup-chinese.bat
│   ├── setup-chinese.ps1
│   └── verify-chinese.bat
├── 公司提供资料.txt        # 公司需求与技术规范
└── 数字人                 # 主项目目录（3D数字人相关代码与资源）
```

## 关键信息与建议

### 当前状态
- 项目处于规划/环境搭建阶段
- 后端计划用C语言实现，需支持WebSocket
- 前端将采用原生HTML/CSS/JS，集成Three.js实现3D全身数字人
- 尚未有正式源代码，建议优先搭建基础骨架

### 中文与多语言支持
- 提供完整的Claude Code中文配置
- 推荐先运行`setup-chinese.bat`自动配置
- 可用`verify-chinese.bat`验证环境
- **多语言支持**：建议前端和后端均设计为可扩展多语言，支持中英文等多语种切换。前端UI、提示、语音识别与合成接口需支持多语言参数，后端消息协议中增加`lang`字段（如`{ type: "textSpeak", content: "...", lang: "zh-CN" }`），便于后续国际化扩展。

### 技术与实现要点
- **3D全身真人模型**：必须采用FBX/GLB格式，含头、躯干、四肢、手指，骨骼20-30根，三角面数≤5万，推荐LHM或Mixamo生成，提升真实感
- **前端交互**：Three.js负责模型加载、动画、碰撞检测与屏幕内自由移动（参考数字人实施方案文档）
- **语音驱动**：集成Fay ASR/TTS，MimicTalk驱动口型，前端通过WebSocket与后端通信
- **消息协议**：建议设计如下消息格式（JSON）：
  - 语音输入：`{ type: "audioStart", stream: ..., lang: "zh-CN" }`
  - 文字输出：`{ type: "textSpeak", content: "...", lang: "en-US" }`
  - 动作控制：`{ type: "moveTo", x: ..., y: ..., z: ... }`
- **安全与权限**：前端需校验用户登录状态，后端建议JWT鉴权
- **性能优化**：支持LOD自动降级，低配设备切换低模

### 开发建议
- 前端优先实现3D数字人加载与基础移动（键盘/鼠标/自动移动），并实现与UI的碰撞检测
- 后端先实现WebSocket服务端，支持基础消息收发与简单业务逻辑
- 逐步完善语音驱动、表情动画、动作联动等高级功能
- 所有依赖建议npm私有库引入，避免与OA主系统冲突
- **多语言建议**：所有用户可见文本、语音、提示均应支持多语言配置，建议采用国际化（i18n）方案，便于后续扩展。

### 测试与部署
- 建议制定集成测试清单（见数字人实施方案），覆盖嵌入、移动、语音、动画、权限、性能、多语言等关键场景
- 部署时注意依赖隔离与性能监控

## 下一步计划
 
1. 创建前端基础HTML结构，集成Three.js，加载3D全身真人模型
2. 实现C语言WebSocket后端，支持与前端的基础通信
3. 设计并实现前后端消息协议（JSON格式，兼容语音、动作、表情、多语言等指令）
4. 完善构建与部署流程，确保跨平台兼容
5. 持续补充和完善3D数字人相关功能与文档


---

如需具体代码示例（如Three.js模型加载、WebSocket通信、碰撞检测、多语言切换等），可参考`数字人`目录内实施方案或随时提问。