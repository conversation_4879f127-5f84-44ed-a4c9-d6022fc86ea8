@echo off
chcp 65001 >nul
:: Fix Digital Human Loading Issue
:: Multiple solutions for loading problems

echo ======================================
echo       Fix Digital Human Loading
echo ======================================
echo.

echo The digital human is stuck at 0% loading because:
echo 1. WebSocket connection to backend server failed
echo 2. Frontend is waiting for backend confirmation
echo 3. 3D model loading depends on server connection
echo.

echo Available Solutions:
echo.
echo [1] Install GCC and start full system (Best experience)
echo [2] Use offline mode (Limited functionality)
echo [3] Use mock backend (Development mode)
echo [4] Diagnose connection issues
echo.

set /p choice="Choose solution (1-4): "

if "%choice%"=="1" goto :install_full
if "%choice%"=="2" goto :offline_mode
if "%choice%"=="3" goto :mock_backend
if "%choice%"=="4" goto :diagnose
echo Invalid choice.
pause
exit /b 1

:install_full
echo.
echo Opening GCC installation guide...
call install-gcc.bat
goto :end

:offline_mode
echo.
echo Creating offline mode version...
echo Modifying frontend to skip WebSocket connection...

:: Create offline version
copy index-enterprise.html index-offline.html >nul

:: Modify the offline version to skip WebSocket
powershell -Command "(Get-Content 'index-offline.html') -replace 'this.initWebSocket\(\);', '// WebSocket disabled for offline mode' | Set-Content 'index-offline.html'"
powershell -Command "(Get-Content 'index-offline.html') -replace 'console.log\(.*WebSocket.*\);', 'console.log(\"Running in offline mode\"); this.showNotification(\"离线模式\", \"info\");' | Set-Content 'index-offline.html'"

echo.
echo [Info] Created offline version: index-offline.html
echo [Info] Starting offline mode...
start-frontend-only.bat 8000 index-offline.html
goto :end

:mock_backend
echo.
echo Creating mock backend server...

:: Create simple mock server
echo const WebSocket = require('ws'); > mock-server.js
echo const wss = new WebSocket.Server({ port: 8080 }); >> mock-server.js
echo console.log('Mock backend running on port 8080'); >> mock-server.js
echo wss.on('connection', function connection(ws) { >> mock-server.js
echo   console.log('Client connected'); >> mock-server.js
echo   ws.send(JSON.stringify({type: 'system_status', data: {status: 'ready'}})); >> mock-server.js
echo   ws.on('message', function incoming(message) { >> mock-server.js
echo     console.log('received:', message); >> mock-server.js
echo     ws.send(JSON.stringify({type: 'response', data: {message: 'Mock response'}})); >> mock-server.js
echo   }); >> mock-server.js
echo }); >> mock-server.js

where node >nul 2>&1
if errorlevel 1 (
    echo [Error] Node.js not found. Installing mock backend requires Node.js
    echo Please install Node.js from https://nodejs.org
    pause
    goto :end
)

where npm >nul 2>&1
if not errorlevel 1 (
    npm install ws >nul 2>&1
)

echo [Info] Starting mock backend server...
start "Mock Backend" node mock-server.js
timeout /t 2 /nobreak >nul

echo [Info] Starting frontend...
start-frontend-only.bat
goto :end

:diagnose
echo.
echo ======================================
echo         Connection Diagnosis
echo ======================================
echo.

echo Checking port 8080 availability...
netstat -an | findstr ":8080"
if errorlevel 1 (
    echo [Info] Port 8080 is available
) else (
    echo [Warning] Port 8080 is in use
)

echo.
echo Checking browser console for errors:
echo 1. Press F12 in browser to open Developer Tools
echo 2. Go to Console tab
echo 3. Look for WebSocket connection errors
echo 4. Check Network tab for failed requests
echo.

echo Common error messages:
echo - "WebSocket connection to 'ws://localhost:8080/' failed"
echo - "ERR_CONNECTION_REFUSED"
echo - "WebSocket is already in CLOSING or CLOSED state"
echo.

echo Solutions based on errors:
echo - Connection refused: Backend server not running
echo - Already in CLOSING: Restart browser
echo - Permission denied: Check firewall settings
echo.

:end
echo.
echo For immediate access, try: start-frontend-only.bat
echo For full experience, install GCC: install-gcc.bat
pause