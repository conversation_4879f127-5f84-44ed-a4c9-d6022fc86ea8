@echo off
chcp 65001 >nul
:: Fix GCC PATH Environment Variable
:: Add MSYS2 GCC to Windows PATH

echo ======================================
echo        Fix GCC PATH Environment
echo ======================================
echo.

echo Problem: GCC is installed but not found in Windows CMD
echo Solution: Add MSYS2 GCC path to system PATH
echo.

:: Common MSYS2 installation paths
set MSYS2_PATHS[0]=C:\msys64\mingw64\bin
set MSYS2_PATHS[1]=C:\msys64\ucrt64\bin
set MSYS2_PATHS[2]=C:\msys2\mingw64\bin
set MSYS2_PATHS[3]=D:\msys64\mingw64\bin

echo [Info] Checking common MSYS2 installation paths...

:: Check each possible path
for /L %%i in (0,1,3) do (
    call set "CURRENT_PATH=%%MSYS2_PATHS[%%i]%%"
    call :check_path "%%CURRENT_PATH%%"
)

echo.
echo [Info] Manual PATH setup instructions:
echo 1. Open System Properties (Win+R, type: sysdm.cpl)
echo 2. Click "Environment Variables"
echo 3. In System Variables, find and edit "Path"
echo 4. Add one of these paths:
echo    - C:\msys64\mingw64\bin
echo    - C:\msys64\ucrt64\bin
echo 5. Click OK and restart command prompt
echo.

echo [Alternative] Quick temporary fix:
echo Run this in your current cmd session:
echo   set PATH=%PATH%;C:\msys64\mingw64\bin
echo.

pause
goto :end

:check_path
set TEST_PATH=%~1
if exist "%TEST_PATH%\gcc.exe" (
    echo [Found] GCC at: %TEST_PATH%
    echo.
    echo [Fix] Adding to current session PATH...
    set PATH=%PATH%;%TEST_PATH%
    echo [Test] Testing GCC...
    gcc --version
    if errorlevel 0 (
        echo [Success] GCC is now working!
        echo.
        echo To make this permanent:
        echo 1. Add "%TEST_PATH%" to system PATH
        echo 2. Or run this batch file before compiling
        echo.
        echo [Auto] Attempting to compile digital human server...
        cd /d "F:\张剑虹\数字人4"
        gcc -Wall -Wextra -std=c99 -O2 -g -D_WIN32_WINNT=0x0600 -o digital_human_server.exe server.c -lws2_32 -lwebsockets -ljson-c
        if exist "digital_human_server.exe" (
            echo [Success] Digital human server compiled successfully!
            echo File size:
            dir digital_human_server.exe
        ) else (
            echo [Warning] Compilation completed but executable not found
            echo This may be due to missing libraries
        )
    )
) else (
    echo [Not Found] %TEST_PATH%
)
goto :eof

:end