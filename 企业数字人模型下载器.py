#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
企业级数字人模型下载器 - 2024版
自动下载适合企业客服的高质量3D模型
"""

import os
import requests
import json
from pathlib import Path
import hashlib
import time

class EnterpriseModelDownloader:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.models_dir = self.base_dir / "models"
        self.models_dir.mkdir(exist_ok=True)
        
        # 企业级模型配置 - 2024增强版
        self.model_sources = {
            # Khronos官方示例模型 - 更新的URL
            "business_female_base": {
                "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/main/2.0/RiggedFigure/glTF-Binary/RiggedFigure.glb",
                "filename": "business_female.glb",
                "description": "女性商务基础模型",
                "size_mb": 12.5
            },
            "business_male_base": {
                "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/main/2.0/RiggedSimple/glTF-Binary/RiggedSimple.glb",
                "filename": "business_male.glb",
                "description": "男性商务基础模型",
                "size_mb": 8.3
            },

            # 高质量备用模型 - 修正URL
            "enhanced_female": {
                "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/main/2.0/RiggedSimple/glTF-Binary/RiggedSimple.glb",
                "filename": "RiggedSimple.glb",
                "description": "通用兼容模型",
                "size_mb": 8.3
            },
            "enhanced_male": {
                "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/main/2.0/RiggedFigure/glTF-Binary/RiggedFigure.glb",
                "filename": "RiggedFigure.glb",
                "description": "高级形象模型",
                "size_mb": 12.5
            },

            # 动画测试模型 - 修正URL
            "animation_test": {
                "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/main/2.0/CesiumMan/glTF-Binary/CesiumMan.glb",
                "filename": "CesiumMan.glb",
                "description": "动画测试模型",
                "size_mb": 2.1
            },

            # 新增高质量模型
            "professional_avatar": {
                "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/main/2.0/BrainStem/glTF-Binary/BrainStem.glb",
                "filename": "professional_avatar.glb",
                "description": "专业头像模型",
                "size_mb": 5.2
            },

            # 表情动画模型
            "expression_model": {
                "url": "https://raw.githubusercontent.com/KhronosGroup/glTF-Sample-Models/main/2.0/Fox/glTF-Binary/Fox.glb",
                "filename": "expression_demo.glb",
                "description": "表情动画演示模型",
                "size_mb": 3.8
            }
        }
        
        # Ready Player Me示例模型（需要替换为实际链接）
        self.rpm_examples = [
            "https://models.readyplayer.me/example-business-female.glb",
            "https://models.readyplayer.me/example-business-male.glb"
        ]
    
    def download_file(self, url, filepath, description):
        """下载文件并显示进度"""
        try:
            print(f"\n🔄 开始下载: {description}")
            print(f"📁 目标文件: {filepath}")
            print(f"🌐 下载地址: {url}")
            
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0
            
            with open(filepath, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        if total_size > 0:
                            percent = (downloaded / total_size) * 100
                            print(f"\r📊 进度: {percent:.1f}% ({downloaded/1024/1024:.1f}MB/{total_size/1024/1024:.1f}MB)", end='')
            
            print(f"\n✅ 下载完成: {description}")
            return True
            
        except Exception as e:
            print(f"\n❌ 下载失败: {description}")
            print(f"🔍 错误信息: {str(e)}")
            return False
    
    def verify_model(self, filepath):
        """验证模型文件"""
        try:
            if not filepath.exists():
                return False, "文件不存在"
            
            file_size = filepath.stat().st_size
            if file_size < 1024:  # 小于1KB
                return False, f"文件过小: {file_size}字节"
            
            # 检查GLB文件头
            with open(filepath, 'rb') as f:
                header = f.read(4)
                if header != b'glTF':
                    return False, "不是有效的GLB文件"
            
            return True, f"验证通过, 大小: {file_size/1024/1024:.1f}MB"
            
        except Exception as e:
            return False, f"验证出错: {str(e)}"
    
    def download_all_models(self):
        """下载所有企业级模型"""
        print("🚀 企业级数字人模型下载器 - 2024版")
        print("=" * 50)
        
        success_count = 0
        total_count = len(self.model_sources)
        
        for model_id, config in self.model_sources.items():
            print(f"\n📦 [{success_count+1}/{total_count}] 处理模型: {model_id}")
            
            filepath = self.models_dir / config["filename"]
            
            # 检查文件是否已存在
            if filepath.exists():
                is_valid, msg = self.verify_model(filepath)
                if is_valid:
                    print(f"✅ 文件已存在且有效: {config['filename']} ({msg})")
                    success_count += 1
                    continue
                else:
                    print(f"⚠️ 文件存在但无效: {msg}, 重新下载...")
                    filepath.unlink()
            
            # 下载文件
            if self.download_file(config["url"], filepath, config["description"]):
                # 验证下载的文件
                is_valid, msg = self.verify_model(filepath)
                if is_valid:
                    print(f"✅ 模型验证通过: {msg}")
                    success_count += 1
                else:
                    print(f"❌ 模型验证失败: {msg}")
                    filepath.unlink(missing_ok=True)
            
            time.sleep(1)  # 避免请求过快
        
        print("\n" + "=" * 50)
        print(f"📊 下载统计: {success_count}/{total_count} 成功")
        
        if success_count == total_count:
            print("🎉 所有模型下载完成！")
            self.generate_model_info()
        elif success_count > 0:
            print("⚠️ 部分模型下载成功，请检查网络连接")
        else:
            print("❌ 所有模型下载失败，请检查网络连接")
        
        return success_count
    
    def generate_model_info(self):
        """生成模型信息文件"""
        info_file = self.models_dir / "models_info.json"
        
        models_info = {
            "download_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_models": 0,
            "models": {}
        }
        
        for model_file in self.models_dir.glob("*.glb"):
            if model_file.is_file():
                size = model_file.stat().st_size
                models_info["models"][model_file.name] = {
                    "size_bytes": size,
                    "size_mb": round(size / 1024 / 1024, 2),
                    "modified": time.strftime("%Y-%m-%d %H:%M:%S", 
                                            time.localtime(model_file.stat().st_mtime))
                }
                models_info["total_models"] += 1
        
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(models_info, f, ensure_ascii=False, indent=2)
        
        print(f"📋 模型信息已保存: {info_file}")
    
    def list_ready_player_me_guide(self):
        
        """显示Ready Player Me使用指南"""
        print("\n🎯 Ready Player Me 企业使用指南:")
        print("-" * 40)
        print("1. 访问 https://readyplayer.me/")
        print("2. 注册开发者账号（免费）")
        print("3. 创建商务风格数字人:")
        print("   - 选择专业发型")
        print("   - 选择商务服装")
        print("   - 调整肤色和年龄")
        print("4. 下载GLB格式文件")
        print("5. 重命名为 business_custom_*.glb")
        print("6. 放入 models/ 目录")
        print("\n💡 提示: RPM模型质量最高，强烈推荐！")
    
    def generate_download_report(self):
        """生成下载报告"""
        report_file = self.base_dir / "download_report.md"
        
        report_content = f"""# 企业数字人模型下载报告

**生成时间**: {time.strftime("%Y-%m-%d %H:%M:%S")}

## 📁 已下载模型

| 文件名 | 大小 | 状态 | 描述 |
|--------|------|------|------|
"""
        
        for model_file in self.models_dir.glob("*.glb"):
            size_mb = model_file.stat().st_size / 1024 / 1024
            is_valid, msg = self.verify_model(model_file)
            status = "✅ 正常" if is_valid else "❌ 异常"
            
            # 获取描述
            desc = "未知模型"
            for config in self.model_sources.values():
                if config["filename"] == model_file.name:
                    desc = config["description"]
                    break
            
            report_content += f"| {model_file.name} | {size_mb:.1f}MB | {status} | {desc} |\n"
        
        report_content += f"""
## 🎯 使用建议

1. **主力模型**: business_female.glb / business_male.glb
2. **备用方案**: RiggedSimple.glb (兼容性最好)  
3. **测试模型**: CesiumMan.glb (轻量级)

## 🔗 进一步优化

1. 使用 Ready Player Me 创建定制模型
2. 在 Blender 中优化材质和纹理
3. 添加企业LOGO或特殊标识
4. 调整动画和表情系统

## 📞 技术支持

- 模型验证: 使用 test-model-loading-debug.html
- 性能优化: 查看 PerformanceMonitor.js 报告
- 错误排查: 检查浏览器控制台日志
"""
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        print(f"📊 下载报告已生成: {report_file}")

def main():
    """主函数"""
    downloader = EnterpriseModelDownloader()
    
    print("选择操作:")
    print("1. 下载所有企业级模型")
    print("2. 显示Ready Player Me指南")
    print("3. 生成模型报告")
    print("4. 验证现有模型")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        success_count = downloader.download_all_models()
        if success_count > 0:
            downloader.generate_download_report()
        downloader.list_ready_player_me_guide()
    
    elif choice == "2":
        downloader.list_ready_player_me_guide()
    
    elif choice == "3":
        downloader.generate_download_report()
    
    elif choice == "4":
        print("\n🔍 验证现有模型...")
        models_dir = Path(__file__).parent / "models"
        
        for model_file in models_dir.glob("*.glb"):
            is_valid, msg = downloader.verify_model(model_file)
            status = "✅" if is_valid else "❌"
            print(f"{status} {model_file.name}: {msg}")
    
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()