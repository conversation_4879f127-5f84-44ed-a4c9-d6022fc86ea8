/**
 * 企业级女性客服角色系统
 * 专门为女性客服数字人设计的角色管理和外观增强
 */
class FemaleCustomerServiceCharacter {
    constructor(digitalHuman, options = {}) {
        this.digitalHuman = digitalHuman;
        this.options = {
            characterType: options.characterType || 'professional_friendly',
            appearanceStyle: options.appearanceStyle || 'business_elegant',
            personalityProfile: options.personalityProfile || 'warm_professional',
            voiceStyle: options.voiceStyle || 'gentle_confident',
            ...options
        };
        
        // 企业级专业女性客服角色配置
        this.characterProfiles = {
            'professional_friendly': {
                name: '林雅婷',
                title: '高级客户服务专员',
                personality: '专业、友善、耐心、细致入微',
                expertise: ['产品专业咨询', '技术支持服务', '售后问题处理', '客户关系维护'],
                greeting: '您好！我是林雅婷，很荣幸为您提供专业的客户服务。请问今天有什么可以帮助您的？',
                appearance: {
                    hairStyle: 'professional_bob_cut',
                    hairColor: '#2c1810', // 自然黑棕色
                    skinTone: '#f5ddd1', // 自然肤色
                    eyeColor: '#3e2723', // 深棕色眼睛
                    makeup: 'professional_elegant'
                },
                clothing: {
                    style: 'corporate_business_suit',
                    colors: ['#1e3a8a', '#ffffff', '#374151'], // 深蓝、白色、深灰
                    accessories: ['pearl_earrings', 'professional_watch', 'corporate_badge']
                },
                voice: {
                    tone: 'warm_professional',
                    pace: 'measured_clear',
                    pitch: 'medium_pleasant'
                }
            },
            'corporate_executive': {
                name: '陈总监',
                title: '客户关系总监',
                personality: '权威、专业、决策果断、商务精英',
                expertise: ['战略客户管理', '高端商务咨询', '企业解决方案', 'VIP客户服务', '商务谈判'],
                greeting: '您好，我是陈总监。作为客户关系总监，我将亲自为您提供最高级别的专业服务。',
                appearance: {
                    hairStyle: 'executive_elegant_updo',
                    hairColor: '#1a1a1a', // 纯黑色
                    skinTone: '#f0e6d2', // 精致肤色
                    eyeColor: '#1a0e0a', // 深黑眼睛
                    makeup: 'executive_sophisticated'
                },
                clothing: {
                    style: 'luxury_executive_suit',
                    colors: ['#0f172a', '#ffffff', '#64748b'], // 深黑、纯白、高级灰
                    accessories: ['designer_earrings', 'luxury_watch', 'executive_pin', 'designer_glasses']
                },
                voice: {
                    tone: 'authoritative_confident',
                    pace: 'deliberate_powerful',
                    pitch: 'medium_commanding'
                }
            },
            'technical_specialist': {
                name: '刘工程师',
                title: '技术服务专家',
                personality: '专业技术背景、逻辑清晰、解决方案导向、耐心专业',
                expertise: ['技术故障诊断', '系统集成支持', '产品技术培训', '定制化解决方案', '技术咨询'],
                greeting: '您好，我是刘工程师，专门负责技术服务支持。请详细描述您遇到的技术问题，我会为您提供专业的解决方案。',
                appearance: {
                    hairStyle: 'modern_professional_style',
                    hairColor: '#4a3728', // 深棕色
                    skinTone: '#fdbcb4', // 温和肤色
                    eyeColor: '#2e1a05', // 温和眼色
                    makeup: 'natural_professional'
                },
                clothing: {
                    style: 'smart_business_casual',
                    colors: ['#3b82f6', '#ffffff', '#6b7280'], // 科技蓝、白色、中性灰
                    accessories: ['smart_glasses', 'tech_watch', 'professional_badge']
                },
                voice: {
                    tone: 'clear_analytical',
                    pace: 'patient_explanatory',
                    pitch: 'medium_clear'
                }
            }
        };
        
        // 表情和动作配置
        this.femaleExpressions = {
            'welcoming_smile': {
                description: '温暖的欢迎微笑',
                morphTargets: {
                    'smile': 0.8,
                    'eyebrow_up': 0.3,
                    'eye_squint': 0.2
                },
                duration: 2000
            },
            'empathetic_concern': {
                description: '同理心关切表情',
                morphTargets: {
                    'brow_down': 0.4,
                    'mouth_press': 0.3,
                    'eye_close': 0.1
                },
                duration: 3000
            },
            'confident_explanation': {
                description: '自信解释表情',
                morphTargets: {
                    'smile': 0.5,
                    'eyebrow_inner_up': 0.3,
                    'mouth_open': 0.2
                },
                duration: 4000
            },
            'gentle_apology': {
                description: '温和道歉表情',
                morphTargets: {
                    'brow_down': 0.5,
                    'mouth_frown': 0.3,
                    'eye_close': 0.2
                },
                duration: 3000
            },
            'professional_listening': {
                description: '专业倾听表情',
                morphTargets: {
                    'eyebrow_up': 0.2,
                    'mouth_neutral': 0.8,
                    'eye_wide': 0.1
                },
                duration: 5000
            }
        };
        
        // 女性客服专用手势
        this.femaleGestures = {
            'elegant_greeting': {
                description: '优雅的问候手势',
                animation: 'wave_right',
                style: 'gentle_refined'
            },
            'graceful_presentation': {
                description: '优雅的展示手势',
                animation: 'presentation',
                style: 'flowing_professional'
            },
            'caring_gesture': {
                description: '关怀手势',
                animation: 'hand_to_heart',
                style: 'warm_caring'
            },
            'polite_indication': {
                description: '礼貌指示手势',
                animation: 'point_gentle',
                style: 'refined_directional'
            }
        };
        
        // 当前选中的角色
        this.currentCharacter = null;
        this.isInitialized = false;
        
        this.init();
    }
    
    /**
     * 初始化女性客服角色系统
     */
    init() {
        console.log('👩‍💼 初始化女性客服角色系统...');
        
        // 设置默认角色
        this.setCharacter(this.options.characterType);
        
        // 绑定数字人事件
        if (this.digitalHuman) {
            this.digitalHuman.on('modelLoaded', (data) => {
                this.onModelLoaded(data.model);
            });
        }
        
        this.isInitialized = true;
        console.log('✅ 女性客服角色系统初始化完成');
    }
    
    /**
     * 设置角色类型
     */
    setCharacter(characterType) {
        if (!this.characterProfiles[characterType]) {
            console.warn(`⚠️ 未找到角色类型: ${characterType}`);
            characterType = 'professional_friendly';
        }
        
        this.currentCharacter = this.characterProfiles[characterType];
        console.log(`👩‍💼 设置角色: ${this.currentCharacter.name} - ${this.currentCharacter.title}`);
        
        // 如果模型已加载，立即应用角色设置
        if (this.digitalHuman && this.digitalHuman.model) {
            this.applyCharacterToModel();
        }
        
        return this.currentCharacter;
    }
    
    /**
     * 当模型加载完成时应用角色设置
     */
    onModelLoaded(model) {
        console.log('👩‍💼 模型加载完成，应用女性客服角色设置...');
        this.applyCharacterToModel();
    }
    
    /**
     * 将角色设置应用到模型
     */
    applyCharacterToModel() {
        if (!this.currentCharacter || !this.digitalHuman.model) {
            return;
        }
        
        console.log(`🎨 应用角色外观: ${this.currentCharacter.name}`);
        
        // 应用外观设置
        this.applyAppearanceSettings();
        
        // 应用服装样式
        this.applyClothingStyle();
        
        // 设置默认表情
        this.setDefaultExpression();
        
        // 配置语音设置
        this.configureVoiceSettings();
        
        console.log('✅ 女性客服角色应用完成');
    }
    
    /**
     * 应用外观设置
     */
    applyAppearanceSettings() {
        const appearance = this.currentCharacter.appearance;
        
        this.digitalHuman.model.traverse((child) => {
            if (child.isMesh && child.material) {
                this.applyAppearanceToMesh(child, appearance);
            }
        });
    }
    
    /**
     * 为网格应用外观设置
     */
    applyAppearanceToMesh(mesh, appearance) {
        const meshName = mesh.name.toLowerCase();
        
        // 皮肤颜色
        if (this.isSkinMesh(meshName)) {
            mesh.material.color.setHex(this.parseColor(appearance.skinTone));
            
            // 添加女性皮肤特有的光泽
            if (mesh.material.roughness !== undefined) {
                mesh.material.roughness = 0.6; // 稍微光滑一些
                mesh.material.metalness = 0.02; // 轻微的光泽
            }
        }
        
        // 头发颜色和样式
        if (this.isHairMesh(meshName)) {
            mesh.material.color.setHex(this.parseColor(appearance.hairColor));
            
            // 头发特有的光泽和透明度
            if (mesh.material.roughness !== undefined) {
                mesh.material.roughness = 0.3; // 头发的自然光泽
                mesh.material.metalness = 0.1;
            }
        }
        
        // 眼睛颜色
        if (this.isEyeMesh(meshName)) {
            mesh.material.color.setHex(this.parseColor(appearance.eyeColor));
            
            // 眼睛的湿润感
            if (mesh.material.roughness !== undefined) {
                mesh.material.roughness = 0.05; // 非常光滑
                mesh.material.metalness = 0.0;
                mesh.material.transparent = true;
                mesh.material.opacity = 0.95;
            }
        }
    }
    
    /**
     * 应用服装样式
     */
    applyClothingStyle() {
        const clothing = this.currentCharacter.clothing;
        
        this.digitalHuman.model.traverse((child) => {
            if (child.isMesh && this.isClothingMesh(child.name.toLowerCase())) {
                this.applyClothingToMesh(child, clothing);
            }
        });
        
        // 添加配饰
        this.addAccessories(clothing.accessories);
    }
    
    /**
     * 为服装网格应用样式
     */
    applyClothingToMesh(mesh, clothing) {
        const meshName = mesh.name.toLowerCase();
        
        // 根据服装部位应用不同颜色
        if (meshName.includes('jacket') || meshName.includes('blazer')) {
            mesh.material.color.setHex(this.parseColor(clothing.colors[0])); // 主色调
        } else if (meshName.includes('shirt') || meshName.includes('blouse')) {
            mesh.material.color.setHex(this.parseColor(clothing.colors[1])); // 衬衫色
        } else if (meshName.includes('skirt') || meshName.includes('pants')) {
            mesh.material.color.setHex(this.parseColor(clothing.colors[2])); // 下装色
        }
        
        // 应用织物材质属性
        this.applyFabricMaterial(mesh, clothing.style);
    }
    
    /**
     * 应用织物材质
     */
    applyFabricMaterial(mesh, style) {
        if (!mesh.material.roughness) return;
        
        switch (style) {
            case 'business_suit':
                mesh.material.roughness = 0.7;
                mesh.material.metalness = 0.1;
                break;
            case 'business_casual':
                mesh.material.roughness = 0.8;
                mesh.material.metalness = 0.0;
                break;
            case 'executive_suit':
                mesh.material.roughness = 0.6;
                mesh.material.metalness = 0.15;
                break;
        }
    }
    
    /**
     * 添加配饰
     */
    addAccessories(accessories) {
        accessories.forEach(accessory => {
            this.createAccessory(accessory);
        });
    }
    
    /**
     * 创建配饰
     */
    createAccessory(accessoryType) {
        switch (accessoryType) {
            case 'pearl_earrings':
                this.createPearlEarrings();
                break;
            case 'simple_earrings':
                this.createSimpleEarrings();
                break;
            case 'gold_earrings':
                this.createGoldEarrings();
                break;
            case 'professional_watch':
                this.createProfessionalWatch();
                break;
            case 'luxury_watch':
                this.createLuxuryWatch();
                break;
        }
    }
    
    /**
     * 创建珍珠耳环
     */
    createPearlEarrings() {
        const earringGeometry = new THREE.SphereGeometry(0.15, 16, 16);
        const earringMaterial = new THREE.MeshPhysicalMaterial({
            color: 0xf8f8ff,
            roughness: 0.1,
            metalness: 0.0,
            clearcoat: 0.8,
            clearcoatRoughness: 0.1,
            transmission: 0.1
        });
        
        const leftEarring = new THREE.Mesh(earringGeometry, earringMaterial);
        const rightEarring = new THREE.Mesh(earringGeometry, earringMaterial);
        
        this.positionEarrings(leftEarring, rightEarring);
        
        console.log('💎 添加珍珠耳环');
    }
    
    /**
     * 定位耳环
     */
    positionEarrings(leftEarring, rightEarring) {
        // 查找头部骨骼来定位耳环
        this.digitalHuman.model.traverse((child) => {
            if (child.isSkinnedMesh && child.skeleton) {
                const bones = child.skeleton.bones;
                const headBone = bones.find(bone => 
                    bone.name.toLowerCase().includes('head'));
                
                if (headBone) {
                    // 女性耳环位置稍微精致一些
                    leftEarring.position.set(-0.8, 0.2, 0.1);
                    rightEarring.position.set(0.8, 0.2, 0.1);
                    
                    headBone.add(leftEarring);
                    headBone.add(rightEarring);
                }
            }
        });
    }
    
    /**
     * 设置默认表情
     */
    setDefaultExpression() {
        // 设置温暖的欢迎微笑作为默认表情
        this.setFemaleExpression('welcoming_smile');
    }
    
    /**
     * 设置女性表情
     */
    setFemaleExpression(expressionType) {
        if (!this.femaleExpressions[expressionType]) {
            console.warn(`⚠️ 未找到表情类型: ${expressionType}`);
            return;
        }
        
        const expression = this.femaleExpressions[expressionType];
        
        if (this.digitalHuman.setEmotion) {
            // 使用数字人的表情系统
            this.digitalHuman.setEmotion(expressionType, 0.7, {
                duration: expression.duration,
                style: 'feminine_professional'
            });
        }
        
        console.log(`😊 设置女性表情: ${expression.description}`);
    }
    
    /**
     * 执行女性手势
     */
    performFemaleGesture(gestureType) {
        if (!this.femaleGestures[gestureType]) {
            console.warn(`⚠️ 未找到手势类型: ${gestureType}`);
            return;
        }
        
        const gesture = this.femaleGestures[gestureType];
        
        if (this.digitalHuman.playHandGesture) {
            this.digitalHuman.playHandGesture(gesture.animation);
        }
        
        console.log(`👋 执行女性手势: ${gesture.description}`);
    }
    
    /**
     * 配置语音设置
     */
    configureVoiceSettings() {
        const voice = this.currentCharacter.voice;
        
        if (this.digitalHuman.voiceAdapter) {
            // 配置女性语音参数
            this.digitalHuman.voiceAdapter.configureVoice({
                gender: 'female',
                tone: voice.tone,
                pace: voice.pace,
                pitch: voice.pitch,
                language: 'zh-CN'
            });
        }
        
        console.log(`🎤 配置女性语音: ${voice.tone}`);
    }
    
    /**
     * 获取角色信息
     */
    getCharacterInfo() {
        return {
            current: this.currentCharacter,
            available: Object.keys(this.characterProfiles),
            expressions: Object.keys(this.femaleExpressions),
            gestures: Object.keys(this.femaleGestures)
        };
    }
    
    /**
     * 切换角色
     */
    switchCharacter(newCharacterType) {
        console.log(`🔄 切换角色: ${newCharacterType}`);
        
        const oldCharacter = this.currentCharacter;
        this.setCharacter(newCharacterType);
        
        console.log(`✅ 角色切换完成: ${oldCharacter?.name} -> ${this.currentCharacter.name}`);
        
        return this.currentCharacter;
    }
    
    // 辅助方法
    isSkinMesh(meshName) {
        const skinKeywords = ['face', 'head', 'body', 'skin', 'neck', 'hand', 'arm', 'leg'];
        return skinKeywords.some(keyword => meshName.includes(keyword));
    }
    
    isHairMesh(meshName) {
        const hairKeywords = ['hair', 'scalp'];
        return hairKeywords.some(keyword => meshName.includes(keyword));
    }
    
    isEyeMesh(meshName) {
        const eyeKeywords = ['eye', 'pupil', 'iris'];
        return eyeKeywords.some(keyword => meshName.includes(keyword));
    }
    
    isClothingMesh(meshName) {
        const clothingKeywords = ['clothing', 'shirt', 'dress', 'suit', 'jacket', 'skirt', 'pants', 'blouse'];
        return clothingKeywords.some(keyword => meshName.includes(keyword));
    }
    
    parseColor(colorString) {
        if (colorString.startsWith('#')) {
            return parseInt(colorString.substring(1), 16);
        }
        return 0xffffff;
    }
    
    /**
     * 销毁角色系统
     */
    destroy() {
        this.currentCharacter = null;
        this.isInitialized = false;
        console.log('🔄 女性客服角色系统已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FemaleCustomerServiceCharacter;
} else {
    window.FemaleCustomerServiceCharacter = FemaleCustomerServiceCharacter;
}