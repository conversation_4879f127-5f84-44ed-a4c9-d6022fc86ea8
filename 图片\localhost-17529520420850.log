VoiceAdapter-simple.js:74 VoiceAdapter 已全局导出
VoiceAdapter-simple.js:77 VoiceAdapter-simple.js 加载完成
PerformanceOptimizer.js:1 Uncaught SyntaxError: Identifier 'PerformanceOptimizer' has already been declared (at PerformanceOptimizer.js:1:1)
index-complete-customer-service.html:1404 🚀 启动完整企业客服数字人应用...
index-complete-customer-service.html:813 🚀 启动完整企业客服数字人系统...
EnterpriseDigitalHuman.js:264 正在初始化加载器...
EnterpriseDigitalHuman.js:271 THREE对象: {ACESFilmicToneMapping: 4, AddEquation: 100, AddOperation: 2, AdditiveAnimationBlendMode: 2501, AdditiveBlending: 2, …}
EnterpriseDigitalHuman.js:272 THREE.js 版本: 128
EnterpriseDigitalHuman.js:275 检查GLTFLoader可用性...
EnterpriseDigitalHuman.js:276 THREE.GLTFLoader: function
EnterpriseDigitalHuman.js:277 window.GLTFLoader: undefined
EnterpriseDigitalHuman.js:284 使用 THREE.GLTFLoader
EnterpriseDigitalHuman.js:297 GLTFLoader 初始化成功: GLTFLoader {manager: sl, crossOrigin: 'anonymous', withCredentials: false, path: '', resourcePath: '', …}
EnterpriseDigitalHuman.js:313 FBXLoader 不可用（可选）
EnterpriseDigitalHuman.js:318 TextureLoader 初始化成功
EnterpriseDigitalHuman.js:320 所有加载器初始化完成
VoiceAdapter-simple.js:10 VoiceAdapter 简化版已创建
VoiceAdapter-simple.js:17 VoiceAdapter 简化版初始化成功
EnterpriseDigitalHuman.js:761 VoiceAdapter 初始化成功
EnterpriseDigitalHuman.js:365 开始加载模型: ./models/business_female_professional.glb
EnterpriseDigitalHuman.js:376 加载进度: 22.504412562582843%
EnterpriseDigitalHuman.js:376 加载进度: 100%
EnterpriseDigitalHuman.js:383 GLTF模型加载失败: SyntaxError: Unexpected token '<', "







<!DOCTYPE "... is not valid JSON
    at JSON.parse (<anonymous>)
    at GLTFLoader.parse (GLTFLoader.js:200:22)
    at Object.onLoad (GLTFLoader.js:95:12)
    at XMLHttpRequest.<anonymous> (three.min.js:6:436514)
（匿名） @ EnterpriseDigitalHuman.js:383
_onError @ GLTFLoader.js:73
（匿名） @ GLTFLoader.js:104
（匿名） @ three.min.js:6
load
load @ three.min.js:6
load @ GLTFLoader.js:91
loadGLTFModel @ EnterpriseDigitalHuman.js:367
loadModel @ EnterpriseDigitalHuman.js:341
init @ EnterpriseDigitalHuman.js:92
EnterpriseDigitalHuman @ EnterpriseDigitalHuman.js:78
initializeDigitalHuman @ index-complete-customer-service.html:843
init @ index-complete-customer-service.html:817
CompleteCustomerServiceApp @ index-complete-customer-service.html:809
（匿名） @ index-complete-customer-service.html:1408
setTimeout
（匿名） @ index-complete-customer-service.html:1407
EnterpriseDigitalHuman.js:384 模型路径: ./models/business_female_professional.glb
（匿名） @ EnterpriseDigitalHuman.js:384
_onError @ GLTFLoader.js:73
（匿名） @ GLTFLoader.js:104
（匿名） @ three.min.js:6
load
load @ three.min.js:6
load @ GLTFLoader.js:91
loadGLTFModel @ EnterpriseDigitalHuman.js:367
loadModel @ EnterpriseDigitalHuman.js:341
init @ EnterpriseDigitalHuman.js:92
EnterpriseDigitalHuman @ EnterpriseDigitalHuman.js:78
initializeDigitalHuman @ index-complete-customer-service.html:843
init @ index-complete-customer-service.html:817
CompleteCustomerServiceApp @ index-complete-customer-service.html:809
（匿名） @ index-complete-customer-service.html:1408
setTimeout
（匿名） @ index-complete-customer-service.html:1407
EnterpriseDigitalHuman.js:385 错误详情: {name: 'SyntaxError', message: `Unexpected token '<', "\n\n\n\n\n\n\n\n<!DOCTYPE "... is not valid JSON`, stack: `SyntaxError: Unexpected token '<', "\n\n\n\n\n\n\n\n<!DOCT…http://localhost:8000/libs/three.min.js:6:436514)`, type: 'object', value: SyntaxError: Unexpected token '<', "







<!DOCTYPE "... is not valid JSON
    at JSON.parse (<an…}
（匿名） @ EnterpriseDigitalHuman.js:385
_onError @ GLTFLoader.js:73
（匿名） @ GLTFLoader.js:104
（匿名） @ three.min.js:6
load
load @ three.min.js:6
load @ GLTFLoader.js:91
loadGLTFModel @ EnterpriseDigitalHuman.js:367
loadModel @ EnterpriseDigitalHuman.js:341
init @ EnterpriseDigitalHuman.js:92
EnterpriseDigitalHuman @ EnterpriseDigitalHuman.js:78
initializeDigitalHuman @ index-complete-customer-service.html:843
init @ index-complete-customer-service.html:817
CompleteCustomerServiceApp @ index-complete-customer-service.html:809
（匿名） @ index-complete-customer-service.html:1408
setTimeout
（匿名） @ index-complete-customer-service.html:1407
index-complete-customer-service.html:860 ❌ 数字人加载失败: 模型加载失败: Unexpected token '<', "







<!DOCTYPE "... is not valid JSON
onError @ index-complete-customer-service.html:860
（匿名） @ EnterpriseDigitalHuman.js:409
_onError @ GLTFLoader.js:73
（匿名） @ GLTFLoader.js:104
（匿名） @ three.min.js:6
load
load @ three.min.js:6
load @ GLTFLoader.js:91
loadGLTFModel @ EnterpriseDigitalHuman.js:367
loadModel @ EnterpriseDigitalHuman.js:341
init @ EnterpriseDigitalHuman.js:92
EnterpriseDigitalHuman @ EnterpriseDigitalHuman.js:78
initializeDigitalHuman @ index-complete-customer-service.html:843
init @ index-complete-customer-service.html:817
CompleteCustomerServiceApp @ index-complete-customer-service.html:809
（匿名） @ index-complete-customer-service.html:1408
setTimeout
（匿名） @ index-complete-customer-service.html:1407
