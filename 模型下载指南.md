# 企业级数字人模型下载指南

## 📋 模型要求规格

### 基本要求
- **类型**: 全身人体模型（非卡通风格）
- **骨骼**: 完整骨骼系统，支持面部、手部、身体动画
- **面数**: ≤ 50,000 三角面
- **格式**: GLB（推荐）或 FBX
- **纹理**: 1024x1024 或更高分辨率

### 推荐命名
- `business_male.glb` - 商务男性模型
- `business_female.glb` - 商务女性模型

## 🌐 推荐下载源

### 1. Khronos glTF样本模型 (免费)
**网址**: https://github.com/KhronosGroup/glTF-Sample-Models

**推荐模型**:
- `RiggedFigure.glb` - 适合作为女性模型
- `RiggedSimple.glb` - 适合作为男性模型

**下载命令**:
```bash
# 女性模型
curl -L -o models/business_female.glb "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedFigure/glTF-Binary/RiggedFigure.glb"

# 男性模型  
curl -L -o models/business_male.glb "https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedSimple/glTF-Binary/RiggedSimple.glb"
```

### 2. Mixamo角色 (免费注册)
**网址**: https://www.mixamo.com

**特点**:
- Adobe官方3D角色库
- 高质量真人风格模型
- 自带动画系统
- 需要Adobe账号

**使用步骤**:
1. 注册Adobe账号
2. 选择商务风格角色
3. 下载FBX格式
4. 用Blender转换为GLB

### 3. Sketchfab (部分免费)
**网址**: https://sketchfab.com

**搜索关键词**:
- "business person"
- "office worker"
- "professional character"
- 筛选：Free、Rigged、GLB

### 4. Ready Player Me (免费)
**网址**: https://readyplayer.me

**特点**:
- 可定制化数字人
- 现代风格
- 直接生成GLB格式

## 🛠️ 模型优化工具

### Blender优化流程
1. **导入模型**
   ```
   File → Import → glTF 2.0
   ```

2. **检查规格**
   - 面数: 统计面板查看
   - 骨骼: 检查Armature
   - 材质: Material Properties

3. **优化操作**
   ```
   # 减面 (如果超过5万面)
   Modifier → Decimate → Ratio: 0.8
   
   # 纹理优化
   Shader Editor → 调整贴图分辨率
   
   # 清理
   Mesh → Clean Up → Merge by Distance
   ```

4. **导出设置**
   ```
   File → Export → glTF 2.0
   Format: GLB (Binary)
   Include: Selected Objects, Animations
   ```

## 📁 文件结构

```
数字人4/
├── models/
│   ├── business_female.glb     # 女性商务模型
│   ├── business_male.glb       # 男性商务模型
│   ├── RiggedSimple.glb       # 备用模型
│   └── README.md              # 模型说明
├── index-enterprise.html      # 企业版主页
└── download-business-models.py # 自动下载脚本
```

## 🚀 快速安装

### 方式一：自动下载脚本
```bash
python download-business-models.py
```

### 方式二：手动下载
1. 访问推荐网站
2. 下载符合规格的模型
3. 重命名为标准文件名
4. 放入 `models/` 目录

### 方式三：使用现有模型
```bash
# 临时使用现有模型
copy models/RiggedSimple.glb models/business_female.glb
copy models/RiggedSimple.glb models/business_male.glb
```

## ⚙️ 验证模型

### 检查项目
- ✅ 文件大小 > 1MB
- ✅ 面数 ≤ 50,000
- ✅ 包含骨骼动画
- ✅ GLB/FBX格式
- ✅ 加载无错误

### 测试加载
```javascript
// 在浏览器控制台测试
const loader = new THREE.GLTFLoader();
loader.load('./models/business_female.glb', 
    (gltf) => console.log('模型加载成功', gltf),
    (progress) => console.log('加载进度', progress),
    (error) => console.error('加载失败', error)
);
```

## 🎨 美化建议

### 换装优化
- 商务正装（西装、衬衫）
- 职业发型
- 自然肤色
- 专业妆容

### 动画调整
- 优雅的待机姿势
- 专业的手势动作
- 自然的表情变化
- 流畅的行走动作

## 🔧 故障排除

### 常见问题

**问题**: 模型不显示
**解决**: 检查文件路径和GLB格式

**问题**: 动画不播放
**解决**: 确保模型包含骨骼和动画数据

**问题**: 加载很慢
**解决**: 优化模型面数和纹理大小

**问题**: 表情不自然
**解决**: 检查面部骨骼绑定和混合形状

### 技术支持
- 查看浏览器控制台错误信息
- 检查网络连接
- 验证文件完整性
- 确认Three.js版本兼容性

---

**提示**: 第一次使用建议先用自动下载脚本获取基础模型，然后根据需要在Blender中进一步美化。