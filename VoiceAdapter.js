/**
 * 语音功能适配器
 * 专为适配公司语音系统设计，避免功能重复
 * 支持多种语音服务接入和降级处理
 */

class VoiceAdapter {
    constructor(options = {}) {
        this.options = {
            mode: options.mode || 'company_integration', // 'company_integration', 'web_api', 'hybrid'
            fallbackToWebAPI: options.fallbackToWebAPI !== false,
            debug: options.debug || false,
            timeout: options.timeout || 10000,
            retryAttempts: options.retryAttempts || 2,
            ...options
        };
        
        // 状态管理
        this.isInitialized = false;
        this.isProcessing = false;
        this.currentVoiceSession = null;
        
        // 公司语音系统配置
        this.companyVoiceConfig = {
            endpoint: null,           // 将来配置公司API端点
            apiKey: null,            // 将来配置API密钥
            model: null,             // 将来配置语音模型
            voice: null,             // 将来配置语音类型
            customHeaders: {},       // 自定义请求头
            customParams: {}         // 自定义参数
        };
        
        // Web API降级配置
        this.webAPIConfig = {
            enabled: this.options.fallbackToWebAPI,
            preferredVoices: ['zh-CN-XiaoxiaoNeural', 'zh-CN-YunyangNeural'],
            settings: {
                rate: 1.0,
                pitch: 1.0,
                volume: 1.0,
                lang: 'zh-CN'
            }
        };
        
        // 性能监控
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            avgResponseTime: 0,
            lastRequestTime: 0
        };
        
        this.init();
    }
    
    /**
     * 初始化语音适配器
     */
    async init() {
        try {
            // 检测公司语音系统可用性
            const companyVoiceAvailable = await this.detectCompanyVoiceSystem();
            
            if (companyVoiceAvailable) {
                await this.initCompanyVoiceSystem();
                this.log('✅ 公司语音系统初始化成功');
            } else {
                this.log('⚠️ 公司语音系统不可用，将使用降级方案');
                
                if (this.options.fallbackToWebAPI) {
                    await this.initWebAPI();
                    this.log('✅ Web API降级方案初始化成功');
                }
            }
            
            this.isInitialized = true;
            this.log('🎤 语音适配器初始化完成');
            
        } catch (error) {
            console.error('语音适配器初始化失败:', error);
            this.isInitialized = false;
        }
    }
    
    /**
     * 检测公司语音系统
     */
    async detectCompanyVoiceSystem() {
        // 检查是否配置了公司语音系统参数
        if (this.companyVoiceConfig.endpoint) {
            try {
                // 发送测试请求验证连通性
                const response = await fetch(this.companyVoiceConfig.endpoint + '/health', {
                    method: 'GET',
                    timeout: 5000
                });
                
                return response.ok;
            } catch (error) {
                this.log('公司语音系统连接测试失败:', error.message);
                return false;
            }
        }
        
        // 检查是否有全局的公司语音接口
        if (typeof window !== 'undefined' && window.CompanyVoiceAPI) {
            return true;
        }
        
        return false;
    }
    
    /**
     * 初始化公司语音系统
     */
    async initCompanyVoiceSystem() {
        if (window.CompanyVoiceAPI) {
            // 如果有全局的公司语音API，直接使用
            this.companyVoiceAPI = window.CompanyVoiceAPI;
            
            // 初始化API
            if (this.companyVoiceAPI.init) {
                await this.companyVoiceAPI.init(this.companyVoiceConfig);
            }
            
        } else if (this.companyVoiceConfig.endpoint) {
            // 如果配置了端点，创建HTTP客户端
            this.createCompanyVoiceHTTPClient();
        }
    }
    
    /**
     * 创建公司语音HTTP客户端
     */
    createCompanyVoiceHTTPClient() {
        this.companyVoiceClient = {
            async synthesize(text, options = {}) {
                const response = await fetch(this.companyVoiceConfig.endpoint + '/synthesize', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${this.companyVoiceConfig.apiKey}`,
                        ...this.companyVoiceConfig.customHeaders
                    },
                    body: JSON.stringify({
                        text: text,
                        voice: options.voice || this.companyVoiceConfig.voice,
                        model: options.model || this.companyVoiceConfig.model,
                        ...this.companyVoiceConfig.customParams,
                        ...options
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`公司语音API错误: ${response.status} ${response.statusText}`);
                }
                
                return await response.json();
            },
            
            async recognize(audioData, options = {}) {
                const formData = new FormData();
                formData.append('audio', audioData);
                formData.append('options', JSON.stringify(options));
                
                const response = await fetch(this.companyVoiceConfig.endpoint + '/recognize', {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.companyVoiceConfig.apiKey}`,
                        ...this.companyVoiceConfig.customHeaders
                    },
                    body: formData
                });
                
                if (!response.ok) {
                    throw new Error(`公司语音识别API错误: ${response.status} ${response.statusText}`);
                }
                
                return await response.json();
            }
        };
    }
    
    /**
     * 初始化Web API降级方案
     */
    async initWebAPI() {
        if (typeof window === 'undefined') return;
        
        // 检查Speech Synthesis API支持
        if ('speechSynthesis' in window) {
            this.webSpeechSynthesis = window.speechSynthesis;
            
            // 加载语音列表
            await this.loadVoices();
            
            this.log('Web Speech Synthesis API 可用');
        }
        
        // 检查Speech Recognition API支持
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.webSpeechRecognition = new SpeechRecognition();
            
            this.setupSpeechRecognition();
            this.log('Web Speech Recognition API 可用');
        }
    }
    
    /**
     * 加载可用语音
     */
    async loadVoices() {
        return new Promise((resolve) => {
            const loadVoicesImpl = () => {
                this.availableVoices = this.webSpeechSynthesis.getVoices();
                
                if (this.availableVoices.length > 0) {
                    this.selectOptimalVoice();
                    resolve();
                } else {
                    // 某些浏览器需要等待
                    setTimeout(loadVoicesImpl, 100);
                }
            };
            
            this.webSpeechSynthesis.addEventListener('voiceschanged', loadVoicesImpl);
            loadVoicesImpl();
        });
    }
    
    /**
     * 选择最佳语音
     */
    selectOptimalVoice() {
        const chineseVoices = this.availableVoices.filter(voice => 
            voice.lang.startsWith('zh') || voice.lang.startsWith('cmn')
        );
        
        // 优先选择配置的首选语音
        for (const preferredVoice of this.webAPIConfig.preferredVoices) {
            const voice = chineseVoices.find(v => v.name.includes(preferredVoice));
            if (voice) {
                this.selectedVoice = voice;
                this.log(`选择语音: ${voice.name}`);
                return;
            }
        }
        
        // 如果没找到首选语音，选择第一个中文语音
        if (chineseVoices.length > 0) {
            this.selectedVoice = chineseVoices[0];
            this.log(`选择语音: ${this.selectedVoice.name}`);
        } else {
            this.log('⚠️ 未找到中文语音，将使用默认语音');
        }
    }
    
    /**
     * 设置语音识别
     */
    setupSpeechRecognition() {
        this.webSpeechRecognition.continuous = false;
        this.webSpeechRecognition.interimResults = false;
        this.webSpeechRecognition.maxAlternatives = 1;
        this.webSpeechRecognition.lang = this.webAPIConfig.settings.lang;
    }
    
    /**
     * 处理语音合成请求
     */
    async processVoice(text, options = {}) {
        if (!this.isInitialized) {
            throw new Error('语音适配器未初始化');
        }
        
        if (this.isProcessing) {
            throw new Error('语音处理进行中，请稍后再试');
        }
        
        this.isProcessing = true;
        const startTime = performance.now();
        
        try {
            this.stats.totalRequests++;
            this.stats.lastRequestTime = startTime;
            
            let result;
            
            // 优先使用公司语音系统
            if (this.companyVoiceAPI || this.companyVoiceClient) {
                try {
                    result = await this.synthesizeWithCompanySystem(text, options);
                    this.log('✅ 使用公司语音系统成功');
                } catch (error) {
                    this.log('❌ 公司语音系统失败:', error.message);
                    
                    if (this.options.fallbackToWebAPI) {
                        result = await this.synthesizeWithWebAPI(text, options);
                        this.log('✅ 降级到Web API成功');
                    } else {
                        throw error;
                    }
                }
            } else {
                // 直接使用Web API
                result = await this.synthesizeWithWebAPI(text, options);
                this.log('✅ 使用Web API成功');
            }
            
            this.stats.successfulRequests++;
            this.updateAverageResponseTime(startTime);
            
            return result;
            
        } catch (error) {
            this.stats.failedRequests++;
            this.log('❌ 语音处理失败:', error.message);
            throw error;
        } finally {\n            this.isProcessing = false;\n        }\n    }\n    \n    /**\n     * 使用公司语音系统合成\n     */\n    async synthesizeWithCompanySystem(text, options) {\n        const requestOptions = {\n            voice: options.voice || 'business_female',\n            speed: options.speed || 1.0,\n            pitch: options.pitch || 1.0,\n            emotion: options.emotion || 'professional',\n            ...options\n        };\n        \n        let audioData;\n        \n        if (this.companyVoiceAPI) {\n            // 使用全局API\n            audioData = await this.companyVoiceAPI.synthesize(text, requestOptions);\n        } else if (this.companyVoiceClient) {\n            // 使用HTTP客户端\n            const response = await this.companyVoiceClient.synthesize(text, requestOptions);\n            audioData = response.audioData || response.audio;\n        }\n        \n        // 创建音频播放Promise\n        const playPromise = this.createAudioPlayPromise(audioData, 'company');\n        \n        return {\n            source: 'company',\n            audioData: audioData,\n            duration: this.estimateDuration(text),\n            playPromise: playPromise\n        };\n    }\n    \n    /**\n     * 使用Web API合成\n     */\n    async synthesizeWithWebAPI(text, options) {\n        if (!this.webSpeechSynthesis) {\n            throw new Error('Web Speech API 不可用');\n        }\n        \n        const utterance = new SpeechSynthesisUtterance(text);\n        \n        // 配置语音参数\n        utterance.lang = options.lang || this.webAPIConfig.settings.lang;\n        utterance.rate = options.rate || this.webAPIConfig.settings.rate;\n        utterance.pitch = options.pitch || this.webAPIConfig.settings.pitch;\n        utterance.volume = options.volume || this.webAPIConfig.settings.volume;\n        \n        // 选择语音\n        if (this.selectedVoice) {\n            utterance.voice = this.selectedVoice;\n        }\n        \n        // 创建播放Promise\n        const playPromise = new Promise((resolve, reject) => {\n            utterance.onstart = () => {\n                this.log('Web API 语音播放开始');\n            };\n            \n            utterance.onend = () => {\n                this.log('Web API 语音播放结束');\n                resolve();\n            };\n            \n            utterance.onerror = (event) => {\n                this.log('Web API 语音播放错误:', event.error);\n                reject(new Error(`语音播放错误: ${event.error}`));\n            };\n            \n            // 开始播放\n            this.webSpeechSynthesis.speak(utterance);\n        });\n        \n        return {\n            source: 'web_api',\n            utterance: utterance,\n            duration: this.estimateDuration(text),\n            playPromise: playPromise\n        };\n    }\n    \n    /**\n     * 创建音频播放Promise\n     */\n    createAudioPlayPromise(audioData, source) {\n        return new Promise((resolve, reject) => {\n            try {\n                // 这里需要根据公司音频数据格式进行适配\n                // 示例实现：\n                \n                if (typeof audioData === 'string') {\n                    // 如果是Base64或URL\n                    const audio = new Audio(audioData);\n                    \n                    audio.onended = () => {\n                        this.log(`${source} 音频播放结束`);\n                        resolve();\n                    };\n                    \n                    audio.onerror = (error) => {\n                        this.log(`${source} 音频播放错误:`, error);\n                        reject(error);\n                    };\n                    \n                    audio.play();\n                    \n                } else if (audioData instanceof ArrayBuffer || audioData instanceof Uint8Array) {\n                    // 如果是二进制数据\n                    const blob = new Blob([audioData], { type: 'audio/wav' });\n                    const url = URL.createObjectURL(blob);\n                    const audio = new Audio(url);\n                    \n                    audio.onended = () => {\n                        URL.revokeObjectURL(url);\n                        resolve();\n                    };\n                    \n                    audio.onerror = (error) => {\n                        URL.revokeObjectURL(url);\n                        reject(error);\n                    };\n                    \n                    audio.play();\n                    \n                } else {\n                    // 其他格式\n                    reject(new Error('不支持的音频数据格式'));\n                }\n                \n            } catch (error) {\n                reject(error);\n            }\n        });\n    }\n    \n    /**\n     * 语音识别\n     */\n    async recognizeSpeech(options = {}) {\n        if (!this.isInitialized) {\n            throw new Error('语音适配器未初始化');\n        }\n        \n        try {\n            // 优先使用公司语音识别\n            if (this.companyVoiceAPI && this.companyVoiceAPI.recognize) {\n                return await this.recognizeWithCompanySystem(options);\n            } else if (this.webSpeechRecognition) {\n                return await this.recognizeWithWebAPI(options);\n            } else {\n                throw new Error('没有可用的语音识别服务');\n            }\n        } catch (error) {\n            this.log('❌ 语音识别失败:', error.message);\n            throw error;\n        }\n    }\n    \n    /**\n     * 使用公司系统识别\n     */\n    async recognizeWithCompanySystem(options) {\n        // 这里需要根据公司API接口实现\n        // 示例实现：\n        \n        if (this.companyVoiceAPI && this.companyVoiceAPI.startRecognition) {\n            return await this.companyVoiceAPI.startRecognition(options);\n        }\n        \n        throw new Error('公司语音识别API未实现');\n    }\n    \n    /**\n     * 使用Web API识别\n     */\n    async recognizeWithWebAPI(options = {}) {\n        if (!this.webSpeechRecognition) {\n            throw new Error('Web Speech Recognition API 不可用');\n        }\n        \n        return new Promise((resolve, reject) => {\n            // 配置识别参数\n            this.webSpeechRecognition.lang = options.lang || this.webAPIConfig.settings.lang;\n            \n            this.webSpeechRecognition.onstart = () => {\n                this.log('语音识别开始');\n            };\n            \n            this.webSpeechRecognition.onresult = (event) => {\n                const result = event.results[0][0];\n                this.log('语音识别结果:', result.transcript);\n                \n                resolve({\n                    text: result.transcript,\n                    confidence: result.confidence,\n                    source: 'web_api'\n                });\n            };\n            \n            this.webSpeechRecognition.onerror = (event) => {\n                this.log('语音识别错误:', event.error);\n                reject(new Error(`语音识别错误: ${event.error}`));\n            };\n            \n            this.webSpeechRecognition.onend = () => {\n                this.log('语音识别结束');\n            };\n            \n            // 开始识别\n            this.webSpeechRecognition.start();\n        });\n    }\n    \n    /**\n     * 估算语音时长\n     */\n    estimateDuration(text) {\n        // 简单估算：中文约300字/分钟，英文约150词/分钟\n        const isChinese = /[\\u4e00-\\u9fa5]/.test(text);\n        const charCount = text.length;\n        \n        if (isChinese) {\n            return (charCount / 300) * 60 * 1000; // 毫秒\n        } else {\n            const wordCount = text.split(/\\s+/).length;\n            return (wordCount / 150) * 60 * 1000; // 毫秒\n        }\n    }\n    \n    /**\n     * 更新平均响应时间\n     */\n    updateAverageResponseTime(startTime) {\n        const responseTime = performance.now() - startTime;\n        this.stats.avgResponseTime = \n            (this.stats.avgResponseTime * 0.9) + (responseTime * 0.1);\n    }\n    \n    /**\n     * 配置公司语音系统\n     */\n    configureCompanyVoiceSystem(config) {\n        this.companyVoiceConfig = { ...this.companyVoiceConfig, ...config };\n        this.log('公司语音系统配置已更新:', config);\n        \n        // 如果已初始化，重新初始化公司系统\n        if (this.isInitialized) {\n            this.initCompanyVoiceSystem();\n        }\n    }\n    \n    /**\n     * 获取适配器状态\n     */\n    getStatus() {\n        return {\n            isInitialized: this.isInitialized,\n            isProcessing: this.isProcessing,\n            mode: this.options.mode,\n            companySystemAvailable: !!(this.companyVoiceAPI || this.companyVoiceClient),\n            webAPIAvailable: !!(this.webSpeechSynthesis && this.webSpeechRecognition),\n            selectedVoice: this.selectedVoice ? this.selectedVoice.name : null,\n            stats: { ...this.stats }\n        };\n    }\n    \n    /**\n     * 获取使用统计\n     */\n    getStats() {\n        const successRate = this.stats.totalRequests > 0 ? \n            (this.stats.successfulRequests / this.stats.totalRequests) * 100 : 0;\n        \n        return {\n            ...this.stats,\n            successRate: successRate.toFixed(2) + '%',\n            avgResponseTimeFormatted: this.stats.avgResponseTime.toFixed(2) + 'ms'\n        };\n    }\n    \n    /**\n     * 停止当前语音播放\n     */\n    stopCurrentVoice() {\n        if (this.webSpeechSynthesis && this.webSpeechSynthesis.speaking) {\n            this.webSpeechSynthesis.cancel();\n        }\n        \n        if (this.currentVoiceSession) {\n            // 停止公司语音系统播放\n            if (this.companyVoiceAPI && this.companyVoiceAPI.stop) {\n                this.companyVoiceAPI.stop();\n            }\n        }\n        \n        this.isProcessing = false;\n    }\n    \n    /**\n     * 日志输出\n     */\n    log(...args) {\n        if (this.options.debug) {\n            console.log('[VoiceAdapter]', ...args);\n        }\n    }\n    \n    /**\n     * 销毁适配器\n     */\n    destroy() {\n        this.stopCurrentVoice();\n        \n        if (this.webSpeechRecognition) {\n            this.webSpeechRecognition.abort();\n        }\n        \n        this.isInitialized = false;\n        this.log('🔄 语音适配器已销毁');\n    }\n}\n\n/**\n * 公司语音系统接口示例\n * 这个接口需要根据实际的公司语音系统API进行实现\n */\nclass CompanyVoiceSystemExample {\n    constructor() {\n        this.isInitialized = false;\n    }\n    \n    async init(config) {\n        // 初始化公司语音系统\n        console.log('初始化公司语音系统:', config);\n        this.isInitialized = true;\n    }\n    \n    async synthesize(text, options) {\n        // 文字转语音\n        console.log('公司语音合成:', text, options);\n        \n        // 返回音频数据（示例）\n        return {\n            audioData: 'data:audio/wav;base64,...', // Base64音频数据\n            duration: text.length * 100, // 估算时长\n            format: 'wav'\n        };\n    }\n    \n    async recognize(audioData, options) {\n        // 语音转文字\n        console.log('公司语音识别:', audioData, options);\n        \n        // 返回识别结果（示例）\n        return {\n            text: '识别的文字内容',\n            confidence: 0.95\n        };\n    }\n    \n    stop() {\n        // 停止当前播放\n        console.log('停止公司语音播放');\n    }\n}\n\n// 导出类\nif (typeof module !== 'undefined' && module.exports) {\n    module.exports = { VoiceAdapter, CompanyVoiceSystemExample };\n} else {\n    window.VoiceAdapter = VoiceAdapter;\n    window.CompanyVoiceSystemExample = CompanyVoiceSystemExample;\n}