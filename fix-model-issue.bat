@echo off
chcp 65001 >nul
title 修复GLB模型文件问题

echo.
echo ████████████████████████████████████████████████████████
echo ████████████████ GLB模型文件修复工具 ████████████████████
echo ████████████████████████████████████████████████████████
echo.

cd /d "%~dp0"

echo 📋 问题诊断:
echo ✅ 检测到模型加载错误: JSON解析失败
echo ✅ 原因: GLB文件实际是HTML内容，不是真正的二进制文件
echo ✅ 解决方案: 下载真正的GLB格式模型文件
echo.

echo 🔍 检查现有模型文件...
if exist "models\business_female_professional.glb" (
    echo 📄 检查 business_female_professional.glb 文件内容...
    
    REM 检查文件头是否为HTML
    powershell "Get-Content 'models\business_female_professional.glb' -TotalCount 1" | findstr /C:"<!DOCTYPE" >nul
    if !errorlevel! equ 0 (
        echo ❌ 确认: 文件是HTML内容，不是GLB格式
        echo 🗑️ 删除错误的文件...
        del "models\business_female_professional.glb"
        echo ✅ 已删除错误文件
    ) else (
        echo ✅ 文件格式似乎正确
    )
) else (
    echo ❌ 文件不存在: business_female_professional.glb
)

echo.
echo 📥 开始下载真正的GLB模型文件...

REM 检查Python环境
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 未检测到Python环境
    echo 💡 请选择下载方式:
    echo.
    echo 1. 手动下载 (推荐)
    echo 2. 使用现有模型
    echo 3. 安装Python后重试
    echo.
    
    choice /c 123 /m "请选择 (1-3): "
    
    if !errorlevel!==1 goto :manual_download
    if !errorlevel!==2 goto :use_existing
    if !errorlevel!==3 goto :install_python
    
) else (
    echo ✅ Python环境已就绪
    echo 🚀 正在下载GLB模型文件...
    python download-real-models.py
    goto :check_result
)

:manual_download
echo.
echo 📚 手动下载指南:
echo.
echo 1. 打开浏览器，访问以下任一链接:
echo    https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/CesiumMan/glTF-Binary/CesiumMan.glb
echo    https://github.com/KhronosGroup/glTF-Sample-Models/raw/master/2.0/RiggedSimple/glTF-Binary/RiggedSimple.glb
echo.
echo 2. 下载文件后，重命名为: business_female_professional.glb
echo.
echo 3. 将文件放到 models\ 目录下
echo.
echo 💡 或者使用现有的有效模型文件...
pause
goto :use_existing

:use_existing
echo.
echo 🔄 使用现有模型文件...

if exist "models\business_female.glb" (
    echo ✅ 发现: business_female.glb
    echo 📋 检查文件格式...
    
    REM 检查是否为有效GLB文件
    powershell "Get-Content 'models\business_female.glb' -Encoding Byte -TotalCount 4" | findstr /C:"glTF" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ✅ 文件格式正确 (GLB二进制)
        echo 📂 复制为专业客服模型...
        copy "models\business_female.glb" "models\business_female_professional.glb" >nul
        echo ✅ 已创建: business_female_professional.glb
        goto :success
    ) else (
        echo ❌ 文件格式可能有问题
    )
)

if exist "models\RiggedSimple.glb" (
    echo ✅ 发现: RiggedSimple.glb
    echo 📂 复制为专业客服模型...
    copy "models\RiggedSimple.glb" "models\business_female_professional.glb" >nul
    echo ✅ 已创建: business_female_professional.glb
    goto :success
)

echo ❌ 未找到可用的GLB模型文件
goto :download_suggestion

:install_python
echo 正在打开Python官网...
start https://www.python.org/downloads/
echo 💡 下载并安装Python后，重新运行此工具
pause
exit

:check_result
if exist "models\business_female_professional.glb" (
    echo ✅ GLB模型文件已准备就绪
    goto :success
) else (
    echo ❌ 下载可能失败，尝试使用现有文件...
    goto :use_existing
)

:success
echo.
echo 🎉 GLB模型文件修复完成!
echo.
echo 📋 文件状态:
if exist "models\business_female_professional.glb" (
    echo ✅ business_female_professional.glb - 已就绪
) else (
    echo ❌ business_female_professional.glb - 缺失
)

if exist "models\business_female.glb" (
    echo ✅ business_female.glb - 已就绪  
) else (
    echo ❌ business_female.glb - 缺失
)

echo.
echo 🚀 下一步操作:
echo 1. 双击运行: 启动企业数字人.bat
echo 2. 或直接运行: python start-server.py
echo 3. 访问: http://localhost:8000/index-complete-customer-service.html
echo.
echo 💡 如果仍有问题，可以:
echo    - 使用安全模式: index-safe-mode.html
echo    - 查看修复指南: 快速修复指南.md
echo.
goto :end

:download_suggestion
echo.
echo 💡 建议解决方案:
echo.
echo 方案1: 在线模型 (立即可用)
echo   修改HTML文件中的modelPath为:
echo   'https://threejs.org/examples/models/gltf/Xbot.glb'
echo.
echo 方案2: 手动下载
echo   访问: https://github.com/KhronosGroup/glTF-Sample-Models
echo   下载任一GLB文件到models目录
echo.
echo 方案3: 安装Python
echo   下载Python后运行下载脚本
echo.

:end
echo ✅ 修复工具执行完成
pause