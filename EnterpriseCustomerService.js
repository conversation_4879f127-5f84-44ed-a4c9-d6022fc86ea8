/**
 * 企业客服对话逻辑系统
 * 专业的AI客服响应与业务逻辑处理
 */

class EnterpriseCustomerService {
    constructor(digitalHuman) {
        this.digitalHuman = digitalHuman;
        this.knowledgeBase = new CustomerServiceKnowledgeBase();
        this.conversationEngine = new ConversationEngine();
        this.emotionAnalyzer = new CustomerEmotionAnalyzer();
        this.responseGenerator = new ResponseGenerator();
        this.ticketSystem = new TicketSystem();
        
        // 客服状态
        this.currentCustomer = null;
        this.conversationHistory = [];
        this.currentIssue = null;
        this.serviceMetrics = {
            totalConversations: 0,
            avgSatisfactionScore: 0,
            responseTime: 0
        };
        
        this.init();
    }
    
    init() {
        this.setupGreetingProtocol();
        this.initializeKnowledgeBase();
        this.setupEventListeners();
        console.log('✅ 企业客服系统初始化完成');
    }
    
    /**
     * 处理客户输入
     */
    async processCustomerInput(input, context = {}) {
        const startTime = performance.now();
        
        try {
            // 1. 分析客户情绪
            const emotionAnalysis = await this.emotionAnalyzer.analyze(input);
            
            // 2. 意图识别
            const intent = await this.conversationEngine.identifyIntent(input);
            
            // 3. 实体提取
            const entities = await this.conversationEngine.extractEntities(input);
            
            // 4. 查询知识库
            const knowledgeResponse = await this.knowledgeBase.query(intent, entities);
            
            // 5. 生成回复
            const response = await this.responseGenerator.generate({
                input,
                intent,
                entities,
                emotion: emotionAnalysis,
                knowledge: knowledgeResponse,
                context: this.getConversationContext()
            });
            
            // 6. 更新对话历史
            this.updateConversationHistory(input, response, intent, emotionAnalysis);
            
            // 7. 设置数字人表情和动画
            await this.setDigitalHumanResponse(response, emotionAnalysis);
            
            // 8. 语音播放
            await this.speakResponse(response.text);
            
            // 9. 更新服务指标
            this.updateServiceMetrics(startTime, response);
            
            return response;
            
        } catch (error) {
            console.error('客服处理错误:', error);
            return this.generateErrorResponse(error);
        }
    }
    
    /**
     * 设置问候协议
     */
    setupGreetingProtocol() {
        this.greetingTemplates = {
            initial: [
                "您好！欢迎来到我们公司，我是您的专属AI客服助手。请问我可以为您做些什么？",
                "欢迎！很高兴为您服务，我会尽我所能帮助解决您的问题。",
                "您好！我是智能客服，专门为您提供专业的服务支持。请描述您需要帮助的事项。"
            ],
            returning: [
                "欢迎回来！我记得您上次咨询的问题，今天需要什么帮助吗？",
                "很高兴再次为您服务！有什么新的问题需要处理吗？"
            ],
            timeBasedGreeting: {
                morning: "早上好！希望您今天有个美好的开始，我可以为您做什么？",
                afternoon: "下午好！很高兴为您服务，请问有什么可以帮助您的？",
                evening: "晚上好！感谢您选择我们的服务，我来为您解答疑问。"
            }
        };
    }
    
    /**
     * 初始化知识库
     */
    initializeKnowledgeBase() {
        this.knowledgeBase.loadCategories([
            'product_info',      // 产品信息
            'pricing',           // 价格政策
            'technical_support', // 技术支持
            'account_management',// 账户管理
            'billing',           // 账单问题
            'shipping',          // 物流配送
            'returns',           // 退换货
            'complaints',        // 投诉处理
            'general_inquiry'    // 一般咨询
        ]);
    }
    
    /**
     * 设置数字人响应
     */
    async setDigitalHumanResponse(response, emotionAnalysis) {
        if (!this.digitalHuman) return;
        
        // 根据回复类型设置表情
        const expressionMapping = {
            greeting: 'service_smile',
            helpful: 'empathetic_listening',
            explaining: 'helpful_explaining',
            apologetic: 'apologetic',
            positive: 'warm_welcome',
            neutral: 'professional_serious',
            problem_solving: 'encouraging'
        };
        
        const expression = expressionMapping[response.type] || 'professional_smile';
        
        // 设置表情
        if (this.digitalHuman.setEmotion) {
            this.digitalHuman.setEmotion(expression, 0.7, {
                duration: 1000,
                context: 'customer_service'
            });
        }
        
        // 根据回复内容设置动画
        if (response.requiresGesture) {
            await this.setAppropriateGesture(response.gestureType);
        }
        
        // 如果是解释类回复，使用展示动画
        if (response.type === 'explaining') {
            this.digitalHuman.playAnimation('presentation');
        }
    }
    
    /**
     * 设置合适的手势
     */
    async setAppropriateGesture(gestureType) {
        const gestureMap = {
            pointing: 'point_right',
            welcoming: 'wave_right',
            explaining: 'hold_object',
            confirming: 'thumbs_up',
            apologizing: 'wave_left'
        };
        
        const gesture = gestureMap[gestureType];
        if (gesture && this.digitalHuman.playHandGesture) {
            this.digitalHuman.playHandGesture(gesture);
        }
    }
    
    /**
     * 语音播放
     */
    async speakResponse(text) {
        if (this.digitalHuman && this.digitalHuman.speak) {
            await this.digitalHuman.speak(text, {
                rate: 0.9,  // 稍慢的语速，更专业
                pitch: 1.0,
                context: 'customer_service'
            });
        }
    }
    
    /**
     * 更新对话历史
     */
    updateConversationHistory(input, response, intent, emotion) {
        this.conversationHistory.push({
            timestamp: new Date(),
            customer_input: input,
            ai_response: response.text,
            intent: intent,
            emotion: emotion,
            satisfaction_score: null // 待客户评价
        });
        
        // 保持历史记录在合理范围内
        if (this.conversationHistory.length > 50) {
            this.conversationHistory = this.conversationHistory.slice(-30);
        }
    }
    
    /**
     * 获取对话上下文
     */
    getConversationContext() {
        return {
            historyLength: this.conversationHistory.length,
            recentTopics: this.getRecentTopics(),
            customerMood: this.getCurrentCustomerMood(),
            currentIssue: this.currentIssue,
            conversationPhase: this.determineConversationPhase()
        };
    }
    
    /**
     * 获取最近话题
     */
    getRecentTopics() {
        return this.conversationHistory
            .slice(-5)
            .map(item => item.intent)
            .filter((intent, index, self) => self.indexOf(intent) === index);
    }
    
    /**
     * 获取当前客户情绪
     */
    getCurrentCustomerMood() {
        const recentEmotions = this.conversationHistory
            .slice(-3)
            .map(item => item.emotion);
        
        if (recentEmotions.length === 0) return 'neutral';
        
        // 计算平均情绪
        const avgValence = recentEmotions.reduce((sum, emotion) => 
            sum + (emotion.valence || 0), 0) / recentEmotions.length;
        
        if (avgValence > 0.3) return 'positive';
        if (avgValence < -0.3) return 'negative';
        return 'neutral';
    }
    
    /**
     * 确定对话阶段
     */
    determineConversationPhase() {
        const length = this.conversationHistory.length;
        if (length === 0) return 'greeting';
        if (length <= 2) return 'problem_identification';
        if (this.currentIssue && !this.currentIssue.resolved) return 'problem_solving';
        return 'closing';
    }
    
    /**
     * 生成错误回复
     */
    generateErrorResponse(error) {
        return {
            text: "抱歉，我刚才遇到了一些技术问题。请您稍等片刻，或者重新描述一下您的问题，我会尽力帮助您。",
            type: 'apologetic',
            requiresGesture: true,
            gestureType: 'apologizing',
            confidence: 0.5
        };
    }
    
    /**
     * 更新服务指标
     */
    updateServiceMetrics(startTime, response) {
        const responseTime = performance.now() - startTime;
        this.serviceMetrics.responseTime = 
            (this.serviceMetrics.responseTime * 0.9) + (responseTime * 0.1);
        this.serviceMetrics.totalConversations++;
    }
    
    /**
     * 开始新对话
     */
    startNewConversation(customerInfo = {}) {
        this.currentCustomer = customerInfo;
        this.conversationHistory = [];
        this.currentIssue = null;
        
        // 选择合适的问候语
        const greeting = this.selectGreeting(customerInfo);
        
        // 设置欢迎表情和动画
        this.setDigitalHumanResponse({
            type: 'greeting',
            requiresGesture: true,
            gestureType: 'welcoming'
        }, { valence: 0.5 });
        
        // 播放问候语
        this.speakResponse(greeting);
        
        return greeting;
    }
    
    /**
     * 选择合适的问候语
     */
    selectGreeting(customerInfo) {
        const hour = new Date().getHours();
        let timeGreeting = '';
        
        if (hour < 12) timeGreeting = this.greetingTemplates.timeBasedGreeting.morning;
        else if (hour < 18) timeGreeting = this.greetingTemplates.timeBasedGreeting.afternoon;
        else timeGreeting = this.greetingTemplates.timeBasedGreeting.evening;
        
        // 如果是回头客
        if (customerInfo.isReturning) {
            return this.greetingTemplates.returning[
                Math.floor(Math.random() * this.greetingTemplates.returning.length)
            ];
        }
        
        return timeGreeting;
    }
    
    /**
     * 结束对话
     */
    endConversation(satisfactionScore = null) {
        if (satisfactionScore) {
            this.serviceMetrics.avgSatisfactionScore = 
                (this.serviceMetrics.avgSatisfactionScore * 0.9) + (satisfactionScore * 0.1);
        }
        
        const closingMessage = this.generateClosingMessage();
        
        // 设置告别表情和动画
        this.setDigitalHumanResponse({
            type: 'positive',
            requiresGesture: true,
            gestureType: 'welcoming'
        }, { valence: 0.7 });
        
        this.speakResponse(closingMessage);
        
        // 重置状态
        this.currentCustomer = null;
        this.currentIssue = null;
        
        return closingMessage;
    }
    
    /**
     * 生成结束语
     */
    generateClosingMessage() {
        const closingTemplates = [
            "感谢您选择我们的服务！如果还有其他问题，随时欢迎您再次咨询。祝您生活愉快！",
            "很高兴能为您服务！希望我的回答对您有所帮助。期待下次为您服务的机会！",
            "谢谢您的耐心！如果后续有任何疑问，请随时联系我们。祝您工作顺利！"
        ];
        
        return closingTemplates[Math.floor(Math.random() * closingTemplates.length)];
    }
    
    /**
     * 获取服务报告
     */
    getServiceReport() {
        return {
            metrics: this.serviceMetrics,
            currentConversation: {
                customer: this.currentCustomer,
                historyLength: this.conversationHistory.length,
                currentIssue: this.currentIssue,
                mood: this.getCurrentCustomerMood()
            },
            recentTopics: this.getRecentTopics()
        };
    }
    
    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听数字人事件
        if (this.digitalHuman && this.digitalHuman.on) {
            this.digitalHuman.on('speakStart', (data) => {
                console.log('开始语音播放:', data.text);
            });
            
            this.digitalHuman.on('speakEnd', (data) => {
                console.log('语音播放结束');
            });
        }
    }
}

/**
 * 客服知识库
 */
class CustomerServiceKnowledgeBase {
    constructor() {
        this.categories = new Map();
        this.faqDatabase = new Map();
        this.productInfo = new Map();
        this.policies = new Map();
        
        this.initializeDatabase();
    }
    
    initializeDatabase() {
        // 常见问题数据库
        this.faqDatabase.set('product_info', [
            {
                question: ['产品功能', '产品介绍', '产品特点'],
                answer: '我们的产品具有先进的AI技术，提供智能化的解决方案，可以大幅提升工作效率。',
                confidence: 0.9
            },
            {
                question: ['价格', '收费', '费用'],
                answer: '我们提供多种灵活的价格方案，包括基础版、专业版和企业版。具体价格请告诉我您的具体需求，我会为您推荐最合适的方案。',
                confidence: 0.8
            }
        ]);
        
        this.faqDatabase.set('technical_support', [
            {
                question: ['无法登录', '登录问题', '账户问题'],
                answer: '请确认您输入的用户名和密码是否正确。如果忘记密码，可以通过邮箱重置。如果问题仍然存在，我会为您转接技术支持。',
                confidence: 0.9
            },
            {
                question: ['系统故障', 'bug', '错误'],
                answer: '非常抱歉给您带来不便。请详细描述您遇到的问题，包括具体的错误信息，我会立即为您处理。',
                confidence: 0.8
            }
        ]);
    }
    
    loadCategories(categories) {
        categories.forEach(category => {
            this.categories.set(category, {
                name: category,
                loaded: true,
                lastUpdated: new Date()
            });
        });
    }
    
    async query(intent, entities) {
        const category = this.matchCategory(intent);
        const faqs = this.faqDatabase.get(category) || [];
        
        // 查找最匹配的答案
        let bestMatch = null;
        let maxScore = 0;
        
        faqs.forEach(faq => {
            const score = this.calculateMatchScore(entities, faq.question);
            if (score > maxScore) {
                maxScore = score;
                bestMatch = faq;
            }
        });
        
        return {
            answer: bestMatch ? bestMatch.answer : this.generateGenericResponse(),
            confidence: bestMatch ? bestMatch.confidence * maxScore : 0.3,
            category: category,
            source: 'knowledge_base'
        };
    }
    
    matchCategory(intent) {
        const categoryMap = {
            'product_inquiry': 'product_info',
            'price_inquiry': 'product_info',
            'login_issue': 'technical_support',
            'bug_report': 'technical_support',
            'account_management': 'account_management',
            'billing_question': 'billing',
            'shipping_inquiry': 'shipping',
            'return_request': 'returns',
            'complaint': 'complaints'
        };
        
        return categoryMap[intent] || 'general_inquiry';
    }
    
    calculateMatchScore(entities, questionKeywords) {
        if (!entities || entities.length === 0) return 0.1;
        
        let matches = 0;
        entities.forEach(entity => {
            questionKeywords.forEach(keyword => {
                if (entity.toLowerCase().includes(keyword) || 
                    keyword.includes(entity.toLowerCase())) {
                    matches++;
                }
            });
        });
        
        return Math.min(matches / Math.max(entities.length, questionKeywords.length), 1.0);
    }
    
    generateGenericResponse() {
        const responses = [
            '我理解您的问题。让我为您查找相关信息，请稍等片刻。',
            '这是一个很好的问题。请您详细描述一下具体情况，我会尽力帮助您。',
            '感谢您的咨询。为了更好地帮助您，可以告诉我更多细节吗？'
        ];
        
        return responses[Math.floor(Math.random() * responses.length)];
    }
}

/**
 * 对话引擎
 */
class ConversationEngine {
    constructor() {
        this.intentClassifier = new IntentClassifier();
        this.entityExtractor = new EntityExtractor();
    }
    
    async identifyIntent(input) {
        return this.intentClassifier.classify(input);
    }
    
    async extractEntities(input) {
        return this.entityExtractor.extract(input);
    }
}

/**
 * 意图分类器
 */
class IntentClassifier {
    classify(input) {
        const intentPatterns = {
            'greeting': ['你好', '您好', '欢迎', 'hello', 'hi'],
            'product_inquiry': ['产品', '功能', '特点', '介绍'],
            'price_inquiry': ['价格', '费用', '收费', '多少钱'],
            'login_issue': ['登录', '无法登录', '账户', '密码'],
            'bug_report': ['bug', '错误', '故障', '问题'],
            'complaint': ['投诉', '不满', '差评', '问题'],
            'goodbye': ['再见', '谢谢', '结束', 'bye']
        };
        
        let bestIntent = 'general_inquiry';
        let maxScore = 0;
        
        Object.entries(intentPatterns).forEach(([intent, keywords]) => {
            const score = keywords.reduce((sum, keyword) => {
                return sum + (input.includes(keyword) ? 1 : 0);
            }, 0);
            
            if (score > maxScore) {
                maxScore = score;
                bestIntent = intent;
            }
        });
        
        return bestIntent;
    }
}

/**
 * 实体提取器
 */
class EntityExtractor {
    extract(input) {
        const entities = [];
        
        // 简单的实体提取逻辑
        const productEntities = ['产品', '软件', '系统', '服务'];
        const timeEntities = ['今天', '昨天', '明天', '这周', '下周'];
        const numberEntities = input.match(/\d+/g) || [];
        
        productEntities.forEach(entity => {
            if (input.includes(entity)) {
                entities.push({ type: 'product', value: entity });
            }
        });
        
        timeEntities.forEach(entity => {
            if (input.includes(entity)) {
                entities.push({ type: 'time', value: entity });
            }
        });
        
        numberEntities.forEach(entity => {
            entities.push({ type: 'number', value: entity });
        });
        
        return entities;
    }
}

/**
 * 客户情绪分析器
 */
class CustomerEmotionAnalyzer {
    async analyze(input) {
        const emotionKeywords = {
            positive: ['满意', '很好', '不错', '感谢', '谢谢', '开心'],
            negative: ['不满', '差', '糟糕', '问题', '故障', '愤怒'],
            neutral: ['了解', '知道', '可以', '需要', '想要']
        };
        
        let emotion = 'neutral';
        let intensity = 0.5;
        let valence = 0.0;
        
        Object.entries(emotionKeywords).forEach(([emotionType, keywords]) => {
            const matchCount = keywords.reduce((count, keyword) => {
                return count + (input.includes(keyword) ? 1 : 0);
            }, 0);
            
            if (matchCount > 0) {
                emotion = emotionType;
                intensity = Math.min(matchCount * 0.3 + 0.5, 1.0);
                
                if (emotionType === 'positive') valence = 0.7;
                else if (emotionType === 'negative') valence = -0.7;
            }
        });
        
        return {
            emotion,
            intensity,
            valence,
            confidence: intensity > 0.5 ? 0.8 : 0.4
        };
    }
}

/**
 * 回复生成器
 */
class ResponseGenerator {
    async generate(context) {
        const { input, intent, entities, emotion, knowledge, context: conversationContext } = context;
        
        let responseText = knowledge.answer;
        let responseType = this.determineResponseType(intent, emotion);
        
        // 根据客户情绪调整回复
        if (emotion.emotion === 'negative') {
            responseText = this.addEmpathy(responseText);
            responseType = 'apologetic';
        }
        
        // 添加个性化元素
        responseText = this.personalize(responseText, conversationContext);
        
        return {
            text: responseText,
            type: responseType,
            confidence: knowledge.confidence,
            requiresGesture: this.shouldIncludeGesture(responseType),
            gestureType: this.getGestureType(responseType)
        };
    }
    
    determineResponseType(intent, emotion) {
        if (intent === 'greeting') return 'greeting';
        if (intent === 'complaint') return 'apologetic';
        if (emotion.emotion === 'positive') return 'positive';
        if (emotion.emotion === 'negative') return 'empathetic';
        return 'helpful';
    }
    
    addEmpathy(response) {
        const empathyPhrases = [
            '我理解您的困扰，',
            '我能感受到您的不便，',
            '非常抱歉给您带来麻烦，'
        ];
        
        const phrase = empathyPhrases[Math.floor(Math.random() * empathyPhrases.length)];
        return phrase + response;
    }
    
    personalize(response, context) {
        // 根据对话历史添加个性化元素
        if (context.historyLength > 5) {
            response = '基于我们之前的交流，' + response;
        }
        
        return response;
    }
    
    shouldIncludeGesture(responseType) {
        return ['greeting', 'apologetic', 'explaining'].includes(responseType);
    }
    
    getGestureType(responseType) {
        const gestureMap = {
            'greeting': 'welcoming',
            'apologetic': 'apologizing',
            'explaining': 'pointing',
            'helpful': 'explaining'
        };
        
        return gestureMap[responseType] || 'confirming';
    }
}

/**
 * 工单系统
 */
class TicketSystem {
    constructor() {
        this.tickets = new Map();
        this.ticketIdCounter = 1000;
    }
    
    createTicket(customerInfo, issue, priority = 'medium') {
        const ticketId = this.ticketIdCounter++;
        const ticket = {
            id: ticketId,
            customer: customerInfo,
            issue: issue,
            priority: priority,
            status: 'open',
            createdAt: new Date(),
            assignedTo: null,
            resolution: null
        };
        
        this.tickets.set(ticketId, ticket);
        return ticket;
    }
    
    updateTicket(ticketId, updates) {
        const ticket = this.tickets.get(ticketId);
        if (ticket) {
            Object.assign(ticket, updates);
            return ticket;
        }
        return null;
    }
    
    getTicket(ticketId) {
        return this.tickets.get(ticketId);
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EnterpriseCustomerService;
} else {
    window.EnterpriseCustomerService = EnterpriseCustomerService;
}