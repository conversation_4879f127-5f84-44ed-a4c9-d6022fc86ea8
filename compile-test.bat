@echo off
chcp 65001 >nul
echo Testing compilation...

echo Current directory:
cd

echo Files in directory:
dir *.c *.exe

echo Testing simple compilation:
echo int main(){return 0;} > simple.c
gcc simple.c -o simple.exe
if exist simple.exe (
    echo Simple compilation works
    del simple.exe simple.c
) else (
    echo Simple compilation failed
)

echo Testing server.c compilation:
gcc -c server.c -o server.o 2>compile.log
if exist server.o (
    echo Object file created successfully
    del server.o
) else (
    echo Object file creation failed
    echo Errors:
    type compile.log 2>nul
)

echo Testing full compilation with libraries:
gcc -Wall -Wextra -std=c99 -O2 -g -D_WIN32_WINNT=0x0600 -o digital_human_server.exe server.c -lws2_32 -lwebsockets -ljson-c 2>full_compile.log
if exist digital_human_server.exe (
    echo Full compilation successful!
    dir digital_human_server.exe
) else (
    echo Full compilation failed
    echo Errors:
    type full_compile.log 2>nul
)

pause