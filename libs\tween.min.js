/**
 * Tween.js - 简化版动画库
 * 为3D数字人系统提供基本动画功能
 */
var TWEEN = {
    _tweens: [],
    
    Tween: function(object) {
        this._object = object;
        this._startTime = 0;
        this._duration = 1000;
        this._valuesStart = {};
        this._valuesEnd = {};
        this._easingFunction = TWEEN.Easing.Linear.None;
        this._onUpdateCallback = null;
        this._onCompleteCallback = null;
        this._onStartCallback = null;
        this._started = false;
        this._finished = false;
        
        this.to = function(properties, duration) {
            this._valuesEnd = properties;
            if (duration !== undefined) {
                this._duration = duration;
            }
            return this;
        };
        
        this.start = function(time) {
            TWEEN._tweens.push(this);
            
            this._startTime = time !== undefined ? time : Date.now();
            this._finished = false;
            this._started = true;
            
            for (var property in this._valuesEnd) {
                if (this._object[property] === undefined) {
                    continue;
                }
                this._valuesStart[property] = this._object[property];
            }
            
            if (this._onStartCallback !== null) {
                this._onStartCallback.call(this._object);
            }
            
            return this;
        };
        
        this.stop = function() {
            var index = TWEEN._tweens.indexOf(this);
            if (index !== -1) {
                TWEEN._tweens.splice(index, 1);
            }
            return this;
        };
        
        this.easing = function(easing) {
            this._easingFunction = easing;
            return this;
        };
        
        this.onStart = function(callback) {
            this._onStartCallback = callback;
            return this;
        };
        
        this.onUpdate = function(callback) {
            this._onUpdateCallback = callback;
            return this;
        };
        
        this.onComplete = function(callback) {
            this._onCompleteCallback = callback;
            return this;
        };
        
        this.update = function(time) {
            if (this._finished) return false;
            
            var elapsed = (time - this._startTime) / this._duration;
            elapsed = elapsed > 1 ? 1 : elapsed;
            
            var value = this._easingFunction(elapsed);
            
            for (var property in this._valuesEnd) {
                var start = this._valuesStart[property];
                var end = this._valuesEnd[property];
                
                if (start === undefined) continue;
                
                this._object[property] = start + (end - start) * value;
            }
            
            if (this._onUpdateCallback !== null) {
                this._onUpdateCallback.call(this._object);
            }
            
            if (elapsed === 1) {
                this._finished = true;
                
                if (this._onCompleteCallback !== null) {
                    this._onCompleteCallback.call(this._object);
                }
                
                return false;
            }
            
            return true;
        };
        
        return this;
    },
    
    Easing: {
        Linear: {
            None: function(k) {
                return k;
            }
        },
        
        Quadratic: {
            In: function(k) {
                return k * k;
            },
            Out: function(k) {
                return k * (2 - k);
            },
            InOut: function(k) {
                if ((k *= 2) < 1) return 0.5 * k * k;
                return -0.5 * (--k * (k - 2) - 1);
            }
        },
        
        Cubic: {
            In: function(k) {
                return k * k * k;
            },
            Out: function(k) {
                return --k * k * k + 1;
            },
            InOut: function(k) {
                if ((k *= 2) < 1) return 0.5 * k * k * k;
                return 0.5 * ((k -= 2) * k * k + 2);
            }
        },
        
        Elastic: {
            In: function(k) {
                var s, a = 0.1, p = 0.4;
                if (k === 0) return 0;
                if (k === 1) return 1;
                if (!a || a < 1) { a = 1; s = p / 4; }
                else s = p * Math.asin(1 / a) / (2 * Math.PI);
                return -(a * Math.pow(2, 10 * (k -= 1)) * Math.sin((k - s) * (2 * Math.PI) / p));
            },
            Out: function(k) {
                var s, a = 0.1, p = 0.4;
                if (k === 0) return 0;
                if (k === 1) return 1;
                if (!a || a < 1) { a = 1; s = p / 4; }
                else s = p * Math.asin(1 / a) / (2 * Math.PI);
                return (a * Math.pow(2, -10 * k) * Math.sin((k - s) * (2 * Math.PI) / p) + 1);
            }
        }
    },
    
    update: function(time) {
        if (TWEEN._tweens.length === 0) return false;
        
        var i = 0;
        var numTweens = TWEEN._tweens.length;
        var time = time !== undefined ? time : Date.now();
        
        while (i < numTweens) {
            if (TWEEN._tweens[i].update(time)) {
                i++;
            } else {
                TWEEN._tweens.splice(i, 1);
                numTweens--;
            }
        }
        
        return true;
    },
    
    removeAll: function() {
        TWEEN._tweens = [];
    }
};

// 创建Tween构造函数
TWEEN.Tween = function(object) {
    return new TWEEN.Tween(object);
};

// 兼容性支持
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TWEEN;
} else if (typeof define === 'function' && define.amd) {
    define([], function() {
        return TWEEN;
    });
} else {
    window.TWEEN = TWEEN;
}