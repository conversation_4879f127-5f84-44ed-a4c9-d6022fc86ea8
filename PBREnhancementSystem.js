/**
 * 企业级PBR渲染增强系统
 * 为3D数字人提供影视级别的视觉质量
 * 包含：PBR材质、IBL光照、后处理、性能优化
 */
class PBREnhancementSystem {
    constructor(digitalHuman, options = {}) {
        this.digitalHuman = digitalHuman;
        this.scene = digitalHuman.scene;
        this.renderer = digitalHuman.renderer;
        this.camera = digitalHuman.camera;
        this.container = digitalHuman.container;
        
        // 配置选项
        this.options = {
            // PBR质量级别 (low|medium|high|ultra)
            qualityLevel: options.qualityLevel || 'high',
            
            // 开启IBL环境光照
            enableIBL: options.enableIBL !== false,
            
            // 开启实时阴影
            enableShadows: options.enableShadows !== false,
            
            // 开启后处理效果
            enablePostProcessing: options.enablePostProcessing !== false,
            
            // 性能自适应
            enableAdaptiveQuality: options.enableAdaptiveQuality !== false,
            
            // 调试模式
            debug: options.debug || false,
            
            ...options
        };
        
        // 核心组件
        this.environmentMap = null;
        this.composer = null;
        this.pbrMaterials = new Map();
        this.lights = new Map();
        this.postProcessingPasses = new Map();
        
        // 性能监控
        this.performanceMonitor = {
            fps: 60,
            frameTime: 16.67,
            adaptiveQuality: this.options.qualityLevel
        };
        
        // 材质配置库
        this.materialPresets = {
            skin: {
                roughness: 0.8,
                metalness: 0.0,
                subsurface: 0.3,
                thickness: 1.0,
                transmission: 0.0,
                clearcoat: 0.0
            },
            clothing_fabric: {
                roughness: 0.9,
                metalness: 0.0,
                subsurface: 0.1,
                thickness: 0.5,
                transmission: 0.0,
                clearcoat: 0.0
            },
            clothing_leather: {
                roughness: 0.6,
                metalness: 0.0,
                subsurface: 0.0,
                thickness: 0.0,
                transmission: 0.0,
                clearcoat: 0.3
            },
            hair: {
                roughness: 0.7,
                metalness: 0.0,
                subsurface: 0.5,
                thickness: 0.8,
                transmission: 0.2,
                clearcoat: 0.1
            },
            eyes: {
                roughness: 0.1,
                metalness: 0.0,
                subsurface: 0.8,
                thickness: 1.0,
                transmission: 0.5,
                clearcoat: 0.9
            },
            jewelry_metal: {
                roughness: 0.1,
                metalness: 1.0,
                subsurface: 0.0,
                thickness: 0.0,
                transmission: 0.0,
                clearcoat: 0.0
            }
        };
        
        this.init();
    }
    
    /**
     * 初始化PBR增强系统
     */
    async init() {
        console.log('🎨 初始化PBR增强系统...');
        
        try {
            // 1. 升级渲染器配置
            this.upgradeRenderer();
            
            // 2. 创建环境光照系统
            await this.setupEnvironmentLighting();
            
            // 3. 初始化后处理管道
            this.setupPostProcessing();
            
            // 4. 配置阴影系统
            this.setupShadowSystem();
            
            // 5. 启动性能监控
            this.startPerformanceMonitoring();
            
            console.log('✅ PBR增强系统初始化完成');
            
        } catch (error) {
            console.error('❌ PBR系统初始化失败:', error);
            throw error;
        }
    }
    
    /**
     * 升级渲染器配置
     */
    upgradeRenderer() {
        console.log('🔧 升级渲染器配置...');
        
        // 启用高质量渲染选项
        this.renderer.shadowMap.enabled = this.options.enableShadows;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap; // 软阴影
        
        // 色彩空间配置
        this.renderer.outputEncoding = THREE.sRGBEncoding;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
        
        // 物理正确光照
        this.renderer.physicallyCorrectLights = true;
        
        // 提高像素比（在支持的设备上）
        if (this.options.qualityLevel === 'ultra') {
            this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        }
        
        console.log('✅ 渲染器配置升级完成');
    }
    
    /**
     * 设置环境光照系统 (IBL)
     */
    async setupEnvironmentLighting() {
        console.log('💡 设置IBL环境光照...');
        
        if (!this.options.enableIBL) {
            console.log('⚠️ IBL已禁用，使用传统光照');
            return;
        }
        
        try {
            // 创建HDR环境贴图
            await this.loadEnvironmentMap();
            
            // 配置主光源
            this.setupKeyLighting();
            
            // 配置补光系统
            this.setupFillLighting();
            
            // 配置轮廓光
            this.setupRimLighting();
            
            console.log('✅ 环境光照设置完成');
            
        } catch (error) {
            console.warn('⚠️ IBL加载失败，使用备用光照:', error.message);
            this.setupFallbackLighting();
        }
    }
    
    /**
     * 加载环境贴图
     */
    async loadEnvironmentMap() {
        return new Promise((resolve, reject) => {
            // 创建HDR立方体贴图（模拟企业办公环境）
            const hdrUrls = this.generateHDREnvironment();
            
            const cubeTextureLoader = new THREE.CubeTextureLoader();
            
            cubeTextureLoader.load(
                hdrUrls,
                (envMap) => {
                    envMap.encoding = THREE.sRGBEncoding;
                    
                    // 设置场景环境
                    this.scene.environment = envMap;
                    this.scene.background = envMap.clone();
                    this.scene.background.encoding = THREE.sRGBEncoding;
                    
                    // 降低背景强度
                    if (this.scene.background) {
                        this.scene.backgroundIntensity = 0.3;
                    }
                    
                    this.environmentMap = envMap;
                    console.log('✅ 环境贴图加载成功');
                    resolve(envMap);
                },
                (progress) => {
                    console.log('HDR环境贴图加载进度:', Math.round((progress.loaded / progress.total) * 100) + '%');
                },
                (error) => {
                    console.error('HDR环境贴图加载失败:', error);
                    reject(error);
                }
            );
        });
    }
    
    /**
     * 生成程序化HDR环境
     */
    generateHDREnvironment() {
        // 创建程序化环境贴图（企业办公室风格）
        const canvas = document.createElement('canvas');
        canvas.width = 512;
        canvas.height = 512;
        const ctx = canvas.getContext('2d');
        
        // 绘制企业级环境（天空盒的6个面）
        const faces = ['px', 'nx', 'py', 'ny', 'pz', 'nz'];
        const urls = [];
        
        faces.forEach((face, index) => {
            // 清空画布
            ctx.fillStyle = this.getEnvironmentColor(face);
            ctx.fillRect(0, 0, 512, 512);
            
            // 添加光照效果
            this.addEnvironmentLighting(ctx, face);
            
            // 转换为Data URL
            const dataUrl = canvas.toDataURL('image/png');
            urls.push(dataUrl);
        });
        
        return urls;
    }
    
    /**
     * 获取环境面颜色
     */
    getEnvironmentColor(face) {
        const colors = {
            'px': 'linear-gradient(to bottom, #87CEEB 0%, #E6F3FF 50%, #F0F8FF 100%)', // 右
            'nx': 'linear-gradient(to bottom, #87CEEB 0%, #E6F3FF 50%, #F0F8FF 100%)', // 左
            'py': 'linear-gradient(to bottom, #FFFFFF 0%, #F0F8FF 50%, #E6F3FF 100%)', // 上（天空）
            'ny': '#D3D3D3', // 下（地面）
            'pz': 'linear-gradient(to bottom, #87CEEB 0%, #E6F3FF 50%, #F0F8FF 100%)', // 前
            'nz': 'linear-gradient(to bottom, #87CEEB 0%, #E6F3FF 50%, #F0F8FF 100%)'  // 后
        };
        
        return colors[face] || '#F0F8FF';
    }
    
    /**
     * 添加环境光照效果
     */
    addEnvironmentLighting(ctx, face) {
        // 模拟办公室光照条件
        const gradient = ctx.createRadialGradient(256, 256, 0, 256, 256, 512);
        
        if (face === 'py') { // 天空面
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)');
            gradient.addColorStop(0.3, 'rgba(240, 248, 255, 0.6)');
            gradient.addColorStop(1, 'rgba(135, 206, 235, 0.3)');
        } else {
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.3)');
            gradient.addColorStop(0.5, 'rgba(240, 248, 255, 0.2)');
            gradient.addColorStop(1, 'rgba(211, 211, 211, 0.1)');
        }
        
        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, 512, 512);
    }
    
    /**
     * 设置主光源
     */
    setupKeyLighting() {
        // 移除旧的主光源
        const oldDirectionalLight = this.scene.children.find(child => 
            child.type === 'DirectionalLight' && child.userData.role === 'key'
        );
        if (oldDirectionalLight) {
            this.scene.remove(oldDirectionalLight);
        }
        
        // 创建主光源（模拟办公室顶灯）
        const keyLight = new THREE.DirectionalLight(0xFFFFFF, 2.5);
        keyLight.position.set(50, 200, 100);
        keyLight.userData.role = 'key';
        
        // 高质量阴影配置
        if (this.options.enableShadows) {
            keyLight.castShadow = true;
            keyLight.shadow.mapSize.width = 2048;
            keyLight.shadow.mapSize.height = 2048;
            keyLight.shadow.camera.near = 0.1;
            keyLight.shadow.camera.far = 500;
            keyLight.shadow.camera.left = -100;
            keyLight.shadow.camera.right = 100;
            keyLight.shadow.camera.top = 100;
            keyLight.shadow.camera.bottom = -100;
            keyLight.shadow.bias = -0.0001;
            keyLight.shadow.radius = 4;
        }
        
        this.scene.add(keyLight);
        this.lights.set('keyLight', keyLight);
        
        console.log('✅ 主光源配置完成');
    }
    
    /**
     * 设置补光系统
     */
    setupFillLighting() {
        // 柔和补光（模拟反射光）
        const fillLight = new THREE.DirectionalLight(0xE6F3FF, 1.0);
        fillLight.position.set(-50, 100, -50);
        fillLight.userData.role = 'fill';
        
        this.scene.add(fillLight);
        this.lights.set('fillLight', fillLight);
        
        // 环境光增强
        const ambientLight = new THREE.AmbientLight(0x404040, 0.8);
        this.scene.add(ambientLight);
        this.lights.set('ambientLight', ambientLight);
        
        console.log('✅ 补光系统配置完成');
    }
    
    /**
     * 设置轮廓光
     */
    setupRimLighting() {
        // 轮廓光（增强立体感）
        const rimLight = new THREE.DirectionalLight(0xB0E0E6, 1.5);
        rimLight.position.set(0, 50, -200);
        rimLight.userData.role = 'rim';
        
        this.scene.add(rimLight);
        this.lights.set('rimLight', rimLight);
        
        console.log('✅ 轮廓光配置完成');
    }
    
    /**
     * 备用光照（当IBL失败时）
     */
    setupFallbackLighting() {
        console.log('⚠️ 设置备用光照系统');
        
        // 清除现有光源
        this.clearLights();
        
        // 基础三点光照
        const keyLight = new THREE.DirectionalLight(0xFFFFFF, 1.5);
        keyLight.position.set(100, 200, 100);
        this.scene.add(keyLight);
        
        const fillLight = new THREE.DirectionalLight(0xFFFFFF, 0.5);
        fillLight.position.set(-50, 100, -50);
        this.scene.add(fillLight);
        
        const backLight = new THREE.DirectionalLight(0x88CCFF, 0.8);
        backLight.position.set(0, 50, -200);
        this.scene.add(backLight);
        
        const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
        this.scene.add(ambientLight);
    }
    
    /**
     * 设置后处理管道
     */
    setupPostProcessing() {
        if (!this.options.enablePostProcessing) {
            console.log('⚠️ 后处理已禁用');
            return;
        }
        
        console.log('🎬 设置后处理管道...');
        
        try {
            // 检查必要的后处理类
            if (typeof THREE.EffectComposer === 'undefined') {
                console.warn('⚠️ EffectComposer未加载，跳过后处理');
                return;
            }
            
            // 创建合成器
            this.composer = new THREE.EffectComposer(this.renderer);
            
            // 渲染通道
            const renderPass = new THREE.RenderPass(this.scene, this.camera);
            this.composer.addPass(renderPass);
            this.postProcessingPasses.set('render', renderPass);
            
            // SSAO（屏幕空间环境光遮蔽）
            if (typeof THREE.SSAOPass !== 'undefined') {
                const ssaoPass = new THREE.SSAOPass(this.scene, this.camera, 
                    this.container.clientWidth, this.container.clientHeight);
                ssaoPass.kernelRadius = 16;
                ssaoPass.minDistance = 0.005;
                ssaoPass.maxDistance = 0.1;
                ssaoPass.output = THREE.SSAOPass.OUTPUT.SSAO;
                
                this.composer.addPass(ssaoPass);
                this.postProcessingPasses.set('ssao', ssaoPass);
                console.log('✅ SSAO通道已添加');
            }
            
            // 抗锯齿（FXAA）
            if (typeof THREE.ShaderPass !== 'undefined' && THREE.FXAAShader) {
                const fxaaPass = new THREE.ShaderPass(THREE.FXAAShader);
                fxaaPass.material.uniforms['resolution'].value.x = 1 / this.container.clientWidth;
                fxaaPass.material.uniforms['resolution'].value.y = 1 / this.container.clientHeight;
                
                this.composer.addPass(fxaaPass);
                this.postProcessingPasses.set('fxaa', fxaaPass);
                console.log('✅ FXAA抗锯齿已添加');
            }
            
            // 色调映射增强
            if (typeof THREE.ShaderPass !== 'undefined' && THREE.GammaCorrectionShader) {
                const gammaCorrectionPass = new THREE.ShaderPass(THREE.GammaCorrectionShader);
                this.composer.addPass(gammaCorrectionPass);
                this.postProcessingPasses.set('gamma', gammaCorrectionPass);
                console.log('✅ 伽马校正已添加');
            }
            
            // 辉光效果（可选）
            if (this.options.qualityLevel === 'ultra' && typeof THREE.UnrealBloomPass !== 'undefined') {
                const bloomPass = new THREE.UnrealBloomPass(
                    new THREE.Vector2(this.container.clientWidth, this.container.clientHeight),
                    0.3, // strength
                    0.4, // radius
                    0.85 // threshold
                );
                
                this.composer.addPass(bloomPass);
                this.postProcessingPasses.set('bloom', bloomPass);
                console.log('✅ 辉光效果已添加');
            }
            
            console.log('✅ 后处理管道设置完成');
            
        } catch (error) {
            console.error('❌ 后处理设置失败:', error);
            this.composer = null;
        }
    }
    
    /**
     * 配置阴影系统
     */
    setupShadowSystem() {
        if (!this.options.enableShadows) {
            console.log('⚠️ 阴影系统已禁用');
            return;
        }
        
        console.log('🌫️ 配置高质量阴影系统...');
        
        // 全局阴影配置
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.shadowMap.autoUpdate = true;
        
        // 为所有网格启用阴影
        this.scene.traverse((child) => {
            if (child.isMesh) {
                child.castShadow = true;
                child.receiveShadow = true;
            }
        });
        
        console.log('✅ 阴影系统配置完成');
    }
    
    /**
     * 为模型应用PBR材质
     */
    enhanceModelMaterials(model) {
        if (!model) {
            console.warn('⚠️ 模型为空，无法应用PBR材质');
            return;
        }
        
        console.log('🎨 为模型应用PBR材质...');
        
        model.traverse((child) => {
            if (child.isMesh && child.material) {
                this.convertToPBRMaterial(child);
            }
        });
        
        console.log('✅ PBR材质应用完成');
    }
    
    /**
     * 将材质转换为PBR材质
     */
    convertToPBRMaterial(mesh) {
        const originalMaterial = mesh.material;
        
        // 创建PBR材质
        const pbrMaterial = new THREE.MeshPhysicalMaterial({
            // 基础属性
            color: originalMaterial.color || 0xFFFFFF,
            map: originalMaterial.map || null,
            
            // PBR属性
            roughness: 0.8,
            metalness: 0.0,
            
            // 高级属性
            clearcoat: 0.0,
            clearcoatRoughness: 0.0,
            transmission: 0.0,
            thickness: 1.0,
            
            // 次表面散射（皮肤效果）
            subsurface: 0.0,
            
            // 其他属性
            normalScale: new THREE.Vector2(1, 1),
            envMapIntensity: 1.0,
            side: originalMaterial.side || THREE.FrontSide,
            transparent: originalMaterial.transparent || false,
            opacity: originalMaterial.opacity || 1.0
        });
        
        // 根据材质名称或网格名称应用预设
        const materialType = this.detectMaterialType(mesh, originalMaterial);
        this.applyMaterialPreset(pbrMaterial, materialType);
        
        // 设置环境贴图
        if (this.environmentMap) {
            pbrMaterial.envMap = this.environmentMap;
        }
        
        // 复制纹理
        this.copyTextures(originalMaterial, pbrMaterial);
        
        // 应用材质
        mesh.material = pbrMaterial;
        
        // 缓存材质
        this.pbrMaterials.set(mesh.uuid, pbrMaterial);
        
        console.log(`✅ 转换材质: ${materialType} -> PBR`);
    }
    
    /**
     * 检测材质类型
     */
    detectMaterialType(mesh, material) {
        const meshName = mesh.name.toLowerCase();
        const materialName = material.name ? material.name.toLowerCase() : '';
        
        // 皮肤材质
        if (meshName.includes('face') || meshName.includes('head') || 
            meshName.includes('skin') || materialName.includes('skin')) {
            return 'skin';
        }
        
        // 头发材质
        if (meshName.includes('hair') || materialName.includes('hair')) {
            return 'hair';
        }
        
        // 眼睛材质
        if (meshName.includes('eye') || materialName.includes('eye')) {
            return 'eyes';
        }
        
        // 衣服材质
        if (meshName.includes('cloth') || meshName.includes('shirt') || 
            meshName.includes('dress') || materialName.includes('fabric')) {
            return 'clothing_fabric';
        }
        
        // 皮革材质
        if (meshName.includes('leather') || materialName.includes('leather')) {
            return 'clothing_leather';
        }
        
        // 金属/珠宝材质
        if (meshName.includes('metal') || meshName.includes('jewelry') || 
            materialName.includes('metal')) {
            return 'jewelry_metal';
        }
        
        // 默认为布料
        return 'clothing_fabric';
    }
    
    /**
     * 应用材质预设
     */
    applyMaterialPreset(material, presetName) {
        const preset = this.materialPresets[presetName];
        if (!preset) {
            console.warn(`⚠️ 未找到材质预设: ${presetName}`);
            return;
        }
        
        // 应用预设属性
        Object.keys(preset).forEach(key => {
            if (material[key] !== undefined) {
                material[key] = preset[key];
            }
        });
        
        console.log(`✅ 应用材质预设: ${presetName}`);
    }
    
    /**
     * 复制纹理
     */
    copyTextures(source, target) {
        // 基础纹理
        if (source.map) target.map = source.map;
        
        // 法线贴图
        if (source.normalMap) target.normalMap = source.normalMap;
        
        // 其他贴图
        if (source.emissiveMap) target.emissiveMap = source.emissiveMap;
        if (source.aoMap) target.aoMap = source.aoMap;
        if (source.roughnessMap) target.roughnessMap = source.roughnessMap;
        if (source.metalnessMap) target.metalnessMap = source.metalnessMap;
    }
    
    /**
     * 性能监控
     */
    startPerformanceMonitoring() {
        if (!this.options.enableAdaptiveQuality) return;
        
        let frameCount = 0;
        let lastTime = performance.now();
        
        const monitor = () => {
            frameCount++;
            const currentTime = performance.now();
            
            if (currentTime - lastTime >= 1000) {
                const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
                this.performanceMonitor.fps = fps;
                this.performanceMonitor.frameTime = 1000 / fps;
                
                // 自适应质量调整
                this.adjustQualityBasedOnPerformance(fps);
                
                frameCount = 0;
                lastTime = currentTime;
            }
            
            requestAnimationFrame(monitor);
        };
        
        monitor();
        console.log('📊 性能监控已启动');
    }
    
    /**
     * 基于性能调整质量
     */
    adjustQualityBasedOnPerformance(fps) {
        if (fps < 30 && this.performanceMonitor.adaptiveQuality !== 'low') {
            console.log('⚡ 性能不足，降低渲染质量');
            this.setQualityLevel('medium');
        } else if (fps > 55 && this.performanceMonitor.adaptiveQuality === 'medium') {
            console.log('⚡ 性能充足，提升渲染质量');
            this.setQualityLevel('high');
        }
    }
    
    /**
     * 设置质量级别
     */
    setQualityLevel(level) {
        this.performanceMonitor.adaptiveQuality = level;
        
        switch (level) {
            case 'low':
                this.renderer.setPixelRatio(1);
                if (this.composer) this.composer.enabled = false;
                break;
                
            case 'medium':
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5));
                if (this.composer) this.composer.enabled = true;
                break;
                
            case 'high':
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                if (this.composer) this.composer.enabled = true;
                break;
                
            case 'ultra':
                this.renderer.setPixelRatio(window.devicePixelRatio);
                if (this.composer) this.composer.enabled = true;
                break;
        }
        
        console.log(`🎚️ 质量级别设置为: ${level}`);
    }
    
    /**
     * 渲染（替换原始渲染）
     */
    render() {
        if (this.composer && this.options.enablePostProcessing) {
            this.composer.render();
        } else {
            this.renderer.render(this.scene, this.camera);
        }
    }
    
    /**
     * 窗口调整处理
     */
    onWindowResize() {
        if (this.composer) {
            this.composer.setSize(this.container.clientWidth, this.container.clientHeight);
            
            // 更新FXAA分辨率
            const fxaaPass = this.postProcessingPasses.get('fxaa');
            if (fxaaPass) {
                fxaaPass.material.uniforms['resolution'].value.x = 1 / this.container.clientWidth;
                fxaaPass.material.uniforms['resolution'].value.y = 1 / this.container.clientHeight;
            }
        }
    }
    
    /**
     * 清除光源
     */
    clearLights() {
        this.lights.forEach(light => {
            this.scene.remove(light);
        });
        this.lights.clear();
    }
    
    /**
     * 获取性能统计
     */
    getPerformanceStats() {
        return {
            fps: this.performanceMonitor.fps,
            frameTime: this.performanceMonitor.frameTime,
            qualityLevel: this.performanceMonitor.adaptiveQuality,
            materialsCount: this.pbrMaterials.size,
            lightsCount: this.lights.size,
            postProcessingEnabled: this.composer ? this.composer.enabled : false
        };
    }
    
    /**
     * 销毁系统
     */
    destroy() {
        // 清理后处理
        if (this.composer) {
            this.composer.dispose();
        }
        
        // 清理材质
        this.pbrMaterials.forEach(material => {
            material.dispose();
        });
        
        // 清理光源
        this.clearLights();
        
        // 清理环境贴图
        if (this.environmentMap) {
            this.environmentMap.dispose();
        }
        
        console.log('🧹 PBR增强系统已清理');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PBREnhancementSystem;
} else {
    window.PBREnhancementSystem = PBREnhancementSystem;
}