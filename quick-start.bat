@echo off
chcp 65001 >nul
:: Digital Human Quick Start Script
:: Automatically detects environment and starts appropriate services

echo ======================================
echo       3D Digital Human Quick Start
echo ======================================
echo.

:: Check environment
echo [Info] Detecting environment...

:: Check GCC
where gcc >nul 2>&1
set GCC_AVAILABLE=%errorlevel%

:: Check Python
where python >nul 2>&1
set PYTHON_AVAILABLE=%errorlevel%

:: Check backend executable
set BACKEND_EXE_AVAILABLE=1
if exist "digital_human_server.exe" set BACKEND_EXE_AVAILABLE=0

:: Display environment status
echo.
echo Environment Status:
if %GCC_AVAILABLE%==0 (
    echo   ✓ GCC Compiler: Available
) else (
    echo   ✗ GCC Compiler: Not found
)

if %PYTHON_AVAILABLE%==0 (
    echo   ✓ Python: Available
) else (
    echo   ✗ Python: Not found
)

if %BACKEND_EXE_AVAILABLE%==0 (
    echo   ✓ Backend Executable: Found
) else (
    echo   ✗ Backend Executable: Not found
)

echo.

:: Determine startup mode
if %PYTHON_AVAILABLE%==0 (
    if %GCC_AVAILABLE%==0 (
        echo [Mode] Full System Startup (Frontend + Backend compilation)
        echo Starting complete digital human system...
        start-all-fixed.bat
    ) else if %BACKEND_EXE_AVAILABLE%==0 (
        echo [Mode] Full System Startup (Frontend + Pre-compiled Backend)
        echo Starting with existing backend executable...
        start "Digital Human Backend" digital_human_server.exe 8080
        timeout /t 2 /nobreak >nul
        start-frontend-only.bat
    ) else (
        echo [Mode] Frontend Only (Backend compilation not available)
        echo Starting frontend-only mode...
        start-frontend-only.bat
    )
) else (
    echo [Error] Python is required but not found
    echo.
    echo Please install Python 3.x from: https://python.org
    echo Then run this script again.
    echo.
    echo Alternative: Install dependencies manually
    echo   GCC: run install-gcc.bat
    echo   Python: https://python.org/downloads/
    pause
    exit /b 1
)

exit /b 0