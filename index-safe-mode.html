<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业数字人 - 安全模式</title>
    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #3b82f6;
            --accent-color: #ef4444;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --background-color: #f8fafc;
            --text-primary: #1f2937;
            --border-radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: var(--text-primary);
            overflow: hidden;
            height: 100vh;
        }

        .header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 60px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            display: flex;
            align-items: center;
            padding: 0 20px;
            z-index: 1000;
            border-bottom: 1px solid #e5e7eb;
        }

        .logo {
            font-size: 20px;
            font-weight: bold;
            color: var(--primary-color);
        }

        .status {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .main-container {
            position: absolute;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
        }

        .control-panel {
            width: 300px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-right: 1px solid #e5e7eb;
            overflow-y: auto;
        }

        .digital-human-viewport {
            flex: 1;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        #digital-human-container {
            width: 100%;
            height: 100%;
        }

        .control-group {
            margin-bottom: 20px;
        }

        .control-label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .control-input, .control-select {
            width: 100%;
            padding: 10px;
            border: 1px solid #e5e7eb;
            border-radius: var(--border-radius);
            font-size: 14px;
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn-primary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-color);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 1000;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .notification {
            position: fixed;
            top: 80px;
            right: 20px;
            padding: 12px 20px;
            border-radius: var(--border-radius);
            color: white;
            font-weight: 600;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.error {
            background: var(--accent-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }

        .error-panel {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--accent-color);
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 15px;
        }

        .error-title {
            font-weight: bold;
            color: var(--accent-color);
            margin-bottom: 8px;
        }

        .error-message {
            font-size: 13px;
            line-height: 1.4;
        }

        .solution-list {
            list-style: none;
            padding-left: 0;
        }

        .solution-list li {
            padding: 5px 0;
            padding-left: 20px;
            position: relative;
        }

        .solution-list li:before {
            content: "💡";
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">🏢 企业数字人 - 安全模式</div>
        <div class="status">
            <div class="status-dot"></div>
            <span id="system-status">正在初始化...</span>
        </div>
    </div>

    <div class="main-container">
        <div class="control-panel">
            <div class="control-group">
                <label class="control-label">🎯 启动模式</label>
                <select class="control-select" id="startup-mode">
                    <option value="safe">安全模式（推荐）</option>
                    <option value="basic">基础模式</option>
                    <option value="full">完整模式</option>
                </select>
            </div>

            <div class="control-group">
                <label class="control-label">📁 模型选择</label>
                <select class="control-select" id="model-select">
                    <option value="./models/business_female_professional.glb">专业女性客服</option>
                    <option value="./models/business_female.glb">标准女性客服</option>
                    <option value="./models/business_male.glb">男性客服</option>
                    <option value="https://threejs.org/examples/models/gltf/Xbot.glb">在线备用模型</option>
                </select>
            </div>

            <div class="control-group">
                <button class="btn btn-primary" id="start-btn">🚀 启动系统</button>
                <button class="btn btn-success" id="test-btn">🧪 测试连接</button>
                <button class="btn btn-warning" id="clear-cache-btn">🧹 清除缓存</button>
            </div>

            <div id="error-panel" class="error-panel" style="display: none;">
                <div class="error-title">⚠️ 检测到问题</div>
                <div class="error-message" id="error-message">正在诊断...</div>
                <div class="control-group">
                    <label class="control-label">💡 解决方案:</label>
                    <ul class="solution-list" id="solution-list">
                        <li>检查网络连接</li>
                        <li>清除浏览器缓存</li>
                        <li>使用HTTP服务器</li>
                        <li>切换到在线模型</li>
                    </ul>
                </div>
            </div>

            <div class="control-group">
                <label class="control-label">📊 系统状态</label>
                <div id="status-info">
                    <div>THREE.js: <span id="threejs-status">检查中...</span></div>
                    <div>GLTFLoader: <span id="gltf-status">检查中...</span></div>
                    <div>本地服务器: <span id="server-status">检查中...</span></div>
                    <div>模型文件: <span id="model-status">检查中...</span></div>
                </div>
            </div>
        </div>

        <div class="digital-human-viewport">
            <div id="digital-human-container"></div>
            
            <div class="loading-overlay" id="loading-overlay">
                <div class="loading-spinner"></div>
                <div style="font-size: 18px; margin-bottom: 10px;">正在启动企业数字人...</div>
                <div style="font-size: 14px; opacity: 0.8;" id="loading-text">安全模式初始化</div>
            </div>
        </div>
    </div>

    <div class="notification" id="notification"></div>

    <!-- 基础THREE.js库 -->
    <script src="./libs/three.min.js?v=1.0.0"></script>
    <script src="./libs/GLTFLoader.js?v=1.0.0"></script>
    <script src="./libs/tween.min.js?v=1.0.0"></script>

    <script>
        // 安全模式数字人应用
        class SafeModeDigitalHuman {
            constructor() {
                this.scene = null;
                this.camera = null;
                this.renderer = null;
                this.model = null;
                this.container = null;
                this.isInitialized = false;
                this.startupMode = 'safe';
                
                this.init();
            }
            
            init() {
                console.log('🔧 安全模式初始化...');
                this.container = document.getElementById('digital-human-container');
                this.checkSystemStatus();
                this.bindEvents();
            }
            
            checkSystemStatus() {
                const checks = {
                    'threejs-status': () => typeof THREE !== 'undefined',
                    'gltf-status': () => typeof THREE !== 'undefined' && THREE.GLTFLoader,
                    'server-status': () => window.location.protocol === 'http:' || window.location.protocol === 'https:',
                    'model-status': () => true // 将在实际加载时检查
                };
                
                Object.keys(checks).forEach(id => {
                    const element = document.getElementById(id);
                    if (checks[id]()) {
                        element.textContent = '✅ 正常';
                        element.style.color = '#10b981';
                    } else {
                        element.textContent = '❌ 异常';
                        element.style.color = '#ef4444';
                    }
                });
                
                // 检查是否需要显示错误面板
                const hasErrors = !checks['server-status']();
                if (hasErrors) {
                    this.showErrorPanel();
                }
                
                document.getElementById('system-status').textContent = hasErrors ? '需要修复' : '就绪';
            }
            
            showErrorPanel() {
                const errorPanel = document.getElementById('error-panel');
                const errorMessage = document.getElementById('error-message');
                const solutionList = document.getElementById('solution-list');
                
                if (window.location.protocol === 'file:') {
                    errorMessage.textContent = '检测到CORS问题：正在通过file://协议访问，无法加载本地模型文件。';
                    solutionList.innerHTML = `
                        <li>运行本地HTTP服务器（推荐）</li>
                        <li>双击"启动企业数字人.bat"</li>
                        <li>使用Python: python -m http.server 8000</li>
                        <li>切换到在线备用模型</li>
                    `;
                }
                
                errorPanel.style.display = 'block';
            }
            
            bindEvents() {
                document.getElementById('start-btn').addEventListener('click', () => {
                    this.startSystem();
                });
                
                document.getElementById('test-btn').addEventListener('click', () => {
                    this.testConnection();
                });
                
                document.getElementById('clear-cache-btn').addEventListener('click', () => {
                    this.clearCache();
                });
                
                document.getElementById('startup-mode').addEventListener('change', (e) => {
                    this.startupMode = e.target.value;
                });
            }
            
            async startSystem() {
                const modelPath = document.getElementById('model-select').value;
                
                try {
                    this.showLoading('正在初始化3D场景...');
                    
                    // 初始化基础场景
                    this.initScene();
                    this.initCamera();
                    this.initRenderer();
                    this.initLights();
                    
                    this.showLoading('正在加载数字人模型...');
                    
                    // 加载模型
                    await this.loadModel(modelPath);
                    
                    this.showLoading('正在启动渲染循环...');
                    
                    // 开始渲染
                    this.animate();
                    
                    this.hideLoading();
                    this.showNotification('🎉 企业数字人启动成功！', 'success');
                    this.isInitialized = true;
                    
                } catch (error) {
                    console.error('启动失败:', error);
                    this.hideLoading();
                    this.showNotification('❌ 启动失败: ' + error.message, 'error');
                    this.showErrorPanel();
                }
            }
            
            initScene() {
                this.scene = new THREE.Scene();
                this.scene.background = new THREE.Color(0x667eea);
            }
            
            initCamera() {
                this.camera = new THREE.PerspectiveCamera(
                    75,
                    this.container.clientWidth / this.container.clientHeight,
                    0.1,
                    1000
                );
                this.camera.position.set(0, 1.6, 3);
            }
            
            initRenderer() {
                this.renderer = new THREE.WebGLRenderer({ antialias: true });
                this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
                this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
                this.renderer.outputEncoding = THREE.sRGBEncoding;
                this.container.appendChild(this.renderer.domElement);
            }
            
            initLights() {
                const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
                this.scene.add(ambientLight);
                
                const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
                directionalLight.position.set(5, 5, 5);
                this.scene.add(directionalLight);
            }
            
            async loadModel(modelPath) {
                return new Promise((resolve, reject) => {
                    const loader = new THREE.GLTFLoader();
                    
                    loader.load(
                        modelPath,
                        (gltf) => {
                            this.model = gltf.scene;
                            this.scene.add(this.model);
                            
                            // 调整模型位置
                            const box = new THREE.Box3().setFromObject(this.model);
                            const center = box.getCenter(new THREE.Vector3());
                            this.model.position.sub(center);
                            this.model.position.y = 0;
                            
                            console.log('✅ 模型加载成功');
                            resolve(gltf);
                        },
                        (progress) => {
                            const percent = (progress.loaded / progress.total) * 100;
                            this.showLoading(`加载模型中... ${Math.round(percent)}%`);
                        },
                        (error) => {
                            console.error('模型加载失败:', error);
                            reject(new Error('模型加载失败，请检查文件路径或网络连接'));
                        }
                    );
                });
            }
            
            animate() {
                requestAnimationFrame(() => this.animate());
                
                if (this.model) {
                    this.model.rotation.y += 0.005;
                }
                
                this.renderer.render(this.scene, this.camera);
            }
            
            testConnection() {
                const testUrl = './models/business_female_professional.glb';
                
                fetch(testUrl, { method: 'HEAD' })
                    .then(response => {
                        if (response.ok) {
                            this.showNotification('✅ 连接测试成功', 'success');
                        } else {
                            this.showNotification('❌ 文件无法访问', 'error');
                        }
                    })
                    .catch(error => {
                        this.showNotification('❌ 连接测试失败: CORS问题', 'error');
                        this.showErrorPanel();
                    });
            }
            
            clearCache() {
                // 清除THREE.js缓存
                if (THREE.Cache) {
                    THREE.Cache.clear();
                }
                
                // 强制重新加载页面
                if (confirm('确定要清除缓存并重新加载页面吗？')) {
                    window.location.reload(true);
                }
            }
            
            showLoading(text) {
                document.getElementById('loading-overlay').style.display = 'flex';
                document.getElementById('loading-text').textContent = text;
            }
            
            hideLoading() {
                document.getElementById('loading-overlay').style.display = 'none';
            }
            
            showNotification(message, type = 'success') {
                const notification = document.getElementById('notification');
                notification.textContent = message;
                notification.className = `notification ${type}`;
                notification.classList.add('show');
                
                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }
        }

        // 页面加载完成后启动
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🔧 启动安全模式数字人应用...');
            const app = new SafeModeDigitalHuman();
            window.app = app;
        });
    </script>
</body>
</html>