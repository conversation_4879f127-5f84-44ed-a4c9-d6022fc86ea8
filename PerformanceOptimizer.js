/**
 * 企业数字人性能优化器
 * 智能调节PBR渲染质量，确保在各种设备上流畅运行
 */
class PerformanceOptimizer {
    constructor(digitalHuman, pbrSystem, options = {}) {
        this.digitalHuman = digitalHuman;
        this.pbrSystem = pbrSystem;
        this.renderer = digitalHuman.renderer;
        this.scene = digitalHuman.scene;
        
        // 配置选项
        this.options = {
            targetFPS: options.targetFPS || 30,
            maxFPS: options.maxFPS || 60,
            adaptiveInterval: options.adaptiveInterval || 2000, // 2秒检查一次
            qualitySteps: ['low', 'medium', 'high', 'ultra'],
            enableLOD: options.enableLOD !== false,
            enableFrustumCulling: options.enableFrustumCulling !== false,
            enableGeometryInstancing: options.enableGeometryInstancing !== false,
            debug: options.debug || false,
            ...options
        };
        
        // 性能监控数据
        this.performanceMetrics = {
            fps: 60,
            frameTime: 16.67,
            avgFrameTime: 16.67,
            memoryUsage: 0,
            drawCalls: 0,
            triangles: 0,
            currentQuality: 'high',
            frameHistory: [],
            adaptationCount: 0
        };
        
        // LOD系统
        this.lodLevels = new Map();
        this.lodObjects = [];
        
        // 几何体实例化缓存
        this.instancedGeometries = new Map();
        
        // 材质缓存
        this.materialCache = new Map();
        
        // 设备性能评估
        this.deviceCapability = 'unknown';
        
        this.init();
    }
    
    /**
     * 初始化性能优化器
     */
    init() {
        console.log('🚀 初始化性能优化器...');
        
        // 评估设备性能
        this.evaluateDeviceCapability();
        
        // 设置初始质量级别
        this.setInitialQuality();
        
        // 设置LOD系统
        if (this.options.enableLOD) {
            this.setupLODSystem();
        }
        
        // 启动性能监控
        this.startPerformanceMonitoring();
        
        // 设置视锥剔除
        if (this.options.enableFrustumCulling) {
            this.setupFrustumCulling();
        }
        
        console.log('✅ 性能优化器初始化完成');
        console.log(`📊 设备性能等级: ${this.deviceCapability}`);
        console.log(`🎚️ 初始质量设置: ${this.performanceMetrics.currentQuality}`);
    }
    
    /**
     * 评估设备性能
     */
    evaluateDeviceCapability() {
        const canvas = this.renderer.domElement;
        const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
        
        if (!gl) {
            this.deviceCapability = 'low';
            return;
        }
        
        // 获取GPU信息
        const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
        const renderer = debugInfo ? 
            gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL) : 'Unknown';
        
        // 检测内存
        const memory = navigator.deviceMemory || 4; // GB，默认4GB
        
        // 检测CPU核心数
        const cores = navigator.hardwareConcurrency || 4;
        
        // 检测像素比
        const pixelRatio = window.devicePixelRatio || 1;
        
        // 检测屏幕分辨率
        const screenArea = window.screen.width * window.screen.height;
        
        console.log('🔍 设备性能评估:');
        console.log(`- GPU: ${renderer}`);
        console.log(`- 内存: ${memory}GB`);
        console.log(`- CPU核心: ${cores}`);
        console.log(`- 像素比: ${pixelRatio}`);
        console.log(`- 屏幕像素: ${screenArea}`);
        
        // 综合评分
        let score = 0;
        
        // GPU评分
        if (renderer.toLowerCase().includes('nvidia') || 
            renderer.toLowerCase().includes('amd') ||
            renderer.toLowerCase().includes('intel iris')) {
            score += 30;
        } else if (renderer.toLowerCase().includes('intel')) {
            score += 15;
        } else {
            score += 10;
        }
        
        // 内存评分
        if (memory >= 8) score += 25;
        else if (memory >= 4) score += 15;
        else score += 5;
        
        // CPU评分
        if (cores >= 8) score += 20;
        else if (cores >= 4) score += 15;
        else score += 8;
        
        // 分辨率惩罚
        if (screenArea > 2073600) score -= 15; // > 1920x1080
        if (pixelRatio > 2) score -= 10;
        
        // 移动设备检测
        if (/Mobi|Android/i.test(navigator.userAgent)) {
            score -= 20;
        }
        
        // 确定设备级别
        if (score >= 60) {
            this.deviceCapability = 'high';
        } else if (score >= 40) {
            this.deviceCapability = 'medium';
        } else {
            this.deviceCapability = 'low';
        }
        
        console.log(`📊 设备性能评分: ${score}/85`);
        console.log(`🏷️ 设备性能等级: ${this.deviceCapability}`);
    }
    
    /**
     * 设置初始质量级别
     */
    setInitialQuality() {
        const qualityMap = {
            'low': 'low',
            'medium': 'medium',
            'high': 'high'
        };
        
        const initialQuality = qualityMap[this.deviceCapability] || 'medium';
        this.setQualityLevel(initialQuality);
        
        console.log(`🎚️ 初始质量级别: ${initialQuality}`);
    }
    
    /**
     * 设置LOD系统
     */
    setupLODSystem() {
        console.log('🔄 设置LOD系统...');
        
        // 为模型创建LOD级别
        if (this.digitalHuman.model) {
            this.createLODForModel(this.digitalHuman.model);
        }
        
        // 监听模型加载事件
        this.digitalHuman.on('modelLoaded', (data) => {
            this.createLODForModel(data.model);
        });
    }
    
    /**
     * 为模型创建LOD级别
     */
    createLODForModel(model) {
        if (!model) return;
        
        console.log('🔄 为模型创建LOD级别...');
        
        model.traverse((child) => {
            if (child.isMesh && child.geometry) {
                this.createLODForMesh(child);
            }
        });
    }
    
    /**
     * 为网格创建LOD级别
     */
    createLODForMesh(mesh) {
        const originalGeometry = mesh.geometry;
        const originalMaterial = mesh.material;
        
        // 创建不同LOD级别的几何体
        const lodGeometries = {
            high: originalGeometry,
            medium: this.simplifyGeometry(originalGeometry, 0.7),
            low: this.simplifyGeometry(originalGeometry, 0.4)
        };
        
        // 创建不同LOD级别的材质
        const lodMaterials = {
            high: originalMaterial,
            medium: this.simplifyMaterial(originalMaterial, 'medium'),
            low: this.simplifyMaterial(originalMaterial, 'low')
        };
        
        // 缓存LOD数据
        this.lodLevels.set(mesh.uuid, {
            geometries: lodGeometries,
            materials: lodMaterials,
            originalMesh: mesh
        });
        
        this.lodObjects.push(mesh);
        
        console.log(`✅ 为网格 ${mesh.name || 'unnamed'} 创建LOD级别`);
    }
    
    /**
     * 简化几何体
     */
    simplifyGeometry(geometry, ratio) {
        // 简单的几何体简化（实际项目中可以使用更复杂的算法）
        const simplified = geometry.clone();
        
        if (simplified.index) {
            // 减少索引数量
            const originalIndices = simplified.index.array;
            const newLength = Math.floor(originalIndices.length * ratio);
            const newIndices = new Uint32Array(newLength);
            
            for (let i = 0; i < newLength; i += 3) {
                // 确保三角形完整
                const baseIndex = Math.floor(i / 3) * 3;
                if (baseIndex + 2 < originalIndices.length) {
                    newIndices[i] = originalIndices[baseIndex];
                    newIndices[i + 1] = originalIndices[baseIndex + 1];
                    newIndices[i + 2] = originalIndices[baseIndex + 2];
                }
            }
            
            simplified.setIndex(new THREE.BufferAttribute(newIndices, 1));
        }
        
        return simplified;
    }
    
    /**
     * 简化材质
     */
    simplifyMaterial(material, quality) {
        const cacheKey = `${material.uuid}_${quality}`;
        
        if (this.materialCache.has(cacheKey)) {
            return this.materialCache.get(cacheKey);
        }
        
        let simplifiedMaterial;
        
        if (quality === 'low') {
            // 低质量：使用基础材质
            simplifiedMaterial = new THREE.MeshLambertMaterial({
                color: material.color || 0xffffff,
                map: material.map || null,
                transparent: material.transparent || false,
                opacity: material.opacity || 1.0
            });
        } else if (quality === 'medium') {
            // 中等质量：使用标准材质但减少特性
            simplifiedMaterial = new THREE.MeshStandardMaterial({
                color: material.color || 0xffffff,
                map: material.map || null,
                roughness: material.roughness || 0.8,
                metalness: material.metalness || 0.0,
                transparent: material.transparent || false,
                opacity: material.opacity || 1.0,
                // 移除法线贴图等复杂特性
                normalMap: null,
                roughnessMap: null,
                metalnessMap: null
            });
        } else {
            // 高质量：保持原材质
            simplifiedMaterial = material;
        }
        
        this.materialCache.set(cacheKey, simplifiedMaterial);
        return simplifiedMaterial;
    }
    
    /**
     * 启动性能监控
     */
    startPerformanceMonitoring() {
        let frameCount = 0;
        let lastTime = performance.now();
        let frameTimeSum = 0;
        
        const monitor = () => {
            const currentTime = performance.now();
            const deltaTime = currentTime - lastTime;
            
            frameCount++;
            frameTimeSum += deltaTime;
            
            // 每秒更新一次FPS
            if (deltaTime >= 1000) {
                const fps = Math.round((frameCount * 1000) / deltaTime);
                const avgFrameTime = frameTimeSum / frameCount;
                
                this.updatePerformanceMetrics(fps, avgFrameTime);
                
                frameCount = 0;
                frameTimeSum = 0;
                lastTime = currentTime;
            }
            
            // 检查是否需要适应性调整
            if (currentTime - this.lastAdaptiveCheck > this.options.adaptiveInterval) {
                this.adaptiveQualityAdjustment();
                this.lastAdaptiveCheck = currentTime;
            }
            
            requestAnimationFrame(monitor);
        };
        
        this.lastAdaptiveCheck = performance.now();
        monitor();
        
        console.log('📊 性能监控已启动');
    }
    
    /**
     * 更新性能指标
     */
    updatePerformanceMetrics(fps, frameTime) {
        this.performanceMetrics.fps = fps;
        this.performanceMetrics.frameTime = frameTime;
        
        // 更新平均帧时间
        this.performanceMetrics.avgFrameTime = 
            (this.performanceMetrics.avgFrameTime * 0.9) + (frameTime * 0.1);
        
        // 记录帧率历史
        this.performanceMetrics.frameHistory.push(fps);
        if (this.performanceMetrics.frameHistory.length > 10) {
            this.performanceMetrics.frameHistory.shift();
        }
        
        // 更新渲染统计
        this.updateRenderingStats();
        
        if (this.options.debug) {
            console.log(`📊 FPS: ${fps}, 帧时间: ${frameTime.toFixed(2)}ms`);
        }
    }
    
    /**
     * 更新渲染统计
     */
    updateRenderingStats() {
        if (this.renderer.info) {
            this.performanceMetrics.drawCalls = this.renderer.info.render.calls;
            this.performanceMetrics.triangles = this.renderer.info.render.triangles;
        }
        
        // 估算内存使用
        if (performance.memory) {
            this.performanceMetrics.memoryUsage = 
                Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
        }
    }
    
    /**
     * 自适应质量调整
     */
    adaptiveQualityAdjustment() {
        const avgFPS = this.getAverageFPS();
        const currentQuality = this.performanceMetrics.currentQuality;
        const qualityIndex = this.options.qualitySteps.indexOf(currentQuality);
        
        let newQuality = currentQuality;
        
        // 如果FPS太低，降低质量
        if (avgFPS < this.options.targetFPS * 0.8) {
            if (qualityIndex > 0) {
                newQuality = this.options.qualitySteps[qualityIndex - 1];
                console.log(`⚡ 性能不足，降低质量: ${currentQuality} -> ${newQuality}`);
            }
        }
        // 如果FPS充足，可以提升质量
        else if (avgFPS > this.options.maxFPS * 0.9) {
            if (qualityIndex < this.options.qualitySteps.length - 1) {
                newQuality = this.options.qualitySteps[qualityIndex + 1];
                console.log(`⚡ 性能充足，提升质量: ${currentQuality} -> ${newQuality}`);
            }
        }
        
        if (newQuality !== currentQuality) {
            this.setQualityLevel(newQuality);
            this.performanceMetrics.adaptationCount++;
        }
    }
    
    /**
     * 获取平均FPS
     */
    getAverageFPS() {
        const history = this.performanceMetrics.frameHistory;
        if (history.length === 0) return 60;
        
        const sum = history.reduce((a, b) => a + b, 0);
        return sum / history.length;
    }
    
    /**
     * 设置质量级别
     */
    setQualityLevel(quality) {
        if (!this.options.qualitySteps.includes(quality)) {
            console.warn(`⚠️ 无效的质量级别: ${quality}`);
            return;
        }
        
        this.performanceMetrics.currentQuality = quality;
        
        // 调整渲染器设置
        this.adjustRendererQuality(quality);
        
        // 调整PBR系统质量
        if (this.pbrSystem && this.pbrSystem.setQualityLevel) {
            this.pbrSystem.setQualityLevel(quality);
        }
        
        // 应用LOD级别
        this.applyLODLevel(quality);
        
        console.log(`🎚️ 质量级别设置为: ${quality}`);
    }
    
    /**
     * 调整渲染器质量
     */
    adjustRendererQuality(quality) {
        switch (quality) {
            case 'low':
                this.renderer.setPixelRatio(Math.min(1, window.devicePixelRatio));
                this.renderer.shadowMap.enabled = false;
                break;
                
            case 'medium':
                this.renderer.setPixelRatio(Math.min(1.5, window.devicePixelRatio));
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.BasicShadowMap;
                break;
                
            case 'high':
                this.renderer.setPixelRatio(Math.min(2, window.devicePixelRatio));
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFShadowMap;
                break;
                
            case 'ultra':
                this.renderer.setPixelRatio(window.devicePixelRatio);
                this.renderer.shadowMap.enabled = true;
                this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
                break;
        }
    }
    
    /**
     * 应用LOD级别
     */
    applyLODLevel(quality) {
        if (!this.options.enableLOD) return;
        
        this.lodObjects.forEach(mesh => {
            const lodData = this.lodLevels.get(mesh.uuid);
            if (!lodData) return;
            
            const lodQuality = this.mapQualityToLOD(quality);
            
            // 更换几何体
            if (lodData.geometries[lodQuality]) {
                mesh.geometry = lodData.geometries[lodQuality];
            }
            
            // 更换材质
            if (lodData.materials[lodQuality]) {
                mesh.material = lodData.materials[lodQuality];
            }
        });
        
        console.log(`🔄 应用LOD级别: ${quality}`);
    }
    
    /**
     * 将质量级别映射到LOD级别
     */
    mapQualityToLOD(quality) {
        const lodMap = {
            'low': 'low',
            'medium': 'medium',
            'high': 'high',
            'ultra': 'high'
        };
        
        return lodMap[quality] || 'medium';
    }
    
    /**
     * 设置视锥剔除
     */
    setupFrustumCulling() {
        if (!this.digitalHuman.camera) return;
        
        const camera = this.digitalHuman.camera;
        const frustum = new THREE.Frustum();
        const matrix = new THREE.Matrix4();
        
        // 在渲染循环中进行视锥剔除
        const originalRender = this.renderer.render.bind(this.renderer);
        
        this.renderer.render = (scene, camera) => {
            // 更新视锥体
            matrix.multiplyMatrices(camera.projectionMatrix, camera.matrixWorldInverse);
            frustum.setFromProjectionMatrix(matrix);
            
            // 遍历场景对象进行剔除
            scene.traverse((object) => {
                if (object.isMesh) {
                    // 检查对象是否在视锥体内
                    object.visible = frustum.intersectsObject(object);
                }
            });
            
            // 调用原始渲染方法
            originalRender(scene, camera);
        };
        
        console.log('✅ 视锥剔除已启用');
    }
    
    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        return {
            device: {
                capability: this.deviceCapability,
                userAgent: navigator.userAgent,
                memory: navigator.deviceMemory || 'unknown',
                cores: navigator.hardwareConcurrency || 'unknown'
            },
            metrics: {
                ...this.performanceMetrics,
                adaptationCount: this.performanceMetrics.adaptationCount
            },
            settings: {
                targetFPS: this.options.targetFPS,
                currentQuality: this.performanceMetrics.currentQuality,
                lodEnabled: this.options.enableLOD,
                frustumCullingEnabled: this.options.enableFrustumCulling
            }
        };
    }
    
    /**
     * 手动触发性能优化
     */
    optimizeNow() {
        console.log('🚀 手动触发性能优化...');
        
        // 清理未使用的材质和几何体
        this.cleanupUnusedResources();
        
        // 强制垃圾回收（如果支持）
        if (window.gc) {
            window.gc();
        }
        
        // 重新评估性能
        this.adaptiveQualityAdjustment();
        
        console.log('✅ 性能优化完成');
    }
    
    /**
     * 清理未使用的资源
     */
    cleanupUnusedResources() {
        // 清理材质缓存中未使用的材质
        this.materialCache.forEach((material, key) => {
            if (material.userData && material.userData.lastUsed) {
                const timeSinceLastUse = Date.now() - material.userData.lastUsed;
                if (timeSinceLastUse > 60000) { // 1分钟未使用
                    material.dispose();
                    this.materialCache.delete(key);
                }
            }
        });
        
        console.log('🧹 未使用资源清理完成');
    }
    
    /**
     * 销毁优化器
     */
    destroy() {
        // 清理LOD数据
        this.lodLevels.clear();
        this.lodObjects.length = 0;
        
        // 清理材质缓存
        this.materialCache.forEach(material => {
            material.dispose();
        });
        this.materialCache.clear();
        
        // 清理实例化几何体
        this.instancedGeometries.clear();
        
        console.log('🔄 性能优化器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PerformanceOptimizer;
} else {
    window.PerformanceOptimizer = PerformanceOptimizer;
}