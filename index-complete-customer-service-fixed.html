<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逼真企业客服数字人 - 修复版</title>
    <style>
        :root {
            --primary-color: #1e3a8a;
            --secondary-color: #3b82f6;
            --accent-color: #ef4444;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --background-color: #f8fafc;
            --card-background: #ffffff;
            --text-primary: #1f2937;
            --text-secondary: #6b7280;
            --border-color: #e5e7eb;
            --shadow: 0 4px 20px rgba(0,0,0,0.08);
            --border-radius: 12px;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: var(--text-primary);
            overflow: hidden;
            height: 100vh;
        }

        .enterprise-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            height: 70px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow);
            z-index: 1000;
            display: flex;
            align-items: center;
            padding: 0 30px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, var(--secondary-color), var(--primary-color));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 20px;
        }

        .logo-text {
            font-size: 24px;
            font-weight: 700;
            color: var(--primary-color);
        }

        .header-controls {
            margin-left: auto;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .status-badge {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
            font-size: 14px;
            font-weight: 600;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.1); }
        }

        .main-container {
            position: absolute;
            top: 70px;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
        }

        .control-panel {
            width: 360px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-right: 1px solid var(--border-color);
            box-shadow: var(--shadow);
            overflow-y: auto;
            z-index: 100;
        }

        .panel-section {
            padding: 25px;
            border-bottom: 1px solid var(--border-color);
        }

        .section-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 20px;
            color: var(--primary-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            min-width: 120px;
        }

        .btn-primary {
            background: var(--secondary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
        }

        .btn-success {
            background: var(--success-color);
            color: white;
        }

        .btn-warning {
            background: var(--warning-color);
            color: white;
        }

        .btn-secondary {
            background: var(--text-secondary);
            color: white;
        }

        .btn-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .digital-human-viewport {
            flex: 1;
            position: relative;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        #digital-human-container {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            z-index: 1000;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-bar {
            width: 300px;
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            overflow: hidden;
            margin-top: 20px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
            width: 0%;
            transition: width 0.3s ease;
        }

        .error-panel {
            background: rgba(239, 68, 68, 0.1);
            border: 2px solid var(--accent-color);
            border-radius: var(--border-radius);
            padding: 20px;
            margin: 20px;
            color: var(--accent-color);
        }

        .error-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .error-message {
            margin-bottom: 15px;
            font-family: monospace;
            background: rgba(0,0,0,0.1);
            padding: 10px;
            border-radius: 5px;
        }

        .recovery-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .control-select {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            font-size: 14px;
            transition: all 0.3s ease;
            background: white;
        }

        .notification {
            position: fixed;
            top: 90px;
            right: 30px;
            padding: 15px 25px;
            border-radius: var(--border-radius);
            color: white;
            font-weight: 600;
            z-index: 10000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 400px;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.error {
            background: var(--accent-color);
        }

        .notification.warning {
            background: var(--warning-color);
        }
    </style>
</head>
<body>
    <!-- 企业级顶部导航 -->
    <header class="enterprise-header">
        <div class="logo-section">
            <div class="logo">🏢</div>
            <div class="logo-text">企业智能客服数字人 - 修复版</div>
        </div>
        
        <div class="header-controls">
            <div class="status-badge">
                <div class="status-dot"></div>
                <span id="system-status">系统就绪</span>
            </div>
            <button class="btn btn-warning" id="start-server-btn">启动服务器</button>
            <button class="btn btn-secondary" id="diagnostics-btn">系统诊断</button>
            <button class="btn btn-primary" id="reset-btn">重置系统</button>
        </div>
    </header>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <!-- 控制面板 -->
        <aside class="control-panel" id="control-panel">
            <!-- 服务器状态检测 -->
            <section class="panel-section">
                <h3 class="section-title">🔧 系统状态</h3>
                
                <div id="server-status"></div>
                
                <div class="btn-group">
                    <button class="btn btn-primary" id="check-server-btn">检查服务器</button>
                    <button class="btn btn-success" id="test-models-btn">测试模型</button>
                </div>
            </section>

            <!-- 模型选择增强版 -->
            <section class="panel-section">
                <h3 class="section-title">👤 数字人模型</h3>
                
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600;">选择模型</label>
                    <select class="control-select" id="model-select">
                        <option value="business_female">专业企业客服（女性）- 推荐</option>
                        <option value="business_male">专业企业客服（男性）</option>
                        <option value="RiggedSimple">基础模型（兼容性最好）</option>
                        <option value="RiggedFigure">进阶模型</option>
                        <option value="RiggedSimple_updated">更新版基础模型</option>
                    </select>
                </div>
                
                <div style="margin-bottom: 15px;">
                    <label style="display: block; margin-bottom: 8px; font-weight: 600;">当前路径</label>
                    <input type="text" id="model-path" class="control-select" 
                           value="./models/business_female.glb" readonly>
                </div>
                
                <div class="btn-group">
                    <button class="btn btn-primary" id="load-model-btn">加载模型</button>
                    <button class="btn btn-warning" id="try-fallback-btn">尝试备用</button>
                </div>
            </section>

            <!-- 诊断信息 -->
            <section class="panel-section">
                <h3 class="section-title">📊 诊断信息</h3>
                
                <div id="diagnostic-info">
                    <div style="margin-bottom: 10px;">
                        <strong>浏览器支持:</strong> <span id="browser-support">检测中...</span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>Three.js:</strong> <span id="threejs-status">检测中...</span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>GLTFLoader:</strong> <span id="gltfloader-status">检测中...</span>
                    </div>
                    <div style="margin-bottom: 10px;">
                        <strong>模型文件:</strong> <span id="model-file-status">检测中...</span>
                    </div>
                </div>
            </section>
        </aside>

        <!-- 3D渲染区域 -->
        <main class="digital-human-viewport">
            <div id="digital-human-container"></div>
            
            <!-- 加载动画 -->
            <div class="loading-overlay" id="loading-overlay">
                <div class="loading-spinner"></div>
                <div style="font-size: 18px; margin-bottom: 10px;" id="loading-title">正在初始化企业客服数字人...</div>
                <div style="font-size: 14px; opacity: 0.8;" id="loading-subtitle">加载3D模型、情感系统和AI引擎</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="loading-progress"></div>
                </div>
                <div id="loading-text">0%</div>
            </div>
            
            <!-- 错误恢复面板 -->
            <div class="error-panel" id="error-panel" style="display: none;">
                <div class="error-title">🚨 系统初始化失败</div>
                <div class="error-message" id="error-message">检测到模型加载错误</div>
                <div class="recovery-actions">
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                    <button class="btn btn-warning" id="try-simple-model">尝试简化模型</button>
                    <button class="btn btn-secondary" id="open-diagnostics">打开诊断工具</button>
                </div>
            </div>
        </main>
    </div>

    <!-- 通知系统 -->
    <div class="notification" id="notification"></div>

    <!-- Three.js 和相关库 -->
    <script src="./libs/three.min.js"></script>
    <script src="./libs/GLTFLoader.js"></script>
    <script src="./libs/tween.min.js"></script>
    
    <!-- 数字人核心系统 -->
    <script src="./AdvancedFacialExpressionSystem.js"></script>
    <script src="./VoiceAdapter-simple.js"></script>
    <script src="./EnterpriseDigitalHuman.js"></script>
    
    <!-- 企业客服增强系统 -->
    <script src="./EnterpriseCustomerService.js"></script>
    <script src="./RealisticAppearanceEnhancer.js"></script>
    <script src="./EnhancedEmotionSystem.js"></script>
    
    <script>
        // 全局应用实例
        let app = null;
        
        // 模型路径映射
        const modelPaths = {
            'business_female': './models/business_female.glb',
            'business_male': './models/business_male.glb',
            'RiggedSimple': './models/RiggedSimple.glb',
            'RiggedFigure': './models/RiggedFigure.glb',
            'RiggedSimple_updated': './models/RiggedSimple_updated.glb'
        };
        
        // 降级顺序（从高级到基础）
        const fallbackOrder = [
            'business_female',
            'business_male', 
            'RiggedSimple_updated',
            'RiggedSimple',
            'RiggedFigure'
        ];
        
        // 修复版企业客服数字人应用
        class FixedCustomerServiceApp {
            constructor() {
                this.digitalHuman = null;
                this.isInitialized = false;
                this.currentModelIndex = 0;
                this.initializationAttempts = 0;
                this.maxAttempts = 3;
                
                this.init();
            }
            
            async init() {
                console.log('🔧 启动修复版企业客服数字人系统...');
                
                try {
                    // 1. 运行系统诊断
                    await this.runDiagnostics();
                    
                    // 2. 检查服务器状态
                    await this.checkServerStatus();
                    
                    // 3. 初始化事件监听
                    this.bindEvents();
                    
                    // 4. 尝试加载模型
                    await this.attemptModelLoading();
                    
                } catch (error) {
                    console.error('系统初始化失败:', error);
                    this.handleCriticalError(error);
                }
            }
            
            async runDiagnostics() {
                console.log('🔍 运行系统诊断...');
                
                // 检查浏览器支持
                const canvas = document.createElement('canvas');
                const webgl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
                document.getElementById('browser-support').textContent = webgl ? '✅ 支持WebGL' : '❌ 不支持WebGL';
                
                // 检查Three.js
                const threejsStatus = typeof THREE !== 'undefined' ? `✅ 已加载 (r${THREE.REVISION})` : '❌ 未加载';
                document.getElementById('threejs-status').textContent = threejsStatus;
                
                // 检查GLTFLoader
                const gltfStatus = typeof THREE.GLTFLoader !== 'undefined' ? '✅ 已加载' : '❌ 未加载';
                document.getElementById('gltfloader-status').textContent = gltfStatus;
                
                // 基础诊断完成
                this.updateStatus('诊断完成');
            }
            
            async checkServerStatus() {
                console.log('🌐 检查服务器状态...');
                
                try {
                    // 尝试访问模型文件来检查服务器
                    const response = await fetch('./models/RiggedSimple.glb', { method: 'HEAD' });
                    
                    if (response.ok) {
                        this.showServerStatus('✅ HTTP服务器运行正常', 'success');
                        return true;
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    this.showServerStatus(`❌ 服务器访问失败: ${error.message}`, 'error');
                    this.showNotification('请启动HTTP服务器，双击 start-http-server.bat', 'warning');
                    return false;
                }
            }
            
            showServerStatus(message, type) {
                const statusDiv = document.getElementById('server-status');
                statusDiv.innerHTML = `<div class="status ${type}" style="margin-bottom: 10px;">${message}</div>`;
            }
            
            async attemptModelLoading() {
                console.log('🎯 尝试加载模型...');
                
                // 从选择的模型开始
                const selectedModel = document.getElementById('model-select').value;
                const startIndex = fallbackOrder.indexOf(selectedModel);
                
                for (let i = startIndex >= 0 ? startIndex : 0; i < fallbackOrder.length; i++) {
                    const modelKey = fallbackOrder[i];
                    const modelPath = modelPaths[modelKey];
                    
                    try {
                        this.updateLoadingProgress(0, `尝试加载 ${modelKey}...`);
                        
                        // 先检查文件是否存在
                        const fileExists = await this.checkModelFile(modelPath);
                        if (!fileExists) {
                            console.warn(`模型文件不存在: ${modelPath}`);
                            continue;
                        }
                        
                        // 尝试加载模型
                        await this.loadDigitalHuman(modelPath);
                        
                        // 如果成功，跳出循环
                        console.log(`✅ 模型加载成功: ${modelKey}`);
                        this.showNotification(`模型加载成功: ${modelKey}`, 'success');
                        break;
                        
                    } catch (error) {
                        console.warn(`模型加载失败 ${modelKey}:`, error);
                        
                        if (i === fallbackOrder.length - 1) {
                            // 所有模型都失败了
                            throw new Error('所有模型加载失败');
                        }
                        
                        // 继续尝试下一个模型
                        this.showNotification(`${modelKey} 加载失败，尝试备用模型...`, 'warning');
                    }
                }
            }
            
            async checkModelFile(path) {
                try {
                    const response = await fetch(path, { method: 'HEAD' });
                    const exists = response.ok;
                    document.getElementById('model-file-status').textContent = 
                        exists ? '✅ 文件可访问' : '❌ 文件无法访问';
                    return exists;
                } catch (error) {
                    document.getElementById('model-file-status').textContent = '❌ 网络错误';
                    return false;
                }
            }
            
            async loadDigitalHuman(modelPath) {
                return new Promise((resolve, reject) => {
                    const container = document.getElementById('digital-human-container');
                    
                    // 清理之前的实例
                    if (this.digitalHuman) {
                        this.digitalHuman.destroy();
                        this.digitalHuman = null;
                    }
                    
                    this.digitalHuman = new EnterpriseDigitalHuman(container, {
                        width: container.clientWidth,
                        height: container.clientHeight,
                        modelPath: modelPath,
                        enableAudio: true,
                        enableControls: true,
                        
                        onProgress: (progress) => {
                            this.updateLoadingProgress(progress, '加载3D模型中...');
                        },
                        
                        onLoaded: () => {
                            console.log('✅ 数字人模型加载完成');
                            this.onModelLoadComplete();
                            resolve();
                        },
                        
                        onError: (error) => {
                            console.error('❌ 数字人加载失败:', error);
                            reject(new Error(error));
                        }
                    });
                });
            }
            
            onModelLoadComplete() {
                // 隐藏加载界面
                this.hideLoading();
                
                // 标记为已初始化
                this.isInitialized = true;
                this.updateStatus('运行中');
                
                // 启动欢迎序列
                this.startWelcomeSequence();
                
                console.log('🎉 企业客服数字人系统初始化完成！');
                this.showNotification('🎉 企业客服数字人已就绪！', 'success');
            }
            
            startWelcomeSequence() {
                // 播放欢迎动画或语音
                if (this.digitalHuman && this.digitalHuman.speak) {
                    setTimeout(() => {
                        this.digitalHuman.speak('您好，我是您的专属AI客服助手，很高兴为您服务！');
                    }, 1000);
                }
            }
            
            bindEvents() {
                // 模型选择
                document.getElementById('model-select').addEventListener('change', (e) => {
                    const selectedModel = e.target.value;
                    const path = modelPaths[selectedModel];
                    document.getElementById('model-path').value = path;
                });
                
                // 手动加载模型
                document.getElementById('load-model-btn').addEventListener('click', () => {
                    this.manualLoadModel();
                });
                
                // 尝试备用模型
                document.getElementById('try-fallback-btn').addEventListener('click', () => {
                    this.tryFallbackModel();
                });
                
                // 启动服务器
                document.getElementById('start-server-btn').addEventListener('click', () => {
                    this.showStartServerInstructions();
                });
                
                // 系统诊断
                document.getElementById('diagnostics-btn').addEventListener('click', () => {
                    this.openDiagnostics();
                });
                
                // 检查服务器
                document.getElementById('check-server-btn').addEventListener('click', () => {
                    this.checkServerStatus();
                });
                
                // 测试模型
                document.getElementById('test-models-btn').addEventListener('click', () => {
                    this.testAllModels();
                });
                
                // 错误恢复
                document.getElementById('try-simple-model').addEventListener('click', () => {
                    this.loadSimpleModel();
                });
                
                document.getElementById('open-diagnostics').addEventListener('click', () => {
                    this.openDiagnostics();
                });
                
                // 重置系统
                document.getElementById('reset-btn').addEventListener('click', () => {
                    if (confirm('确定要重置整个系统吗？')) {
                        location.reload();
                    }
                });
            }
            
            async manualLoadModel() {
                const selectedModel = document.getElementById('model-select').value;
                const modelPath = modelPaths[selectedModel];
                
                try {
                    this.showLoading();
                    await this.loadDigitalHuman(modelPath);
                } catch (error) {
                    this.showNotification('模型加载失败: ' + error.message, 'error');
                    this.hideLoading();
                }
            }
            
            async tryFallbackModel() {
                this.currentModelIndex++;
                if (this.currentModelIndex >= fallbackOrder.length) {
                    this.currentModelIndex = 0;
                }
                
                const modelKey = fallbackOrder[this.currentModelIndex];
                const modelPath = modelPaths[modelKey];
                
                try {
                    this.showLoading();
                    await this.loadDigitalHuman(modelPath);
                    document.getElementById('model-select').value = modelKey;
                } catch (error) {
                    this.showNotification('备用模型加载失败', 'error');
                    this.hideLoading();
                }
            }
            
            async loadSimpleModel() {
                try {
                    this.hideError();
                    this.showLoading();
                    await this.loadDigitalHuman('./models/RiggedSimple.glb');
                } catch (error) {
                    this.showNotification('简化模型也无法加载', 'error');
                    this.hideLoading();
                }
            }
            
            async testAllModels() {
                this.showNotification('开始测试所有模型...', 'info');
                
                for (const [key, path] of Object.entries(modelPaths)) {
                    try {
                        const response = await fetch(path, { method: 'HEAD' });
                        const status = response.ok ? '✅' : '❌';
                        console.log(`${status} ${key}: ${path}`);
                    } catch (error) {
                        console.log(`❌ ${key}: ${path} - ${error.message}`);
                    }
                }
                
                this.showNotification('模型测试完成，请查看控制台', 'success');
            }
            
            showStartServerInstructions() {
                const message = `
请按以下步骤启动HTTP服务器：

1. 双击 "start-http-server.bat" 文件
2. 或在命令行运行: python -m http.server 8000
3. 然后访问: http://localhost:8000/index-complete-customer-service-fixed.html

当前正在使用 file:// 协议，需要HTTP服务器来加载3D模型。
                `.trim();
                
                alert(message);
            }
            
            openDiagnostics() {
                window.open('./test-model-loading-debug.html', '_blank');
            }
            
            handleCriticalError(error) {
                console.error('严重错误:', error);
                this.hideLoading();
                this.showError('系统初始化失败: ' + error.message);
            }
            
            // UI更新方法
            updateLoadingProgress(progress, text = '') {
                document.getElementById('loading-progress').style.width = progress + '%';
                document.getElementById('loading-text').textContent = Math.round(progress) + '%';
                
                if (text) {
                    document.getElementById('loading-subtitle').textContent = text;
                }
            }
            
            showLoading() {
                document.getElementById('loading-overlay').style.display = 'flex';
            }
            
            hideLoading() {
                document.getElementById('loading-overlay').style.display = 'none';
            }
            
            showError(message) {
                document.getElementById('error-message').textContent = message;
                document.getElementById('error-panel').style.display = 'block';
            }
            
            hideError() {
                document.getElementById('error-panel').style.display = 'none';
            }
            
            updateStatus(status) {
                document.getElementById('system-status').textContent = status;
            }
            
            showNotification(message, type = 'success') {
                const notification = document.getElementById('notification');
                notification.textContent = message;
                notification.className = `notification ${type}`;
                notification.classList.add('show');
                
                setTimeout(() => {
                    notification.classList.remove('show');
                }, 3000);
            }
        }
        
        // 页面加载完成后启动应用
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🚀 启动修复版企业客服数字人应用...');
            
            setTimeout(() => {
                app = new FixedCustomerServiceApp();
                window.app = app; // 全局访问
            }, 500);
        });
        
        // 页面卸载时清理资源
        window.addEventListener('beforeunload', () => {
            if (app && app.digitalHuman) {
                app.digitalHuman.destroy();
            }
        });
    </script>
</body>
</html>